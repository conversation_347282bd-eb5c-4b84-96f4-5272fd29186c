import{r,c as m,o as c,a as s,b as a,d as v,u as p,s as l}from"./index-GQNKmeFm.js";import{A as f}from"./AppLogo-BaVronrj.js";import{B as _}from"./BrandFooter-CIZu14VB.js";import{_ as T}from"./_plugin-vue_export-helper-DlAUqK2U.js";const g={class:"home"},b={class:"content"},h={class:"header"},k={class:"cards"},B={class:"card-decoration"},C={class:"usage-tip"},z={__name:"Home",setup(x){const i=p();r([]);const e=r(0),t=r(null),d=()=>{i.push("/multi-form")},u=()=>{if(e.value>=5){e.value=0,l({message:"进入管理模式",type:"success"}),i.push("/admin");return}t.value&&clearTimeout(t.value),t.value=setTimeout(()=>{e.value=0},3e3),e.value===3&&l("再点击2次进入管理模式")};return(A,o)=>{const n=v("van-icon");return c(),m("div",g,[s("div",b,[s("div",h,[a(f,{size:"large"}),o[1]||(o[1]=s("h1",null,"阿泰会议服务",-1)),o[2]||(o[2]=s("p",{class:"header-subtitle"},"快速提交会议室服务需求",-1))]),s("div",k,[s("div",{class:"card main-card",onClick:d},[a(n,{name:"service-o",size:"32",color:"#1989fa"}),o[3]||(o[3]=s("h3",null,"服务需求提交",-1)),o[4]||(o[4]=s("p",null,"支持单项或多项服务需求提交",-1)),s("div",B,[a(n,{name:"arrow",size:"16",color:"#1989fa"})]),o[5]||(o[5]=s("div",{class:"feature-badge"}," 推荐 ",-1))])]),s("div",C,[a(n,{name:"info-o",size:"16",color:"rgba(255,255,255,0.8)"}),o[6]||(o[6]=s("span",null,"选择会议室，提交服务需求，工作人员将及时处理",-1))]),s("div",{class:"admin-entry",onClick:o[0]||(o[0]=w=>e.value++),onTouchstart:u},null,32),a(_,{variant:"default"})])])}}},V=T(z,[["__scopeId","data-v-b133cf47"]]);export{V as default};
