import{_ as t}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as o,o as r,a as n,y as d}from"./index-GQNKmeFm.js";const l={__name:"BrandFooter",props:{variant:{type:String,default:"default",validator:a=>["default","compact","minimal"].includes(a)},fixed:{type:Boolean,default:!1},lightBackground:{type:Boolean,default:!1}},setup(a){return(s,e)=>(r(),o("div",{class:d(["brand-footer",[`brand-footer--${a.variant}`,{"brand-footer--fixed":a.fixed,"brand-footer--light-bg":a.lightBackground}]])},e[0]||(e[0]=[n("div",{class:"brand-text"},"恒泰信息",-1)]),2))}},c=t(l,[["__scopeId","data-v-52c8f03c"]]);export{c as B};
