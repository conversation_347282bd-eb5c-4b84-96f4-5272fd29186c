import{r as _,B as U,C as B,c as L,o as j,b as a,a as w,d as m,l as s,J as y,K as u,s as i,u as O,p as V}from"./index-GQNKmeFm.js";import{c}from"./api-CiHvhAh2.js";import{B as W}from"./BrandFooter-CIZu14VB.js";import{_ as M}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as S}from"./function-call-CabHCnJg.js";const P={class:"settings"},A={class:"settings-content"},$={class:"action-buttons"},z={__name:"Settings",setup(F){O();const g=_(!1),b=_(!1),l=U({webhookUrl:"",corpId:"",agentId:"",reminderInterval:30,maxReminders:3,enableReminder:!0,systemName:"会议室服务管理系统",companyName:"恒泰信息",adminEmail:"",supportPhone:"",dataRetentionDays:90,autoCleanup:!1}),r=U({version:"v2.1.0",envId:"huiyi-7gudxwsnf4ca4b67",deployTime:"",dbStatus:"检查中...",functionStatus:"检查中..."}),I=async()=>{try{y({message:"加载设置中...",forbidClick:!0});const e=await c("systemConfig",{action:"getSettings"});e.code===0&&e.data&&Object.assign(l,e.data),u()}catch(e){u(),console.error("加载设置失败:",e),i({type:"fail",message:"加载设置失败"})}},h=async()=>{try{g.value=!0,y({message:"保存中...",forbidClick:!0});const e=await c("systemConfig",{action:"saveSettings",data:l});if(e.code===0)u(),i({type:"success",message:"设置保存成功"});else throw new Error(e.message||"保存失败")}catch(e){u(),i({type:"fail",message:e.message||"保存失败"})}finally{g.value=!1}},R=async()=>{if(!l.webhookUrl.trim()){i("请先输入Webhook URL");return}try{b.value=!0;const e=await c("systemConfig",{action:"testWebhook",data:{webhookUrl:l.webhookUrl}});if(e.code===0)i({type:"success",message:"测试消息发送成功"});else throw new Error(e.message||"测试失败")}catch(e){i({type:"fail",message:e.message||"测试失败"})}finally{b.value=!1}},x=async()=>{await S({title:"确认重置",message:"确定要重置所有设置为默认值吗？此操作不可撤销。"}).catch(()=>!1)&&(Object.assign(l,{webhookUrl:"",corpId:"",agentId:"",reminderInterval:30,maxReminders:3,enableReminder:!0,systemName:"会议室服务管理系统",companyName:"恒泰信息",adminEmail:"",supportPhone:"",dataRetentionDays:90,autoCleanup:!1}),i({type:"success",message:"已重置为默认值"}))},D=async()=>{try{y({message:"导出中...",forbidClick:!0});const e=await c("systemConfig",{action:"exportData"});if(e.code===0){const t=new Blob([JSON.stringify(e.data,null,2)],{type:"application/json"}),f=URL.createObjectURL(t),n=document.createElement("a");n.href=f,n.download=`meeting-service-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(f),u(),i({type:"success",message:"数据导出成功"})}else throw new Error(e.message||"导出失败")}catch(e){u(),i({type:"fail",message:e.message||"导出失败"})}},N=async()=>{await S({title:"危险操作",message:"确定要清空所有数据吗？此操作将删除所有服务需求记录，且不可恢复！",confirmButtonText:"确认清空",confirmButtonColor:"#ee0a24"}).catch(()=>!1)&&await E()},E=async()=>{try{y({message:"清空中...",forbidClick:!0});const e=await c("systemConfig",{action:"clearAllData"});if(e.code===0)u(),i({type:"success",message:"数据清空成功"});else throw new Error(e.message||"清空失败")}catch(e){u(),i({type:"fail",message:e.message||"清空失败"})}},T=async()=>{try{const e=await c("systemConfig",{action:"getSystemStatus"});e.code===0&&e.data&&(r.deployTime=e.data.deployTime||"未知",r.dbStatus=e.data.dbStatus||"正常",r.functionStatus=e.data.functionStatus||"正常")}catch{r.dbStatus="连接失败",r.functionStatus="连接失败"}};return B(async()=>{await I(),await T()}),(e,t)=>{const f=m("van-nav-bar"),n=m("van-field"),v=m("van-button"),d=m("van-cell"),p=m("van-cell-group"),k=m("van-switch"),C=m("van-icon");return j(),L("div",P,[a(f,{title:"系统设置","left-text":"返回","left-arrow":"",onClickLeft:t[0]||(t[0]=o=>e.$router.back())}),w("div",A,[a(p,{inset:"",title:"企业微信配置"},{default:s(()=>[a(n,{modelValue:l.webhookUrl,"onUpdate:modelValue":t[1]||(t[1]=o=>l.webhookUrl=o),label:"Webhook URL",placeholder:"请输入企业微信机器人Webhook地址",type:"textarea",rows:"2",autosize:"",required:""},null,8,["modelValue"]),a(n,{modelValue:l.corpId,"onUpdate:modelValue":t[2]||(t[2]=o=>l.corpId=o),label:"企业ID",placeholder:"请输入企业微信企业ID（可选）"},null,8,["modelValue"]),a(n,{modelValue:l.agentId,"onUpdate:modelValue":t[3]||(t[3]=o=>l.agentId=o),label:"应用ID",placeholder:"请输入企业微信应用ID（可选）"},null,8,["modelValue"]),a(d,{title:"测试连接"},{value:s(()=>[a(v,{type:"primary",size:"mini",loading:b.value,onClick:R},{default:s(()=>t[13]||(t[13]=[V(" 发送测试消息 ")])),_:1,__:[13]},8,["loading"])]),_:1})]),_:1}),a(p,{inset:"",title:"提醒设置"},{default:s(()=>[a(n,{modelValue:l.reminderInterval,"onUpdate:modelValue":t[4]||(t[4]=o=>l.reminderInterval=o),modelModifiers:{number:!0},label:"提醒间隔",placeholder:"30",type:"number",suffix:"分钟"},null,8,["modelValue"]),a(n,{modelValue:l.maxReminders,"onUpdate:modelValue":t[5]||(t[5]=o=>l.maxReminders=o),modelModifiers:{number:!0},label:"最大提醒次数",placeholder:"3",type:"number",suffix:"次"},null,8,["modelValue"]),a(d,{title:"启用自动提醒"},{value:s(()=>[a(k,{modelValue:l.enableReminder,"onUpdate:modelValue":t[6]||(t[6]=o=>l.enableReminder=o)},null,8,["modelValue"])]),_:1})]),_:1}),a(p,{inset:"",title:"系统配置"},{default:s(()=>[a(n,{modelValue:l.systemName,"onUpdate:modelValue":t[7]||(t[7]=o=>l.systemName=o),label:"系统名称",placeholder:"会议室服务管理系统"},null,8,["modelValue"]),a(n,{modelValue:l.companyName,"onUpdate:modelValue":t[8]||(t[8]=o=>l.companyName=o),label:"公司名称",placeholder:"恒泰信息"},null,8,["modelValue"]),a(n,{modelValue:l.adminEmail,"onUpdate:modelValue":t[9]||(t[9]=o=>l.adminEmail=o),label:"管理员邮箱",placeholder:"<EMAIL>",type:"email"},null,8,["modelValue"]),a(n,{modelValue:l.supportPhone,"onUpdate:modelValue":t[10]||(t[10]=o=>l.supportPhone=o),label:"技术支持电话",placeholder:"************",type:"tel"},null,8,["modelValue"])]),_:1}),a(p,{inset:"",title:"数据管理"},{default:s(()=>[a(n,{modelValue:l.dataRetentionDays,"onUpdate:modelValue":t[11]||(t[11]=o=>l.dataRetentionDays=o),modelModifiers:{number:!0},label:"数据保留天数",placeholder:"90",type:"number",suffix:"天"},null,8,["modelValue"]),a(d,{title:"自动清理过期数据"},{value:s(()=>[a(k,{modelValue:l.autoCleanup,"onUpdate:modelValue":t[12]||(t[12]=o=>l.autoCleanup=o)},null,8,["modelValue"])]),_:1}),a(d,{title:"导出所有数据","is-link":"",onClick:D},{icon:s(()=>[a(C,{name:"down"})]),_:1}),a(d,{title:"清空所有数据","is-link":"",onClick:N},{icon:s(()=>[a(C,{name:"delete-o",color:"#ee0a24"})]),value:s(()=>t[14]||(t[14]=[w("span",{style:{color:"#ee0a24"}},"危险操作",-1)])),_:1})]),_:1}),a(p,{inset:"",title:"系统信息"},{default:s(()=>[a(d,{title:"系统版本",value:r.version},null,8,["value"]),a(d,{title:"环境ID",value:r.envId},null,8,["value"]),a(d,{title:"部署时间",value:r.deployTime},null,8,["value"]),a(d,{title:"数据库状态",value:r.dbStatus},null,8,["value"]),a(d,{title:"云函数状态",value:r.functionStatus},null,8,["value"])]),_:1}),w("div",$,[a(v,{type:"primary",block:"",round:"",loading:g.value,onClick:h},{default:s(()=>t[15]||(t[15]=[V(" 保存设置 ")])),_:1,__:[15]},8,["loading"]),a(v,{block:"",round:"",style:{"margin-top":"12px"},onClick:x},{default:s(()=>t[16]||(t[16]=[V(" 重置为默认值 ")])),_:1,__:[16]})])]),a(W,{variant:"minimal","light-background":!0})])}}},Q=M(z,[["__scopeId","data-v-2b560aa0"]]);export{Q as default};
