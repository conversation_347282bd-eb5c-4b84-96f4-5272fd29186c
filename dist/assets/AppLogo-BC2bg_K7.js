import{_ as s}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as l,o as r,a as t,y as m}from"./index-DaPIm3IU.js";const n=""+new URL("logo-C2CSGR66.jpg",import.meta.url).href,c={__name:"AppLogo",props:{size:{type:String,default:"medium",validator:o=>["small","medium","large"].includes(o)}},setup(o){const a=e=>{console.warn("Logo图片加载失败"),e.target.style.display="none"};return(e,i)=>(r(),l("div",{class:m(["app-logo",{"logo-small":o.size==="small","logo-large":o.size==="large"}])},[t("img",{src:n,alt:"阿泰会议服务",class:"logo-image",onError:a},null,32)],2))}},d=s(c,[["__scopeId","data-v-a3f6701e"]]);export{d as A};
