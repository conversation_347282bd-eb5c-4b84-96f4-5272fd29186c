import{r as v,w as I,C as L,D as S,c as m,o as r,b as n,a as t,d as l,x as R,A as f,l as s,t as x,u as z,p as q,s as i,I as G}from"./index-DaPIm3IU.js";import{a as M}from"./browser-BJzWpxwN.js";import{c as j}from"./api-CiHvhAh2.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";const J={class:"qrcode"},K={class:"content"},O={key:0,class:"qr-section"},P={class:"qr-container"},W={class:"qr-tip"},X={key:1,class:"empty-tip"},Y={class:"instruction-content"},Z={key:4,class:"actions"},ee={__name:"QRCode",setup(te){const U=z(),y=S(),u=v(),g=v([]),c=v(""),C=v(""),a=v("");I(a,async o=>{o&&await V()}),L(async()=>{C.value=new Date().toLocaleString("zh-CN"),y.query.meetingRoom&&(a.value=y.query.meetingRoom)});const V=async()=>{if(a.value)try{const e=(window.location.origin+window.location.pathname).replace("/qrcode","/multi-form")+`?meetingRoom=${encodeURIComponent(a.value)}`;c.value=e,await G(),u.value&&(await M.toCanvas(u.value,e,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"}}),await N(e))}catch(o){console.error("生成二维码失败:",o),i({type:"fail",message:"生成二维码失败"})}},N=async o=>{try{const e=u.value;if(!e)return;const p=e.toDataURL("image/png").split(",")[1];await j("qrCodeGenerator",{action:"saveQRCode",data:{roomNumber:a.value,qrUrl:o,qrCodeBase64:p,description:`${a.value}会议室服务二维码`}}),console.log("二维码已自动保存到数据库")}catch(e){console.error("保存二维码到数据库失败:",e)}},h=()=>{U.back()},D=()=>{},b=async()=>{try{if(navigator.clipboard)await navigator.clipboard.writeText(c.value);else{const o=document.createElement("textarea");o.value=c.value,document.body.appendChild(o),o.select(),document.execCommand("copy"),document.body.removeChild(o)}i({type:"success",message:"链接已复制到剪贴板"})}catch{i({type:"fail",message:"复制失败，请手动复制"})}},F=()=>{try{const o=u.value;if(o){const e=document.createElement("a");e.download=`会议室${a.value}-服务二维码-${new Date().getTime()}.png`,e.href=o.toDataURL(),e.click(),i({type:"success",message:"二维码已保存"})}}catch{i({type:"fail",message:"保存失败"})}},T=async()=>{try{navigator.share?await navigator.share({title:`${a.value}会议室服务`,text:`扫码直接进入${a.value}会议室服务页面`,url:c.value}):await b()}catch(o){o.name!=="AbortError"&&i({type:"fail",message:"分享失败"})}};return(o,e)=>{const p=l("van-nav-bar"),B=l("van-field"),w=l("van-cell-group"),Q=l("van-empty"),d=l("van-cell"),$=l("van-icon"),A=l("van-collapse-item"),E=l("van-collapse"),k=l("van-button");return r(),m("div",J,[n(p,{title:"会议室二维码生成","left-text":"返回","left-arrow":"",onClickLeft:h,fixed:""}),t("div",K,[e[15]||(e[15]=t("div",{class:"header"},[t("h2",null,"会议室服务二维码"),t("p",null,"生成专属会议室二维码，微信扫码直达服务页面")],-1)),n(w,{inset:"",title:"会议室信息"},{default:s(()=>[n(B,{modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=_=>a.value=_),name:"meetingRoom",label:"会议室编号",placeholder:"请输入会议室编号（如：A301）",onInput:D,clearable:""},null,8,["modelValue"])]),_:1}),a.value?(r(),m("div",O,[t("div",P,[t("canvas",{ref_key:"qrCanvas",ref:u,class:"qr-canvas"},null,512)]),t("p",W,"使用微信扫一扫直接进入"+x(a.value)+"服务页面",1)])):(r(),m("div",X,[n(Q,{image:"search",description:"请先输入会议室编号"})])),a.value?(r(),R(w,{key:2,inset:"",title:"二维码信息"},{default:s(()=>[n(d,{title:"会议室编号",value:a.value},null,8,["value"]),n(d,{title:"访问链接",value:c.value,"is-link":"",onClick:b},{"right-icon":s(()=>[n($,{name:"copy"})]),_:1},8,["value"]),n(d,{title:"生成时间",value:C.value},null,8,["value"]),n(d,{title:"有效期",value:"长期有效"})]),_:1})):f("",!0),a.value?(r(),R(E,{key:3,modelValue:g.value,"onUpdate:modelValue":e[1]||(e[1]=_=>g.value=_),class:"instructions"},{default:s(()=>[n(A,{title:"使用说明",name:"1"},{default:s(()=>[t("div",Y,[e[2]||(e[2]=t("h4",null,"📱 微信扫码",-1)),e[3]||(e[3]=t("p",null,"• 使用微信扫一扫功能扫描二维码",-1)),t("p",null,"• 自动跳转到"+x(a.value)+"服务页面",1),e[4]||(e[4]=t("p",null,"• 会议室信息已预填，无需手动输入",-1)),e[5]||(e[5]=t("h4",null,"🔗 部署方式",-1)),e[6]||(e[6]=t("p",null,"• 打印二维码贴在会议室门口",-1)),e[7]||(e[7]=t("p",null,"• 投影显示在会议室内",-1)),e[8]||(e[8]=t("p",null,"• 分享给会议参与者",-1)),e[9]||(e[9]=t("h4",null,"⚡ 服务流程",-1)),e[10]||(e[10]=t("p",null,"• 扫码后选择服务类型和具体需求",-1)),e[11]||(e[11]=t("p",null,"• 填写详细描述（可选）",-1)),e[12]||(e[12]=t("p",null,"• 提交后工作人员将及时处理",-1))])]),_:1})]),_:1},8,["modelValue"])):f("",!0),a.value?(r(),m("div",Z,[n(k,{type:"primary",block:"",round:"",onClick:F,icon:"photo-o"},{default:s(()=>e[13]||(e[13]=[q(" 保存二维码 ")])),_:1,__:[13]}),n(k,{type:"default",block:"",round:"",onClick:T,icon:"share-o",style:{"margin-top":"12px"}},{default:s(()=>e[14]||(e[14]=[q(" 分享二维码 ")])),_:1,__:[14]})])):f("",!0)])])}}},se=H(ee,[["__scopeId","data-v-5b2bb093"]]);export{se as default};
