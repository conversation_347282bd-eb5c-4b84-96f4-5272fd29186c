import{r as l,C as h,D as k,s as _,u as x,c as y,o as C,b as t,a as o,l as v,d as i,p as n,t as d}from"./index-DaPIm3IU.js";import{A as w}from"./AppLogo-BC2bg_K7.js";import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";const T={class:"multi-service-success"},B={class:"content"},R={class:"success-section"},M={class:"success-icon"},N={class:"success-subtitle"},V={class:"action-section"},q={__name:"MultiServiceSuccess",setup(z){const e=x(),r=k(),c=l(""),a=l(0),p=()=>{window.history.length>1?e.back():e.push("/")},m=()=>{e.push("/")};return h(()=>{try{c.value=r.query.meetingRoom||"",a.value=parseInt(r.query.serviceCount)||0,(!c.value||a.value===0)&&(_("页面参数错误，即将返回首页"),setTimeout(()=>{e.push("/")},2e3))}catch(u){console.error("解析页面参数失败:",u),_("页面参数错误，即将返回首页"),setTimeout(()=>{e.push("/")},2e3)}}),(u,s)=>{const f=i("van-nav-bar"),b=i("van-icon"),g=i("van-button");return C(),y("div",T,[t(f,{"left-text":"返回","left-arrow":"",onClickLeft:p,fixed:""},{title:v(()=>[t(w,{size:"small"})]),_:1}),o("div",B,[o("div",R,[o("div",M,[t(b,{name:"checked",size:"60",color:"#07c160"})]),s[3]||(s[3]=o("div",{class:"success-title"},"服务需求提交成功！",-1)),o("div",N,[s[0]||(s[0]=n(" 已为会议室 ")),o("strong",null,d(c.value),1),s[1]||(s[1]=n(" 提交 ")),o("strong",null,d(a.value),1),s[2]||(s[2]=n(" 项服务需求 "))])]),o("div",V,[t(g,{type:"primary",block:"",round:"",onClick:m,icon:"wap-home-o"},{default:v(()=>s[4]||(s[4]=[n(" 返回首页 ")])),_:1,__:[4]})])])])}}},L=S(q,[["__scopeId","data-v-b34145cc"]]);export{L as default};
