import{s as r,r as y,B as T,C as se,c as R,o as w,b as s,l as c,d as A,a,x as $,A as U,G as oe,u as ae,p as C,t as k,F as ne,z as le,J as h,K as g}from"./index-DaPIm3IU.js";import{c as m}from"./api-CiHvhAh2.js";import{A as ie}from"./AppLogo-BC2bg_K7.js";import{B as re}from"./BrandFooter-EH36XjN5.js";import{_ as ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as de,a as ue}from"./function-call-T0USrQIg.js";const me=()=>{r("已退出登录")},ge={class:"admin"},pe={key:0,class:"login-section"},ve={class:"login-form"},fe={class:"login-header"},ye={class:"login-button"},be={key:1,class:"admin-content"},we={class:"quick-actions"},Ae={class:"stats-grid"},ke={class:"stat-item"},he={class:"stat-number"},Be={class:"stat-item"},Ce={class:"stat-number today"},Se={class:"stat-item"},Re={class:"stat-number"},_e={class:"stat-item"},qe={class:"stat-number"},Ve={class:"stat-item"},xe={class:"stat-number"},De={class:"stat-item"},Te={class:"stat-number scans"},$e={class:"qr-preview-grid"},Ue={class:"qr-header"},Ie=["innerHTML"],Pe={class:"qr-url"},Ee={class:"admin-actions"},Ne={__name:"Admin",setup(Oe){const E=ae(),I=y(!1),N=y(null),_=y(""),V=y(!1),B=y([]),f=T({totalRequests:void 0,todayRequests:void 0,singleRequests:void 0,batchRequests:void 0,totalScans:void 0}),x=y(0),q=T({A:0,B:0}),S=y("未生成"),D=y("未同步");y(!1);const O=y(!1),z=y(!1),l=T({webhookUrl:"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e3da0597-dbb4-4cca-8b3e-6f3975264e2b",serviceTypes:"茶水,空调,设备",teaServices:"更换茶水,增加茶水,清洁茶具",airServices:"升温,降温,关闭,调节风速",equipmentServices:"投影设备,音响设备,麦克风,网络连接",meetingRooms:"A0815,A0816,A0914,A0915,A1014,A1015,A1101,A1102,A1103,A1104,A1106,A1107,A1108,A1211,A1212,A1311,A1312,A1408,A1409,A1514,A1515,A1615,A1616,A1708,A2015,A2016,A2113,A2312,A2313,A2412,A2413,B0511,B0512,B0611,B0711,B0712,B0906,B0911,B0912,B1011,B1012,B1110,B1111,B1112,B1211,B1212,B1301,B1312,B1313,B1411,B1511,B1512",systemTitle:"阿泰会议服务",systemDescription:"快速提交服务需求到企业微信群",contactPhone:"************"}),L=async()=>{try{const t=["admin123","manager456","atai2024"];if(!_.value){r({type:"fail",message:"请输入验证码"});return}t.includes(_.value)?(I.value=!0,N.value={nickName:"管理员",isAdmin:!0},r({type:"success",message:"验证成功"}),j()):r({type:"fail",message:"验证码错误"})}catch{r({type:"fail",message:"验证失败"})}},j=async()=>{try{const t=await m("configManager",{action:"getConfig",data:{}});if(t.code===0&&t.data.config)Object.assign(l,t.data.config),D.value="已同步",console.log("从云端加载配置成功");else{const e=localStorage.getItem("admin_config");if(e)try{const o=JSON.parse(e);Object.assign(l,o),console.log("从本地存储加载配置")}catch(o){console.error("加载本地配置失败:",o)}}}catch(t){console.error("加载配置失败:",t);const e=localStorage.getItem("admin_config");if(e)try{const o=JSON.parse(e);Object.assign(l,o)}catch(o){console.error("加载本地配置失败:",o)}}P(),F()},P=async()=>{try{const t=await m("serviceRequest",{action:"getRequests",data:{limit:1e3}});if(t.code===0){const o=t.data.list;f.totalRequests=o.length;const i=new Date().toISOString().split("T")[0];f.todayRequests=o.filter(v=>new Date(v.createTime).toISOString().split("T")[0]===i).length;const u=new Set;let p=0;o.forEach(v=>{v.batchId?u.add(v.batchId):p++}),f.singleRequests=p,f.batchRequests=u.size}const e=await m("qrCodeGenerator",{action:"getQRCodeStats",data:{}});e.code===0&&(f.totalScans=e.data.totalScans)}catch(t){console.error("加载统计数据失败:",t)}},F=async()=>{try{const t=await m("room",{action:"getRooms",data:{limit:100}});if(t.code===0){const e=t.data;x.value=e.length,q.A=e.filter(o=>o.building==="A").length,q.B=e.filter(o=>o.building==="B").length}}catch(t){console.error("加载会议室统计失败:",t)}},Q=async()=>{try{h({message:"导出数据中...",duration:0});const[t,e,o]=await Promise.all([m("serviceRequest",{action:"getRequests",data:{limit:1e3}}),m("room",{action:"getRooms",data:{limit:100}}),m("qrCodeGenerator",{action:"getQRCodeStats",data:{}})]),i={exportTime:new Date().toISOString(),serviceRequests:t.code===0?t.data.list:[],meetingRooms:e.code===0?e.data:[],scanStats:o.code===0?o.data:{}},u=new Blob([JSON.stringify(i,null,2)],{type:"application/json"}),p=URL.createObjectURL(u),v=document.createElement("a");v.href=p,v.download=`阿泰会议服务数据_${new Date().toISOString().split("T")[0]}.json`,v.click(),g(),r({type:"success",message:"数据导出成功"})}catch(t){g(),r({type:"fail",message:"导出失败："+t.message})}},M=async()=>{V.value=!0,h({message:"保存中...",duration:0});try{const t=await m("configManager",{action:"saveConfig",data:l});if(t.code===0)localStorage.setItem("admin_config",JSON.stringify(l)),D.value="已同步",g(),r({type:"success",message:"配置保存成功，立即生效！"}),console.log("配置保存成功，Webhook地址:",l.webhookUrl);else throw new Error(t.message||"保存失败")}catch(t){console.error("保存配置失败:",t),g(),r({type:"fail",message:t.message||"保存失败，请重试"})}finally{V.value=!1}},G=async()=>{try{await ue({title:"确认重置",message:"确定要重置所有配置吗？此操作不可恢复。"}),localStorage.removeItem("admin_config");const t=await m("configManager",{action:"resetConfig",data:{}});if(t.code===0)Object.assign(l,{webhookUrl:"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e3da0597-dbb4-4cca-8b3e-6f3975264e2b",serviceTypes:"茶水,空调,设备",teaServices:"更换茶水,增加茶水,清洁茶具",airServices:"升温,降温,关闭,调节风速",equipmentServices:"投影设备,音响设备,麦克风,网络连接",meetingRooms:"A0815,A0816,A0914,A0915,A1014,A1015,A1101,A1102,A1103,A1104,A1106,A1107,A1108,A1211,A1212,A1311,A1312,A1408,A1409,A1514,A1515,A1615,A1616,A1708,A2015,A2016,A2113,A2312,A2313,A2412,A2413,B0511,B0512,B0611,B0711,B0712,B0906,B0911,B0912,B1011,B1012,B1110,B1111,B1112,B1211,B1212,B1301,B1312,B1313,B1411,B1511,B1512",systemTitle:"阿泰会议服务",systemDescription:"快速提交服务需求到企业微信群",contactPhone:"************"});else throw new Error(t.message||"重置失败");r({type:"success",message:"配置已重置并同步到云端"})}catch(t){t!=="cancel"&&r({type:"fail",message:t.message||"重置失败"})}},W=async()=>{if(!l.webhookUrl){r("请先输入Webhook地址");return}h({message:"测试中...",duration:0});try{const t=await m("configManager",{action:"saveConfig",data:l});if(t.code!==0)throw new Error("保存配置失败："+t.message);const e=await m("serviceRequest",{action:"submitRequest",data:{meetingRoom:"测试会议室",serviceType:"测试",serviceDetail:"Webhook配置测试",description:"这是一条测试消息，用于验证企业微信通知配置是否正确。"}});if(g(),e.code===0)r({type:"success",message:"测试消息发送成功！请检查企业微信群"});else throw new Error(e.message||"测试失败")}catch(t){g(),r({type:"fail",message:t.message||"Webhook连接测试失败"})}},J=()=>{E.push("/")},Z=async()=>{if(!l.meetingRooms.trim()){r("请先配置会议室列表");return}h({message:"同步会议室数据中...",duration:0});try{const t=l.meetingRooms.split(",").map(i=>i.trim()).filter(i=>i),e=t.map(i=>{const u=i.charAt(0),p=i.slice(1),v=parseInt(p.substring(0,2))||1;return{roomNumber:i,building:u,floor:v,status:"active",createTime:new Date,updateTime:new Date}}),o=await m("room",{action:"syncRooms",data:{rooms:e}});if(g(),o.code===0)r({type:"success",message:`成功同步${t.length}个会议室`});else throw new Error(o.message)}catch(t){g(),r({type:"fail",message:t.message||"同步失败"})}},H=async()=>{S.value="生成中...",h({message:"生成二维码中...",duration:0});try{const t=await m("qrCodeGenerator",{action:"generateQRCodes",data:{format:"images"}});if(t.code===0)B.value=t.data.qrCodes.map(e=>({room:e.roomNumber,roomNumber:e.roomNumber,building:e.building,floor:e.floor,url:e.qrUrl,qrcode:`<img src="data:image/png;base64,${e.qrCodeBase64}" style="width: 150px; height: 150px;" />`})),S.value="已生成",g(),r({type:"success",message:`成功生成${B.value.length}个二维码`});else throw S.value="生成失败",new Error(t.message)}catch(t){S.value="生成失败",g(),r({type:"fail",message:t.message||"生成二维码失败"})}},K=t=>{const e=document.createElement("canvas"),o=e.getContext("2d");e.width=300,e.height=400,o.fillStyle="white",o.fillRect(0,0,300,400),o.fillStyle="black",o.font="bold 20px Arial",o.textAlign="center",o.fillText(`${t.room} 会议室`,150,30),o.fillRect(50,50,200,200),o.fillStyle="white",o.fillRect(60,60,180,180),o.fillStyle="black",o.font="12px Arial",o.fillText("扫码提交服务需求",150,280),o.fillText("阿泰会议服务系统",150,300);const i=document.createElement("a");i.download=`${t.room}_二维码.png`,i.href=e.toDataURL(),i.click()},X=async()=>{h({message:"生成PDF中...",duration:0});try{const t=await m("qrCodeGenerator",{action:"generateQRCodes",data:{format:"pdf",layout:"4x4"}});if(t.code===0){const e=document.createElement("a");e.href=`data:application/pdf;base64,${t.data.content}`,e.download=t.data.filename,e.click(),g(),r({type:"success",message:"PDF下载成功"})}else throw new Error(t.message)}catch(t){g(),r({type:"fail",message:t.message||"PDF生成失败"})}},Y=async()=>{h({message:"生成ZIP中...",duration:0});try{const t=await m("qrCodeGenerator",{action:"generateQRCodes",data:{format:"zip"}});if(t.code===0){const e=document.createElement("a");e.href=`data:application/zip;base64,${t.data.content}`,e.download=t.data.filename,e.click(),g(),r({type:"success",message:"ZIP下载成功"})}else throw new Error(t.message)}catch(t){g(),r({type:"fail",message:t.message||"ZIP生成失败"})}},ee=async()=>{try{const t=await m("qrCodeGenerator",{action:"getQRCodeStats",data:{}});if(t.code===0){const e=t.data;let o=`扫码统计信息：

`;o+=`总扫码次数：${e.totalScans}
`,Object.keys(e.roomStats).length>0&&(o+=`
各会议室扫码次数：
`,Object.values(e.roomStats).forEach(i=>{o+=`${i.building}${i.roomNumber}：${i.scanCount}次
`})),de({title:"扫码统计",message:o,confirmButtonText:"确定"})}else throw new Error(t.message)}catch(t){r({type:"fail",message:t.message||"获取统计失败"})}};return se(()=>{}),(t,e)=>{const o=A("van-nav-bar"),i=A("van-icon"),u=A("van-field"),p=A("van-button"),v=A("van-form"),d=A("van-cell"),b=A("van-cell-group"),te=A("van-tag");return w(),R("div",ge,[s(o,{title:"后台设置","left-text":"返回","left-arrow":"",onClickLeft:J,fixed:""},{right:c(()=>[s(ie,{size:"small"})]),_:1}),I.value?(w(),R("div",be,[s(b,{inset:"",title:"企业微信配置"},{default:c(()=>[s(u,{modelValue:l.webhookUrl,"onUpdate:modelValue":e[1]||(e[1]=n=>l.webhookUrl=n),name:"webhookUrl",label:"Webhook地址",placeholder:"请输入企业微信群机器人Webhook地址",type:"textarea",rows:"3"},null,8,["modelValue"]),s(d,{title:"配置状态",value:D.value},null,8,["value"]),s(d,{title:"测试连接","is-link":"",onClick:W,icon:"chat-o"})]),_:1}),s(b,{inset:"",title:"服务类型配置"},{default:c(()=>[s(u,{modelValue:l.serviceTypes,"onUpdate:modelValue":e[2]||(e[2]=n=>l.serviceTypes=n),name:"serviceTypes",label:"服务类型",placeholder:"请输入服务类型，用逗号分隔",type:"textarea",rows:"2"},null,8,["modelValue"]),s(u,{modelValue:l.teaServices,"onUpdate:modelValue":e[3]||(e[3]=n=>l.teaServices=n),name:"teaServices",label:"茶水服务选项",placeholder:"请输入茶水服务选项，用逗号分隔",type:"textarea",rows:"2"},null,8,["modelValue"]),s(u,{modelValue:l.airServices,"onUpdate:modelValue":e[4]||(e[4]=n=>l.airServices=n),name:"airServices",label:"空调服务选项",placeholder:"请输入空调服务选项，用逗号分隔",type:"textarea",rows:"2"},null,8,["modelValue"]),s(u,{modelValue:l.equipmentServices,"onUpdate:modelValue":e[5]||(e[5]=n=>l.equipmentServices=n),name:"equipmentServices",label:"设备服务选项",placeholder:"请输入设备服务选项，用逗号分隔",type:"textarea",rows:"2"},null,8,["modelValue"])]),_:1}),s(b,{inset:"",title:"快速操作"},{default:c(()=>[a("div",we,[a("div",{class:"action-card",onClick:e[6]||(e[6]=n=>t.$router.push("/requests"))},[s(i,{name:"orders-o",size:"24",color:"#ee0a24"}),e[18]||(e[18]=a("span",null,"需求管理",-1))]),a("div",{class:"action-card",onClick:H},[s(i,{name:"qr",size:"24",color:"#1989fa"}),e[19]||(e[19]=a("span",null,"生成二维码",-1))]),a("div",{class:"action-card",onClick:Z},[s(i,{name:"refresh",size:"24",color:"#07c160"}),e[20]||(e[20]=a("span",null,"同步会议室",-1))]),a("div",{class:"action-card",onClick:ee},[s(i,{name:"chart-trending-o",size:"24",color:"#ff976a"}),e[21]||(e[21]=a("span",null,"查看统计",-1))]),a("div",{class:"action-card",onClick:Q},[s(i,{name:"down",size:"24",color:"#323233"}),e[22]||(e[22]=a("span",null,"导出数据",-1))])])]),_:1}),f.totalRequests!==void 0?(w(),$(b,{key:0,inset:"",title:"数据统计"},{default:c(()=>[a("div",Ae,[a("div",ke,[a("div",he,k(f.totalRequests),1),e[23]||(e[23]=a("div",{class:"stat-label"},"总服务请求",-1))]),a("div",Be,[a("div",Ce,k(f.todayRequests),1),e[24]||(e[24]=a("div",{class:"stat-label"},"今日请求",-1))]),a("div",Se,[a("div",Re,k(f.singleRequests),1),e[25]||(e[25]=a("div",{class:"stat-label"},"单项服务",-1))]),a("div",_e,[a("div",qe,k(f.batchRequests),1),e[26]||(e[26]=a("div",{class:"stat-label"},"批次服务",-1))]),a("div",Ve,[a("div",xe,k(x.value),1),e[27]||(e[27]=a("div",{class:"stat-label"},"会议室数量",-1))]),a("div",De,[a("div",Te,k(f.totalScans),1),e[28]||(e[28]=a("div",{class:"stat-label"},"扫码次数",-1))])]),s(d,{title:"刷新统计","is-link":"",onClick:P})]),_:1})):U("",!0),s(b,{inset:"",title:"会议室管理"},{default:c(()=>[s(d,{title:"会议室总数",value:`${x.value}个`},null,8,["value"]),s(d,{title:"A栋会议室",value:`${q.A||0}个`},null,8,["value"]),s(d,{title:"B栋会议室",value:`${q.B||0}个`},null,8,["value"]),s(d,{title:"编辑会议室列表","is-link":"",onClick:e[7]||(e[7]=n=>t.$router.push("/rooms"))}),s(d,{title:"批量导入会议室","is-link":"",onClick:e[8]||(e[8]=n=>O.value=!0)}),s(d,{title:"系统设置","is-link":"",onClick:e[9]||(e[9]=n=>t.$router.push("/settings"))},{icon:c(()=>[s(i,{name:"setting-o"})]),value:c(()=>e[29]||(e[29]=[a("span",null,"配置管理",-1)])),_:1})]),_:1}),s(b,{inset:"",title:"二维码管理"},{default:c(()=>[s(d,{title:"二维码管理中心","is-link":"",onClick:e[10]||(e[10]=n=>t.$router.push("/qr-management")),icon:"qr"},{value:c(()=>[s(te,{type:"primary"},{default:c(()=>e[30]||(e[30]=[C("新功能")])),_:1,__:[30]})]),_:1}),s(d,{title:"生成状态",value:S.value},null,8,["value"]),s(d,{title:"生成数量",value:`${B.value.length}个`},null,8,["value"]),s(d,{title:"下载PDF格式","is-link":"",onClick:X}),s(d,{title:"下载ZIP格式","is-link":"",onClick:Y}),B.value.length>0?(w(),$(d,{key:0,title:"预览二维码","is-link":"",onClick:e[11]||(e[11]=n=>z.value=!0)})):U("",!0)]),_:1}),B.value.length>0?(w(),$(b,{key:1,inset:"",title:"二维码预览"},{default:c(()=>[a("div",$e,[(w(!0),R(ne,null,le(B.value,n=>(w(),R("div",{key:n.room,class:"qr-item"},[a("div",Ue,k(n.room),1),a("div",{class:"qr-code",innerHTML:n.qrcode},null,8,Ie),a("div",Pe,k(n.url),1),s(p,{size:"small",type:"primary",onClick:ze=>K(n)},{default:c(()=>e[31]||(e[31]=[C("下载")])),_:2,__:[31]},1032,["onClick"])]))),128))])]),_:1})):U("",!0),s(b,{inset:"",title:"系统设置"},{default:c(()=>[s(u,{modelValue:l.systemTitle,"onUpdate:modelValue":e[12]||(e[12]=n=>l.systemTitle=n),name:"systemTitle",label:"系统标题",placeholder:"请输入系统标题"},null,8,["modelValue"]),s(u,{modelValue:l.systemDescription,"onUpdate:modelValue":e[13]||(e[13]=n=>l.systemDescription=n),name:"systemDescription",label:"系统描述",placeholder:"请输入系统描述",type:"textarea",rows:"2"},null,8,["modelValue"]),s(u,{modelValue:l.contactPhone,"onUpdate:modelValue":e[14]||(e[14]=n=>l.contactPhone=n),name:"contactPhone",label:"紧急联系电话",placeholder:"请输入紧急联系电话"},null,8,["modelValue"])]),_:1}),a("div",Ee,[s(p,{type:"primary",block:"",round:"",onClick:M,loading:V.value},{default:c(()=>e[32]||(e[32]=[C(" 保存配置 ")])),_:1,__:[32]},8,["loading"]),s(p,{type:"default",block:"",round:"",onClick:G,style:{"margin-top":"12px"}},{default:c(()=>e[33]||(e[33]=[C(" 重置配置 ")])),_:1,__:[33]}),s(p,{type:"danger",block:"",round:"",onClick:oe(me),style:{"margin-top":"12px"}},{default:c(()=>e[34]||(e[34]=[C(" 退出登录 ")])),_:1,__:[34]},8,["onClick"])]),s(re,{variant:"minimal","light-background":!0})])):(w(),R("div",pe,[a("div",ve,[a("div",fe,[s(i,{name:"lock",size:"48",color:"#1989fa"}),e[15]||(e[15]=a("h2",null,"后台管理",-1)),e[16]||(e[16]=a("p",null,"请输入管理密码",-1))]),s(v,{onSubmit:L},{default:c(()=>[s(u,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=n=>_.value=n),type:"password",name:"password",label:"验证码",placeholder:"请输入管理员验证码",rules:[{required:!0,message:"请输入验证码"}]},null,8,["modelValue"]),a("div",ye,[s(p,{round:"",block:"",type:"primary","native-type":"submit"},{default:c(()=>e[17]||(e[17]=[C(" 验证登录 ")])),_:1,__:[17]})])]),_:1})])]))])}}},We=ce(Ne,[["__scopeId","data-v-debc3b88"]]);export{We as default};
