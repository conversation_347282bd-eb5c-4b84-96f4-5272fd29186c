import{r as d,q as Q,C as W,s as u,c as f,o as v,b as o,a as n,l as m,d as r,x as D,t as g,F as O,z as j,p as X,E as F}from"./index-GQNKmeFm.js";import{c as y}from"./api-CiHvhAh2.js";import{A as Y}from"./AppLogo-BaVronrj.js";import{B as Z}from"./BrandFooter-CIZu14VB.js";import{_ as ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as le}from"./function-call-CabHCnJg.js";const ae={class:"room-management"},oe={class:"content"},te={class:"search-section"},ne={class:"filter-tabs"},ie={class:"add-button"},se={class:"rooms-section"},ue={class:"section-header"},re={class:"view-toggle"},de={key:0,class:"rooms-grid"},me=["onClick"],ce={class:"room-header"},ve={class:"room-number"},pe={class:"room-info"},be={class:"room-location"},_e={class:"room-capacity"},fe={class:"edit-form"},ge={__name:"RoomManagement",setup(ye){const N=d([]),V=d(""),w=d("all"),k=d("grid"),p=d(!1),h=d(!1),C=d(""),i=d({number:"",name:"",building:"",floor:"",capacity:""}),t=d({_id:"",roomNumber:"",name:"",building:"",floor:"",capacity:""}),R=Q(()=>{let a=N.value;if(w.value!=="all"&&(a=a.filter(l=>l.building===w.value)),V.value){const l=V.value.toLowerCase();a=a.filter(c=>(c.roomNumber||c.number||"").toLowerCase().includes(l)||(c.name||"").toLowerCase().includes(l))}return a});W(()=>{b()});const b=async()=>{try{const a=await y("roomManagement",{action:"getRooms"});N.value=a.data||[]}catch{u("加载会议室列表失败")}},I=async()=>{if(!i.value.number){u("请输入会议室编号");return}try{await y("roomManagement",{action:"addRoom",data:i.value}),u("添加成功"),i.value={number:"",name:"",building:"",floor:"",capacity:""},b()}catch{u("添加失败")}},A=a=>{t.value={_id:a._id,roomNumber:a.roomNumber||a.number,name:a.name||"",building:a.building||"",floor:a.floor||"",capacity:a.capacity||""},p.value=!0},T=async()=>{if(!t.value.roomNumber){u("请输入会议室编号");return}try{await y("roomManagement",{action:"updateRoom",data:{id:t.value._id,roomNumber:t.value.roomNumber,number:t.value.roomNumber,name:t.value.name,building:t.value.building,floor:parseInt(t.value.floor)||0,capacity:parseInt(t.value.capacity)||0}}),u("更新成功"),p.value=!1,b()}catch{u("更新失败")}},q=()=>{p.value=!1},x=async a=>{try{await le({title:"确认删除",message:`确定要删除会议室 ${a.roomNumber||a.number} 吗？`}),await y("roomManagement",{action:"deleteRoom",data:{id:a._id}}),u("删除成功"),b()}catch(l){l!=="cancel"&&u("删除失败")}},J=async()=>{try{const a=JSON.parse(C.value);await y("roomManagement",{action:"importRooms",data:{rooms:a}}),u("导入成功"),C.value="",h.value=!1,b()}catch{u("导入失败，请检查数据格式")}},z=()=>{const a=JSON.stringify(N.value,null,2),l=new Blob([a],{type:"application/json"}),c=URL.createObjectURL(l),_=document.createElement("a");_.href=c,_.download="meeting-rooms.json",_.click(),URL.revokeObjectURL(c)},E=()=>{},K=()=>{},G=()=>{k.value=k.value==="grid"?"list":"grid"};return(a,l)=>{const c=r("van-nav-bar"),_=r("van-search"),U=r("van-tab"),H=r("van-tabs"),s=r("van-field"),P=r("van-button"),M=r("van-cell-group"),$=r("van-icon"),L=r("van-cell"),S=r("van-dialog");return v(),f("div",ae,[o(c,{title:"会议室管理","left-arrow":"",onClickLeft:l[0]||(l[0]=e=>a.$router.back())},{right:m(()=>[o(Y,{size:"small"})]),_:1}),n("div",oe,[n("div",te,[o(_,{modelValue:V.value,"onUpdate:modelValue":l[1]||(l[1]=e=>V.value=e),placeholder:"搜索会议室编号或名称",onSearch:E,onClear:E},null,8,["modelValue"]),n("div",ne,[o(H,{active:w.value,"onUpdate:active":l[2]||(l[2]=e=>w.value=e),onChange:K},{default:m(()=>[o(U,{title:"全部",name:"all"}),o(U,{title:"A栋",name:"A"}),o(U,{title:"B栋",name:"B"})]),_:1},8,["active"])])]),o(M,{inset:"",title:"添加会议室"},{default:m(()=>[o(s,{modelValue:i.value.number,"onUpdate:modelValue":l[3]||(l[3]=e=>i.value.number=e),label:"会议室编号",placeholder:"如: A0101",required:""},null,8,["modelValue"]),o(s,{modelValue:i.value.name,"onUpdate:modelValue":l[4]||(l[4]=e=>i.value.name=e),label:"会议室名称",placeholder:"如: 第一会议室"},null,8,["modelValue"]),o(s,{modelValue:i.value.building,"onUpdate:modelValue":l[5]||(l[5]=e=>i.value.building=e),label:"楼栋",placeholder:"如: A"},null,8,["modelValue"]),o(s,{modelValue:i.value.floor,"onUpdate:modelValue":l[6]||(l[6]=e=>i.value.floor=e),label:"楼层",placeholder:"如: 1",type:"number"},null,8,["modelValue"]),o(s,{modelValue:i.value.capacity,"onUpdate:modelValue":l[7]||(l[7]=e=>i.value.capacity=e),label:"容纳人数",placeholder:"如: 20",type:"number"},null,8,["modelValue"]),n("div",ie,[o(P,{type:"primary",block:"",onClick:I},{default:m(()=>l[17]||(l[17]=[X("添加会议室")])),_:1,__:[17]})])]),_:1}),n("div",se,[n("div",ue,[n("h3",null,"会议室列表 ("+g(R.value.length)+")",1),n("div",re,[o($,{name:k.value==="grid"?"apps-o":"bars",onClick:G,class:"toggle-icon"},null,8,["name"])])]),k.value==="grid"?(v(),f("div",de,[(v(!0),f(O,null,j(R.value,e=>(v(),f("div",{key:e._id,class:"room-card",onClick:B=>A(e)},[n("div",ce,[n("span",ve,g(e.roomNumber||e.number),1),o($,{name:"delete-o",class:"delete-icon",onClick:F(B=>x(e),["stop"])},null,8,["onClick"])]),n("div",pe,[n("div",be,g(e.building)+"栋 "+g(e.floor)+"楼",1),n("div",_e,g(e.capacity||0)+"人",1)])],8,me))),128))])):(v(),D(M,{key:1,inset:""},{default:m(()=>[(v(!0),f(O,null,j(R.value,e=>(v(),D(L,{key:e._id,title:e.roomNumber||e.number,label:`${e.building}栋 ${e.floor}楼 | 容纳${e.capacity||0}人`,value:e.name||e.roomNumber,"is-link":"",onClick:B=>A(e)},{"right-icon":m(()=>[o($,{name:"delete-o",onClick:F(B=>x(e),["stop"])},null,8,["onClick"])]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1}))]),o(M,{inset:"",title:"批量操作"},{default:m(()=>[o(L,{title:"批量导入","is-link":"",onClick:l[8]||(l[8]=e=>h.value=!0)}),o(L,{title:"导出数据","is-link":"",onClick:z})]),_:1})]),o(S,{show:p.value,"onUpdate:show":l[14]||(l[14]=e=>p.value=e),title:"编辑会议室","show-cancel-button":"",onConfirm:T,onCancel:q},{default:m(()=>[n("div",fe,[o(s,{modelValue:t.value.roomNumber,"onUpdate:modelValue":l[9]||(l[9]=e=>t.value.roomNumber=e),label:"会议室编号",placeholder:"如: A0101",required:""},null,8,["modelValue"]),o(s,{modelValue:t.value.name,"onUpdate:modelValue":l[10]||(l[10]=e=>t.value.name=e),label:"会议室名称",placeholder:"如: 第一会议室"},null,8,["modelValue"]),o(s,{modelValue:t.value.building,"onUpdate:modelValue":l[11]||(l[11]=e=>t.value.building=e),label:"楼栋",placeholder:"如: A"},null,8,["modelValue"]),o(s,{modelValue:t.value.floor,"onUpdate:modelValue":l[12]||(l[12]=e=>t.value.floor=e),label:"楼层",placeholder:"如: 1",type:"number"},null,8,["modelValue"]),o(s,{modelValue:t.value.capacity,"onUpdate:modelValue":l[13]||(l[13]=e=>t.value.capacity=e),label:"容纳人数",placeholder:"如: 20",type:"number"},null,8,["modelValue"])])]),_:1},8,["show"]),o(S,{show:h.value,"onUpdate:show":l[16]||(l[16]=e=>h.value=e),title:"批量导入会议室","show-cancel-button":"",onConfirm:J},{default:m(()=>[o(s,{modelValue:C.value,"onUpdate:modelValue":l[15]||(l[15]=e=>C.value=e),type:"textarea",placeholder:"请输入JSON格式的会议室数据",rows:"6"},null,8,["modelValue"])]),_:1},8,["show"]),o(Z,{variant:"minimal","light-background":!0})])}}},Re=ee(ge,[["__scopeId","data-v-53cd6e72"]]);export{Re as default};
