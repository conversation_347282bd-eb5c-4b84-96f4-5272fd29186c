import{i as hi,e as vi,m as pi,f as gi,b as U,g as xi,h as yi,j as _i,r as ge,w as Tr,k as wi,c as pe,o as re,l as J,a as F,n as wt,v as At,d as xe,t as Ge,p as Pe,s as Ce,q as rt,x as _t,y as yr,z as Ct,F as Et,A as We,B as Gr,C as Ai,D as Ci,u as Ei,E as at,G as Xr,H as mi,I as Si,J as Ii,K as Wr}from"./index-GQNKmeFm.js";import{c as Oi,s as Ti}from"./api-CiHvhAh2.js";import{_ as br}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{A as bi}from"./AppLogo-BaVronrj.js";import{B as Di}from"./BrandFooter-CIZu14VB.js";let zr,Tt;const Ri=r=>_i(r)?r:{message:r};function Ni(){({instance:Tt}=pi({setup(){const{state:r,toggle:e}=gi();return()=>U(yi,xi(r,{"onUpdate:show":e}),null)}}))}const Pi=()=>({type:"danger",color:void 0,message:"",onClose:void 0,onClick:void 0,onOpened:void 0,duration:3e3,position:void 0,className:"",lockScroll:!1,background:void 0});let Mi=Pi();const Bi=()=>{Tt&&Tt.toggle(!1)};function $r(r){if(hi)return Tt||Ni(),r=vi({},Mi,Ri(r)),Tt.open(r),clearTimeout(zr),r.duration>0&&(zr=setTimeout(Bi,r.duration)),Tt}function Fi(r,e){var t=Object.setPrototypeOf;t?t(r,e):r.__proto__=e}function Li(r,e){e===void 0&&(e=r.constructor);var t=Error.captureStackTrace;t&&t(r,e)}var ki=function(){var r=function(t,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},r(t,n)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ui=function(r){ki(e,r);function e(t,n){var i=this.constructor,a=r.call(this,t,n)||this;return Object.defineProperty(a,"name",{value:i.name,enumerable:!1,configurable:!0}),Fi(a,i.prototype),Li(a),a}return e}(Error),Vi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),$e=function(r){Vi(e,r);function e(t){t===void 0&&(t=void 0);var n=r.call(this,t)||this;return n.message=t,n}return e.prototype.getKind=function(){var t=this.constructor;return t.kind},e.kind="Exception",e}(Ui),Hi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ye=function(r){Hi(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ArgumentException",e}($e),Gi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),k=function(r){Gi(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="IllegalArgumentException",e}($e),bn=function(){function r(e){if(this.binarizer=e,e===null)throw new k("Binarizer must be non-null.")}return r.prototype.getWidth=function(){return this.binarizer.getWidth()},r.prototype.getHeight=function(){return this.binarizer.getHeight()},r.prototype.getBlackRow=function(e,t){return this.binarizer.getBlackRow(e,t)},r.prototype.getBlackMatrix=function(){return(this.matrix===null||this.matrix===void 0)&&(this.matrix=this.binarizer.getBlackMatrix()),this.matrix},r.prototype.isCropSupported=function(){return this.binarizer.getLuminanceSource().isCropSupported()},r.prototype.crop=function(e,t,n,i){var a=this.binarizer.getLuminanceSource().crop(e,t,n,i);return new r(this.binarizer.createBinarizer(a))},r.prototype.isRotateSupported=function(){return this.binarizer.getLuminanceSource().isRotateSupported()},r.prototype.rotateCounterClockwise=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new r(this.binarizer.createBinarizer(e))},r.prototype.rotateCounterClockwise45=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new r(this.binarizer.createBinarizer(e))},r.prototype.toString=function(){try{return this.getBlackMatrix().toString()}catch{return""}},r}(),Xi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),_e=function(r){Xi(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.getChecksumInstance=function(){return new e},e.kind="ChecksumException",e}($e),Wi=function(){function r(e){this.source=e}return r.prototype.getLuminanceSource=function(){return this.source},r.prototype.getWidth=function(){return this.source.getWidth()},r.prototype.getHeight=function(){return this.source.getHeight()},r}(),ue=function(){function r(){}return r.arraycopy=function(e,t,n,i,a){for(;a--;)n[i++]=e[t++]},r.currentTimeMillis=function(){return Date.now()},r}(),zi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Dr=function(r){zi(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="IndexOutOfBoundsException",e}($e),$i=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),jr=function(r){$i(e,r);function e(t,n){t===void 0&&(t=void 0),n===void 0&&(n=void 0);var i=r.call(this,n)||this;return i.index=t,i.message=n,i}return e.kind="ArrayIndexOutOfBoundsException",e}(Dr),ji=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Se=function(){function r(){}return r.fill=function(e,t){for(var n=0,i=e.length;n<i;n++)e[n]=t},r.fillWithin=function(e,t,n,i){r.rangeCheck(e.length,t,n);for(var a=t;a<n;a++)e[a]=i},r.rangeCheck=function(e,t,n){if(t>n)throw new k("fromIndex("+t+") > toIndex("+n+")");if(t<0)throw new jr(t);if(n>e)throw new jr(n)},r.asList=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e},r.create=function(e,t,n){var i=Array.from({length:e});return i.map(function(a){return Array.from({length:t}).fill(n)})},r.createInt32Array=function(e,t,n){var i=Array.from({length:e});return i.map(function(a){return Int32Array.from({length:t}).fill(n)})},r.equals=function(e,t){if(!e||!t||!e.length||!t.length||e.length!==t.length)return!1;for(var n=0,i=e.length;n<i;n++)if(e[n]!==t[n])return!1;return!0},r.hashCode=function(e){var t,n;if(e===null)return 0;var i=1;try{for(var a=ji(e),o=a.next();!o.done;o=a.next()){var s=o.value;i=31*i+s}}catch(u){t={error:u}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i},r.fillUint8Array=function(e,t){for(var n=0;n!==e.length;n++)e[n]=t},r.copyOf=function(e,t){return e.slice(0,t)},r.copyOfUint8Array=function(e,t){if(e.length<=t){var n=new Uint8Array(t);return n.set(e),n}return e.slice(0,t)},r.copyOfRange=function(e,t,n){var i=n-t,a=new Int32Array(i);return ue.arraycopy(e,t,a,0,i),a},r.binarySearch=function(e,t,n){n===void 0&&(n=r.numberComparator);for(var i=0,a=e.length-1;i<=a;){var o=a+i>>1,s=n(t,e[o]);if(s>0)i=o+1;else if(s<0)a=o-1;else return o}return-i-1},r.numberComparator=function(e,t){return e-t},r}(),z=function(){function r(){}return r.numberOfTrailingZeros=function(e){var t;if(e===0)return 32;var n=31;return t=e<<16,t!==0&&(n-=16,e=t),t=e<<8,t!==0&&(n-=8,e=t),t=e<<4,t!==0&&(n-=4,e=t),t=e<<2,t!==0&&(n-=2,e=t),n-(e<<1>>>31)},r.numberOfLeadingZeros=function(e){if(e===0)return 32;var t=1;return e>>>16||(t+=16,e<<=16),e>>>24||(t+=8,e<<=8),e>>>28||(t+=4,e<<=4),e>>>30||(t+=2,e<<=2),t-=e>>>31,t},r.toHexString=function(e){return e.toString(16)},r.toBinaryString=function(e){return String(parseInt(String(e),2))},r.bitCount=function(e){return e=e-(e>>>1&1431655765),e=(e&858993459)+(e>>>2&858993459),e=e+(e>>>4)&252645135,e=e+(e>>>8),e=e+(e>>>16),e&63},r.truncDivision=function(e,t){return Math.trunc(e/t)},r.parseInt=function(e,t){return t===void 0&&(t=void 0),parseInt(e,t)},r.MIN_VALUE_32_BITS=-2147483648,r.MAX_VALUE=Number.MAX_SAFE_INTEGER,r}(),be=function(){function r(e,t){e===void 0?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,t==null?this.bits=r.makeArray(e):this.bits=t)}return r.prototype.getSize=function(){return this.size},r.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},r.prototype.ensureCapacity=function(e){if(e>this.bits.length*32){var t=r.makeArray(e);ue.arraycopy(this.bits,0,t,0,this.bits.length),this.bits=t}},r.prototype.get=function(e){return(this.bits[Math.floor(e/32)]&1<<(e&31))!==0},r.prototype.set=function(e){this.bits[Math.floor(e/32)]|=1<<(e&31)},r.prototype.flip=function(e){this.bits[Math.floor(e/32)]^=1<<(e&31)},r.prototype.getNextSet=function(e){var t=this.size;if(e>=t)return t;var n=this.bits,i=Math.floor(e/32),a=n[i];a&=~((1<<(e&31))-1);for(var o=n.length;a===0;){if(++i===o)return t;a=n[i]}var s=i*32+z.numberOfTrailingZeros(a);return s>t?t:s},r.prototype.getNextUnset=function(e){var t=this.size;if(e>=t)return t;var n=this.bits,i=Math.floor(e/32),a=~n[i];a&=~((1<<(e&31))-1);for(var o=n.length;a===0;){if(++i===o)return t;a=~n[i]}var s=i*32+z.numberOfTrailingZeros(a);return s>t?t:s},r.prototype.setBulk=function(e,t){this.bits[Math.floor(e/32)]=t},r.prototype.setRange=function(e,t){if(t<e||e<0||t>this.size)throw new k;if(t!==e){t--;for(var n=Math.floor(e/32),i=Math.floor(t/32),a=this.bits,o=n;o<=i;o++){var s=o>n?0:e&31,u=o<i?31:t&31,f=(2<<u)-(1<<s);a[o]|=f}}},r.prototype.clear=function(){for(var e=this.bits.length,t=this.bits,n=0;n<e;n++)t[n]=0},r.prototype.isRange=function(e,t,n){if(t<e||e<0||t>this.size)throw new k;if(t===e)return!0;t--;for(var i=Math.floor(e/32),a=Math.floor(t/32),o=this.bits,s=i;s<=a;s++){var u=s>i?0:e&31,f=s<a?31:t&31,c=(2<<f)-(1<<u)&4294967295;if((o[s]&c)!==(n?c:0))return!1}return!0},r.prototype.appendBit=function(e){this.ensureCapacity(this.size+1),e&&(this.bits[Math.floor(this.size/32)]|=1<<(this.size&31)),this.size++},r.prototype.appendBits=function(e,t){if(t<0||t>32)throw new k("Num bits must be between 0 and 32");this.ensureCapacity(this.size+t);for(var n=t;n>0;n--)this.appendBit((e>>n-1&1)===1)},r.prototype.appendBitArray=function(e){var t=e.size;this.ensureCapacity(this.size+t);for(var n=0;n<t;n++)this.appendBit(e.get(n))},r.prototype.xor=function(e){if(this.size!==e.size)throw new k("Sizes don't match");for(var t=this.bits,n=0,i=t.length;n<i;n++)t[n]^=e.bits[n]},r.prototype.toBytes=function(e,t,n,i){for(var a=0;a<i;a++){for(var o=0,s=0;s<8;s++)this.get(e)&&(o|=1<<7-s),e++;t[n+a]=o}},r.prototype.getBitArray=function(){return this.bits},r.prototype.reverse=function(){for(var e=new Int32Array(this.bits.length),t=Math.floor((this.size-1)/32),n=t+1,i=this.bits,a=0;a<n;a++){var o=i[a];o=o>>1&1431655765|(o&1431655765)<<1,o=o>>2&858993459|(o&858993459)<<2,o=o>>4&252645135|(o&252645135)<<4,o=o>>8&16711935|(o&16711935)<<8,o=o>>16&65535|(o&65535)<<16,e[t-a]=o}if(this.size!==n*32){for(var s=n*32-this.size,u=e[0]>>>s,a=1;a<n;a++){var f=e[a];u|=f<<32-s,e[a-1]=u,u=f>>>s}e[n-1]=u}this.bits=e},r.makeArray=function(e){return new Int32Array(Math.floor((e+31)/32))},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.size===t.size&&Se.equals(this.bits,t.bits)},r.prototype.hashCode=function(){return 31*this.size+Se.hashCode(this.bits)},r.prototype.toString=function(){for(var e="",t=0,n=this.size;t<n;t++)t&7||(e+=" "),e+=this.get(t)?"X":".";return e},r.prototype.clone=function(){return new r(this.size,this.bits.slice())},r.prototype.toArray=function(){for(var e=[],t=0,n=this.size;t<n;t++)e.push(this.get(t));return e},r}(),oe;(function(r){r[r.OTHER=0]="OTHER",r[r.PURE_BARCODE=1]="PURE_BARCODE",r[r.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",r[r.TRY_HARDER=3]="TRY_HARDER",r[r.CHARACTER_SET=4]="CHARACTER_SET",r[r.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",r[r.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",r[r.ENABLE_CODE_39_EXTENDED_MODE=7]="ENABLE_CODE_39_EXTENDED_MODE",r[r.ASSUME_GS1=8]="ASSUME_GS1",r[r.RETURN_CODABAR_START_END=9]="RETURN_CODABAR_START_END",r[r.NEED_RESULT_POINT_CALLBACK=10]="NEED_RESULT_POINT_CALLBACK",r[r.ALLOWED_EAN_EXTENSIONS=11]="ALLOWED_EAN_EXTENSIONS"})(oe||(oe={}));var Yi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),N=function(r){Yi(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.getFormatInstance=function(){return new e},e.kind="FormatException",e}($e),Zi=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ee;(function(r){r[r.Cp437=0]="Cp437",r[r.ISO8859_1=1]="ISO8859_1",r[r.ISO8859_2=2]="ISO8859_2",r[r.ISO8859_3=3]="ISO8859_3",r[r.ISO8859_4=4]="ISO8859_4",r[r.ISO8859_5=5]="ISO8859_5",r[r.ISO8859_6=6]="ISO8859_6",r[r.ISO8859_7=7]="ISO8859_7",r[r.ISO8859_8=8]="ISO8859_8",r[r.ISO8859_9=9]="ISO8859_9",r[r.ISO8859_10=10]="ISO8859_10",r[r.ISO8859_11=11]="ISO8859_11",r[r.ISO8859_13=12]="ISO8859_13",r[r.ISO8859_14=13]="ISO8859_14",r[r.ISO8859_15=14]="ISO8859_15",r[r.ISO8859_16=15]="ISO8859_16",r[r.SJIS=16]="SJIS",r[r.Cp1250=17]="Cp1250",r[r.Cp1251=18]="Cp1251",r[r.Cp1252=19]="Cp1252",r[r.Cp1256=20]="Cp1256",r[r.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",r[r.UTF8=22]="UTF8",r[r.ASCII=23]="ASCII",r[r.Big5=24]="Big5",r[r.GB18030=25]="GB18030",r[r.EUC_KR=26]="EUC_KR"})(ee||(ee={}));var me=function(){function r(e,t,n){for(var i,a,o=[],s=3;s<arguments.length;s++)o[s-3]=arguments[s];this.valueIdentifier=e,this.name=n,typeof t=="number"?this.values=Int32Array.from([t]):this.values=t,this.otherEncodingNames=o,r.VALUE_IDENTIFIER_TO_ECI.set(e,this),r.NAME_TO_ECI.set(n,this);for(var u=this.values,f=0,c=u.length;f!==c;f++){var l=u[f];r.VALUES_TO_ECI.set(l,this)}try{for(var h=Zi(o),d=h.next();!d.done;d=h.next()){var v=d.value;r.NAME_TO_ECI.set(v,this)}}catch(g){i={error:g}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}}return r.prototype.getValueIdentifier=function(){return this.valueIdentifier},r.prototype.getName=function(){return this.name},r.prototype.getValue=function(){return this.values[0]},r.getCharacterSetECIByValue=function(e){if(e<0||e>=900)throw new N("incorect value");var t=r.VALUES_TO_ECI.get(e);if(t===void 0)throw new N("incorect value");return t},r.getCharacterSetECIByName=function(e){var t=r.NAME_TO_ECI.get(e);if(t===void 0)throw new N("incorect value");return t},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.getName()===t.getName()},r.VALUE_IDENTIFIER_TO_ECI=new Map,r.VALUES_TO_ECI=new Map,r.NAME_TO_ECI=new Map,r.Cp437=new r(ee.Cp437,Int32Array.from([0,2]),"Cp437"),r.ISO8859_1=new r(ee.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),r.ISO8859_2=new r(ee.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),r.ISO8859_3=new r(ee.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),r.ISO8859_4=new r(ee.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),r.ISO8859_5=new r(ee.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),r.ISO8859_6=new r(ee.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),r.ISO8859_7=new r(ee.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),r.ISO8859_8=new r(ee.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),r.ISO8859_9=new r(ee.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),r.ISO8859_10=new r(ee.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),r.ISO8859_11=new r(ee.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),r.ISO8859_13=new r(ee.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),r.ISO8859_14=new r(ee.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),r.ISO8859_15=new r(ee.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),r.ISO8859_16=new r(ee.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),r.SJIS=new r(ee.SJIS,20,"SJIS","Shift_JIS"),r.Cp1250=new r(ee.Cp1250,21,"Cp1250","windows-1250"),r.Cp1251=new r(ee.Cp1251,22,"Cp1251","windows-1251"),r.Cp1252=new r(ee.Cp1252,23,"Cp1252","windows-1252"),r.Cp1256=new r(ee.Cp1256,24,"Cp1256","windows-1256"),r.UnicodeBigUnmarked=new r(ee.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),r.UTF8=new r(ee.UTF8,26,"UTF8","UTF-8"),r.ASCII=new r(ee.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),r.Big5=new r(ee.Big5,28,"Big5"),r.GB18030=new r(ee.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),r.EUC_KR=new r(ee.EUC_KR,30,"EUC_KR","EUC-KR"),r}(),Ki=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Zt=function(r){Ki(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="UnsupportedOperationException",e}($e),qe=function(){function r(){}return r.decode=function(e,t){var n=this.encodingName(t);return this.customDecoder?this.customDecoder(e,n):typeof TextDecoder>"u"||this.shouldDecodeOnFallback(n)?this.decodeFallback(e,n):new TextDecoder(n).decode(e)},r.shouldDecodeOnFallback=function(e){return!r.isBrowser()&&e==="ISO-8859-1"},r.encode=function(e,t){var n=this.encodingName(t);return this.customEncoder?this.customEncoder(e,n):typeof TextEncoder>"u"?this.encodeFallback(e):new TextEncoder().encode(e)},r.isBrowser=function(){return typeof window<"u"&&{}.toString.call(window)==="[object Window]"},r.encodingName=function(e){return typeof e=="string"?e:e.getName()},r.encodingCharacterSet=function(e){return e instanceof me?e:me.getCharacterSetECIByName(e)},r.decodeFallback=function(e,t){var n=this.encodingCharacterSet(t);if(r.isDecodeFallbackSupported(n)){for(var i="",a=0,o=e.length;a<o;a++){var s=e[a].toString(16);s.length<2&&(s="0"+s),i+="%"+s}return decodeURIComponent(i)}if(n.equals(me.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new Zt("Encoding "+this.encodingName(t)+" not supported by fallback.")},r.isDecodeFallbackSupported=function(e){return e.equals(me.UTF8)||e.equals(me.ISO8859_1)||e.equals(me.ASCII)},r.encodeFallback=function(e){for(var t=btoa(unescape(encodeURIComponent(e))),n=t.split(""),i=[],a=0;a<n.length;a++)i.push(n[a].charCodeAt(0));return new Uint8Array(i)},r}(),$=function(){function r(){}return r.castAsNonUtf8Char=function(e,t){t===void 0&&(t=null);var n=t?t.getName():this.ISO88591;return qe.decode(new Uint8Array([e]),n)},r.guessEncoding=function(e,t){if(t!=null&&t.get(oe.CHARACTER_SET)!==void 0)return t.get(oe.CHARACTER_SET).toString();for(var n=e.length,i=!0,a=!0,o=!0,s=0,u=0,f=0,c=0,l=0,h=0,d=0,v=0,g=0,y=0,_=0,x=e.length>3&&e[0]===239&&e[1]===187&&e[2]===191,A=0;A<n&&(i||a||o);A++){var m=e[A]&255;o&&(s>0?m&128?s--:o=!1:m&128&&(m&64?(s++,m&32?(s++,m&16?(s++,m&8?o=!1:c++):f++):u++):o=!1)),i&&(m>127&&m<160?i=!1:m>159&&(m<192||m===215||m===247)&&_++),a&&(l>0?m<64||m===127||m>252?a=!1:l--:m===128||m===160||m>239?a=!1:m>160&&m<224?(h++,v=0,d++,d>g&&(g=d)):m>127?(l++,d=0,v++,v>y&&(y=v)):(d=0,v=0))}return o&&s>0&&(o=!1),a&&l>0&&(a=!1),o&&(x||u+f+c>0)?r.UTF8:a&&(r.ASSUME_SHIFT_JIS||g>=3||y>=3)?r.SHIFT_JIS:i&&a?g===2&&h===2||_*10>=n?r.SHIFT_JIS:r.ISO88591:i?r.ISO88591:a?r.SHIFT_JIS:o?r.UTF8:r.PLATFORM_DEFAULT_ENCODING},r.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=-1;function a(s,u,f,c,l,h){if(s==="%%")return"%";if(t[++i]!==void 0){s=c?parseInt(c.substr(1)):void 0;var d=l?parseInt(l.substr(1)):void 0,v;switch(h){case"s":v=t[i];break;case"c":v=t[i][0];break;case"f":v=parseFloat(t[i]).toFixed(s);break;case"p":v=parseFloat(t[i]).toPrecision(s);break;case"e":v=parseFloat(t[i]).toExponential(s);break;case"x":v=parseInt(t[i]).toString(d||16);break;case"d":v=parseFloat(parseInt(t[i],d||10).toPrecision(s)).toFixed(0);break}v=typeof v=="object"?JSON.stringify(v):(+v).toString(d);for(var g=parseInt(f),y=f&&f[0]+""=="0"?"0":" ";v.length<g;)v=u!==void 0?v+y:y+v;return v}}var o=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return e.replace(o,a)},r.getBytes=function(e,t){return qe.encode(e,t)},r.getCharCode=function(e,t){return t===void 0&&(t=0),e.charCodeAt(t)},r.getCharAt=function(e){return String.fromCharCode(e)},r.SHIFT_JIS=me.SJIS.getName(),r.GB2312="GB2312",r.ISO88591=me.ISO8859_1.getName(),r.EUC_JP="EUC_JP",r.UTF8=me.UTF8.getName(),r.PLATFORM_DEFAULT_ENCODING=r.UTF8,r.ASSUME_SHIFT_JIS=!1,r}(),X=function(){function r(e){e===void 0&&(e=""),this.value=e}return r.prototype.enableDecoding=function(e){return this.encoding=e,this},r.prototype.append=function(e){return typeof e=="string"?this.value+=e.toString():this.encoding?this.value+=$.castAsNonUtf8Char(e,this.encoding):this.value+=String.fromCharCode(e),this},r.prototype.appendChars=function(e,t,n){for(var i=t;t<t+n;i++)this.append(e[i]);return this},r.prototype.length=function(){return this.value.length},r.prototype.charAt=function(e){return this.value.charAt(e)},r.prototype.deleteCharAt=function(e){this.value=this.value.substr(0,e)+this.value.substring(e+1)},r.prototype.setCharAt=function(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+1)},r.prototype.substring=function(e,t){return this.value.substring(e,t)},r.prototype.setLengthToZero=function(){this.value=""},r.prototype.toString=function(){return this.value},r.prototype.insert=function(e,t){this.value=this.value.substring(0,e)+t+this.value.substring(e)},r}(),it=function(){function r(e,t,n,i){if(this.width=e,this.height=t,this.rowSize=n,this.bits=i,t==null&&(t=e),this.height=t,e<1||t<1)throw new k("Both dimensions must be greater than 0");n==null&&(n=Math.floor((e+31)/32)),this.rowSize=n,i==null&&(this.bits=new Int32Array(this.rowSize*this.height))}return r.parseFromBooleanArray=function(e){for(var t=e.length,n=e[0].length,i=new r(n,t),a=0;a<t;a++)for(var o=e[a],s=0;s<n;s++)o[s]&&i.set(s,a);return i},r.parseFromString=function(e,t,n){if(e===null)throw new k("stringRepresentation cannot be null");for(var i=new Array(e.length),a=0,o=0,s=-1,u=0,f=0;f<e.length;)if(e.charAt(f)===`
`||e.charAt(f)==="\r"){if(a>o){if(s===-1)s=a-o;else if(a-o!==s)throw new k("row lengths do not match");o=a,u++}f++}else if(e.substring(f,f+t.length)===t)f+=t.length,i[a]=!0,a++;else if(e.substring(f,f+n.length)===n)f+=n.length,i[a]=!1,a++;else throw new k("illegal character encountered: "+e.substring(f));if(a>o){if(s===-1)s=a-o;else if(a-o!==s)throw new k("row lengths do not match");u++}for(var c=new r(s,u),l=0;l<a;l++)i[l]&&c.set(Math.floor(l%s),Math.floor(l/s));return c},r.prototype.get=function(e,t){var n=t*this.rowSize+Math.floor(e/32);return(this.bits[n]>>>(e&31)&1)!==0},r.prototype.set=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]|=1<<(e&31)&4294967295},r.prototype.unset=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]&=~(1<<(e&31)&4294967295)},r.prototype.flip=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]^=1<<(e&31)&4294967295},r.prototype.xor=function(e){if(this.width!==e.getWidth()||this.height!==e.getHeight()||this.rowSize!==e.getRowSize())throw new k("input matrix dimensions do not match");for(var t=new be(Math.floor(this.width/32)+1),n=this.rowSize,i=this.bits,a=0,o=this.height;a<o;a++)for(var s=a*n,u=e.getRow(a,t).getBitArray(),f=0;f<n;f++)i[s+f]^=u[f]},r.prototype.clear=function(){for(var e=this.bits,t=e.length,n=0;n<t;n++)e[n]=0},r.prototype.setRegion=function(e,t,n,i){if(t<0||e<0)throw new k("Left and top must be nonnegative");if(i<1||n<1)throw new k("Height and width must be at least 1");var a=e+n,o=t+i;if(o>this.height||a>this.width)throw new k("The region must fit inside the matrix");for(var s=this.rowSize,u=this.bits,f=t;f<o;f++)for(var c=f*s,l=e;l<a;l++)u[c+Math.floor(l/32)]|=1<<(l&31)&4294967295},r.prototype.getRow=function(e,t){t==null||t.getSize()<this.width?t=new be(this.width):t.clear();for(var n=this.rowSize,i=this.bits,a=e*n,o=0;o<n;o++)t.setBulk(o*32,i[a+o]);return t},r.prototype.setRow=function(e,t){ue.arraycopy(t.getBitArray(),0,this.bits,e*this.rowSize,this.rowSize)},r.prototype.rotate180=function(){for(var e=this.getWidth(),t=this.getHeight(),n=new be(e),i=new be(e),a=0,o=Math.floor((t+1)/2);a<o;a++)n=this.getRow(a,n),i=this.getRow(t-1-a,i),n.reverse(),i.reverse(),this.setRow(a,i),this.setRow(t-1-a,n)},r.prototype.getEnclosingRectangle=function(){for(var e=this.width,t=this.height,n=this.rowSize,i=this.bits,a=e,o=t,s=-1,u=-1,f=0;f<t;f++)for(var c=0;c<n;c++){var l=i[f*n+c];if(l!==0){if(f<o&&(o=f),f>u&&(u=f),c*32<a){for(var h=0;!(l<<31-h&4294967295);)h++;c*32+h<a&&(a=c*32+h)}if(c*32+31>s){for(var h=31;!(l>>>h);)h--;c*32+h>s&&(s=c*32+h)}}}return s<a||u<o?null:Int32Array.from([a,o,s-a+1,u-o+1])},r.prototype.getTopLeftOnBit=function(){for(var e=this.rowSize,t=this.bits,n=0;n<t.length&&t[n]===0;)n++;if(n===t.length)return null;for(var i=n/e,a=n%e*32,o=t[n],s=0;!(o<<31-s&4294967295);)s++;return a+=s,Int32Array.from([a,i])},r.prototype.getBottomRightOnBit=function(){for(var e=this.rowSize,t=this.bits,n=t.length-1;n>=0&&t[n]===0;)n--;if(n<0)return null;for(var i=Math.floor(n/e),a=Math.floor(n%e)*32,o=t[n],s=31;!(o>>>s);)s--;return a+=s,Int32Array.from([a,i])},r.prototype.getWidth=function(){return this.width},r.prototype.getHeight=function(){return this.height},r.prototype.getRowSize=function(){return this.rowSize},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.width===t.width&&this.height===t.height&&this.rowSize===t.rowSize&&Se.equals(this.bits,t.bits)},r.prototype.hashCode=function(){var e=this.width;return e=31*e+this.width,e=31*e+this.height,e=31*e+this.rowSize,e=31*e+Se.hashCode(this.bits),e},r.prototype.toString=function(e,t,n){return e===void 0&&(e="X "),t===void 0&&(t="  "),n===void 0&&(n=`
`),this.buildToString(e,t,n)},r.prototype.buildToString=function(e,t,n){for(var i=new X,a=0,o=this.height;a<o;a++){for(var s=0,u=this.width;s<u;s++)i.append(this.get(s,a)?e:t);i.append(n)}return i.toString()},r.prototype.clone=function(){return new r(this.width,this.height,this.rowSize,this.bits.slice())},r}(),qi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),I=function(r){qi(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.getNotFoundInstance=function(){return new e},e.kind="NotFoundException",e}($e),Qi=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ji=function(r){Qi(e,r);function e(t){var n=r.call(this,t)||this;return n.luminances=e.EMPTY,n.buckets=new Int32Array(e.LUMINANCE_BUCKETS),n}return e.prototype.getBlackRow=function(t,n){var i=this.getLuminanceSource(),a=i.getWidth();n==null||n.getSize()<a?n=new be(a):n.clear(),this.initArrays(a);for(var o=i.getRow(t,this.luminances),s=this.buckets,u=0;u<a;u++)s[(o[u]&255)>>e.LUMINANCE_SHIFT]++;var f=e.estimateBlackPoint(s);if(a<3)for(var u=0;u<a;u++)(o[u]&255)<f&&n.set(u);else for(var c=o[0]&255,l=o[1]&255,u=1;u<a-1;u++){var h=o[u+1]&255;(l*4-c-h)/2<f&&n.set(u),c=l,l=h}return n},e.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),n=t.getWidth(),i=t.getHeight(),a=new it(n,i);this.initArrays(n);for(var o=this.buckets,s=1;s<5;s++)for(var u=Math.floor(i*s/5),f=t.getRow(u,this.luminances),c=Math.floor(n*4/5),l=Math.floor(n/5);l<c;l++){var h=f[l]&255;o[h>>e.LUMINANCE_SHIFT]++}for(var d=e.estimateBlackPoint(o),v=t.getMatrix(),s=0;s<i;s++)for(var g=s*n,l=0;l<n;l++){var h=v[g+l]&255;h<d&&a.set(l,s)}return a},e.prototype.createBinarizer=function(t){return new e(t)},e.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var n=this.buckets,i=0;i<e.LUMINANCE_BUCKETS;i++)n[i]=0},e.estimateBlackPoint=function(t){for(var n=t.length,i=0,a=0,o=0,s=0;s<n;s++)t[s]>o&&(a=s,o=t[s]),t[s]>i&&(i=t[s]);for(var u=0,f=0,s=0;s<n;s++){var c=s-a,l=t[s]*c*c;l>f&&(u=s,f=l)}if(a>u){var h=a;a=u,u=h}if(u-a<=n/16)throw new I;for(var d=u-1,v=-1,s=u-1;s>a;s--){var g=s-a,l=g*g*(u-s)*(i-t[s]);l>v&&(d=s,v=l)}return d<<e.LUMINANCE_SHIFT},e.LUMINANCE_BITS=5,e.LUMINANCE_SHIFT=8-e.LUMINANCE_BITS,e.LUMINANCE_BUCKETS=1<<e.LUMINANCE_BITS,e.EMPTY=Uint8ClampedArray.from([0]),e}(Wi),ea=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Dn=function(r){ea(e,r);function e(t){var n=r.call(this,t)||this;return n.matrix=null,n}return e.prototype.getBlackMatrix=function(){if(this.matrix!==null)return this.matrix;var t=this.getLuminanceSource(),n=t.getWidth(),i=t.getHeight();if(n>=e.MINIMUM_DIMENSION&&i>=e.MINIMUM_DIMENSION){var a=t.getMatrix(),o=n>>e.BLOCK_SIZE_POWER;n&e.BLOCK_SIZE_MASK&&o++;var s=i>>e.BLOCK_SIZE_POWER;i&e.BLOCK_SIZE_MASK&&s++;var u=e.calculateBlackPoints(a,o,s,n,i),f=new it(n,i);e.calculateThresholdForBlock(a,o,s,n,i,u,f),this.matrix=f}else this.matrix=r.prototype.getBlackMatrix.call(this);return this.matrix},e.prototype.createBinarizer=function(t){return new e(t)},e.calculateThresholdForBlock=function(t,n,i,a,o,s,u){for(var f=o-e.BLOCK_SIZE,c=a-e.BLOCK_SIZE,l=0;l<i;l++){var h=l<<e.BLOCK_SIZE_POWER;h>f&&(h=f);for(var d=e.cap(l,2,i-3),v=0;v<n;v++){var g=v<<e.BLOCK_SIZE_POWER;g>c&&(g=c);for(var y=e.cap(v,2,n-3),_=0,x=-2;x<=2;x++){var A=s[d+x];_+=A[y-2]+A[y-1]+A[y]+A[y+1]+A[y+2]}var m=_/25;e.thresholdBlock(t,g,h,m,a,u)}}},e.cap=function(t,n,i){return t<n?n:t>i?i:t},e.thresholdBlock=function(t,n,i,a,o,s){for(var u=0,f=i*o+n;u<e.BLOCK_SIZE;u++,f+=o)for(var c=0;c<e.BLOCK_SIZE;c++)(t[f+c]&255)<=a&&s.set(n+c,i+u)},e.calculateBlackPoints=function(t,n,i,a,o){for(var s=o-e.BLOCK_SIZE,u=a-e.BLOCK_SIZE,f=new Array(i),c=0;c<i;c++){f[c]=new Int32Array(n);var l=c<<e.BLOCK_SIZE_POWER;l>s&&(l=s);for(var h=0;h<n;h++){var d=h<<e.BLOCK_SIZE_POWER;d>u&&(d=u);for(var v=0,g=255,y=0,_=0,x=l*a+d;_<e.BLOCK_SIZE;_++,x+=a){for(var A=0;A<e.BLOCK_SIZE;A++){var m=t[x+A]&255;v+=m,m<g&&(g=m),m>y&&(y=m)}if(y-g>e.MIN_DYNAMIC_RANGE)for(_++,x+=a;_<e.BLOCK_SIZE;_++,x+=a)for(var A=0;A<e.BLOCK_SIZE;A++)v+=t[x+A]&255}var S=v>>e.BLOCK_SIZE_POWER*2;if(y-g<=e.MIN_DYNAMIC_RANGE&&(S=g/2,c>0&&h>0)){var O=(f[c-1][h]+2*f[c][h-1]+f[c-1][h-1])/4;g<O&&(S=O)}f[c][h]=S}}return f},e.BLOCK_SIZE_POWER=3,e.BLOCK_SIZE=1<<e.BLOCK_SIZE_POWER,e.BLOCK_SIZE_MASK=e.BLOCK_SIZE-1,e.MINIMUM_DIMENSION=e.BLOCK_SIZE*5,e.MIN_DYNAMIC_RANGE=24,e}(Ji),Ht=function(){function r(e,t){this.width=e,this.height=t}return r.prototype.getWidth=function(){return this.width},r.prototype.getHeight=function(){return this.height},r.prototype.isCropSupported=function(){return!1},r.prototype.crop=function(e,t,n,i){throw new Zt("This luminance source does not support cropping.")},r.prototype.isRotateSupported=function(){return!1},r.prototype.rotateCounterClockwise=function(){throw new Zt("This luminance source does not support rotation by 90 degrees.")},r.prototype.rotateCounterClockwise45=function(){throw new Zt("This luminance source does not support rotation by 45 degrees.")},r.prototype.toString=function(){for(var e=new Uint8ClampedArray(this.width),t=new X,n=0;n<this.height;n++){for(var i=this.getRow(n,e),a=0;a<this.width;a++){var o=i[a]&255,s=void 0;o<64?s="#":o<128?s="+":o<192?s=".":s=" ",t.append(s)}t.append(`
`)}return t.toString()},r}(),ta=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ir=function(r){ta(e,r);function e(t){var n=r.call(this,t.getWidth(),t.getHeight())||this;return n.delegate=t,n}return e.prototype.getRow=function(t,n){for(var i=this.delegate.getRow(t,n),a=this.getWidth(),o=0;o<a;o++)i[o]=255-(i[o]&255);return i},e.prototype.getMatrix=function(){for(var t=this.delegate.getMatrix(),n=this.getWidth()*this.getHeight(),i=new Uint8ClampedArray(n),a=0;a<n;a++)i[a]=255-(t[a]&255);return i},e.prototype.isCropSupported=function(){return this.delegate.isCropSupported()},e.prototype.crop=function(t,n,i,a){return new e(this.delegate.crop(t,n,i,a))},e.prototype.isRotateSupported=function(){return this.delegate.isRotateSupported()},e.prototype.invert=function(){return this.delegate},e.prototype.rotateCounterClockwise=function(){return new e(this.delegate.rotateCounterClockwise())},e.prototype.rotateCounterClockwise45=function(){return new e(this.delegate.rotateCounterClockwise45())},e}(Ht),ra=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),na=function(r){ra(e,r);function e(t,n){n===void 0&&(n=!1);var i=r.call(this,t.width,t.height)||this;return i.canvas=t,i.tempCanvasElement=null,i.buffer=e.makeBufferFromCanvasImageData(t,n),i}return e.makeBufferFromCanvasImageData=function(t,n){n===void 0&&(n=!1);var i=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(i.data,t.width,t.height,n)},e.toGrayscaleBuffer=function(t,n,i,a){a===void 0&&(a=!1);var o=new Uint8ClampedArray(n*i);if(e.FRAME_INDEX=!e.FRAME_INDEX,e.FRAME_INDEX||!a)for(var s=0,u=0,f=t.length;s<f;s+=4,u++){var c=void 0,l=t[s+3];if(l===0)c=255;else{var h=t[s],d=t[s+1],v=t[s+2];c=306*h+601*d+117*v+512>>10}o[u]=c}else for(var s=0,u=0,g=t.length;s<g;s+=4,u++){var c=void 0,l=t[s+3];if(l===0)c=255;else{var h=t[s],d=t[s+1],v=t[s+2];c=306*h+601*d+117*v+512>>10}o[u]=255-c}return o},e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new k("Requested row is outside the image: "+t);var i=this.getWidth(),a=t*i;return n===null?n=this.buffer.slice(a,a+i):(n.length<i&&(n=new Uint8ClampedArray(i)),n.set(this.buffer.slice(a,a+i))),n},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return r.prototype.crop.call(this,t,n,i,a),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(this.tempCanvasElement===null){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement(),i=n.getContext("2d"),a=t*e.DEGREE_TO_RADIANS,o=this.canvas.width,s=this.canvas.height,u=Math.ceil(Math.abs(Math.cos(a))*o+Math.abs(Math.sin(a))*s),f=Math.ceil(Math.abs(Math.sin(a))*o+Math.abs(Math.cos(a))*s);return n.width=u,n.height=f,i.translate(u/2,f/2),i.rotate(a),i.drawImage(this.canvas,o/-2,s/-2),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.prototype.invert=function(){return new ir(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e.FRAME_INDEX=!0,e}(Ht),ia=function(){function r(e,t,n){this.deviceId=e,this.label=t,this.kind="videoinput",this.groupId=n||void 0}return r.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},r}(),Ue=function(r,e,t,n){function i(a){return a instanceof t?a:new t(function(o){o(a)})}return new(t||(t=Promise))(function(a,o){function s(c){try{f(n.next(c))}catch(l){o(l)}}function u(c){try{f(n.throw(c))}catch(l){o(l)}}function f(c){c.done?a(c.value):i(c.value).then(s,u)}f((n=n.apply(r,e||[])).next())})},Ve=function(r,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(f){return function(c){return u([f,c])}}function u(f){if(n)throw new TypeError("Generator is already executing.");for(;t;)try{if(n=1,i&&(a=f[0]&2?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[f[0]&2,a.value]),f[0]){case 0:case 1:a=f;break;case 4:return t.label++,{value:f[1],done:!1};case 5:t.label++,i=f[1],f=[0];continue;case 7:f=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(f[0]===6||f[0]===2)){t=0;continue}if(f[0]===3&&(!a||f[1]>a[0]&&f[1]<a[3])){t.label=f[1];break}if(f[0]===6&&t.label<a[1]){t.label=a[1],a=f;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(f);break}a[2]&&t.ops.pop(),t.trys.pop();continue}f=e.call(r,t)}catch(c){f=[6,c],i=0}finally{n=a=0}if(f[0]&5)throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}},aa=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Dt=function(){function r(e,t,n){t===void 0&&(t=500),this.reader=e,this.timeBetweenScansMillis=t,this._hints=n,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(r.prototype,"hasNavigator",{get:function(){return typeof navigator<"u"},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"canEnumerateDevices",{get:function(){return!!(this.isMediaDevicesSuported&&navigator.mediaDevices.enumerateDevices)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(e){this._timeBetweenDecodingAttempts=e<0?0:e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"hints",{get:function(){return this._hints},set:function(e){this._hints=e||null},enumerable:!1,configurable:!0}),r.prototype.listVideoInputDevices=function(){return Ue(this,void 0,void 0,function(){var e,t,n,i,a,o,s,u,f,c,l,h;return Ve(this,function(d){switch(d.label){case 0:if(!this.hasNavigator)throw new Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:e=d.sent(),t=[];try{for(n=aa(e),i=n.next();!i.done;i=n.next())a=i.value,o=a.kind==="video"?"videoinput":a.kind,o==="videoinput"&&(s=a.deviceId||a.id,u=a.label||"Video device "+(t.length+1),f=a.groupId,c={deviceId:s,label:u,kind:o,groupId:f},t.push(c))}catch(v){l={error:v}}finally{try{i&&!i.done&&(h=n.return)&&h.call(n)}finally{if(l)throw l.error}}return[2,t]}})})},r.prototype.getVideoInputDevices=function(){return Ue(this,void 0,void 0,function(){var e;return Ve(this,function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return e=t.sent(),[2,e.map(function(n){return new ia(n.deviceId,n.label)})]}})})},r.prototype.findDeviceById=function(e){return Ue(this,void 0,void 0,function(){var t;return Ve(this,function(n){switch(n.label){case 0:return[4,this.listVideoInputDevices()];case 1:return t=n.sent(),t?[2,t.find(function(i){return i.deviceId===e})]:[2,null]}})})},r.prototype.decodeFromInputVideoDevice=function(e,t){return Ue(this,void 0,void 0,function(){return Ve(this,function(n){switch(n.label){case 0:return[4,this.decodeOnceFromVideoDevice(e,t)];case 1:return[2,n.sent()]}})})},r.prototype.decodeOnceFromVideoDevice=function(e,t){return Ue(this,void 0,void 0,function(){var n,i;return Ve(this,function(a){switch(a.label){case 0:return this.reset(),e?n={deviceId:{exact:e}}:n={facingMode:"environment"},i={video:n},[4,this.decodeOnceFromConstraints(i,t)];case 1:return[2,a.sent()]}})})},r.prototype.decodeOnceFromConstraints=function(e,t){return Ue(this,void 0,void 0,function(){var n;return Ve(this,function(i){switch(i.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return n=i.sent(),[4,this.decodeOnceFromStream(n,t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromStream=function(e,t){return Ue(this,void 0,void 0,function(){var n,i;return Ve(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.attachStreamToVideo(e,t)];case 1:return n=a.sent(),[4,this.decodeOnce(n)];case 2:return i=a.sent(),[2,i]}})})},r.prototype.decodeFromInputVideoDeviceContinuously=function(e,t,n){return Ue(this,void 0,void 0,function(){return Ve(this,function(i){switch(i.label){case 0:return[4,this.decodeFromVideoDevice(e,t,n)];case 1:return[2,i.sent()]}})})},r.prototype.decodeFromVideoDevice=function(e,t,n){return Ue(this,void 0,void 0,function(){var i,a;return Ve(this,function(o){switch(o.label){case 0:return e?i={deviceId:{exact:e}}:i={facingMode:"environment"},a={video:i},[4,this.decodeFromConstraints(a,t,n)];case 1:return[2,o.sent()]}})})},r.prototype.decodeFromConstraints=function(e,t,n){return Ue(this,void 0,void 0,function(){var i;return Ve(this,function(a){switch(a.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return i=a.sent(),[4,this.decodeFromStream(i,t,n)];case 2:return[2,a.sent()]}})})},r.prototype.decodeFromStream=function(e,t,n){return Ue(this,void 0,void 0,function(){var i;return Ve(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.attachStreamToVideo(e,t)];case 1:return i=a.sent(),[4,this.decodeContinuously(i,n)];case 2:return[2,a.sent()]}})})},r.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},r.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},r.prototype.attachStreamToVideo=function(e,t){return Ue(this,void 0,void 0,function(){var n;return Ve(this,function(i){switch(i.label){case 0:return n=this.prepareVideoElement(t),this.addVideoSource(n,e),this.videoElement=n,this.stream=e,[4,this.playVideoOnLoadAsync(n)];case 1:return i.sent(),[2,n]}})})},r.prototype.playVideoOnLoadAsync=function(e){var t=this;return new Promise(function(n,i){return t.playVideoOnLoad(e,function(){return n()})})},r.prototype.playVideoOnLoad=function(e,t){var n=this;this.videoEndedListener=function(){return n.stopStreams()},this.videoCanPlayListener=function(){return n.tryPlayVideo(e)},e.addEventListener("ended",this.videoEndedListener),e.addEventListener("canplay",this.videoCanPlayListener),e.addEventListener("playing",t),this.tryPlayVideo(e)},r.prototype.isVideoPlaying=function(e){return e.currentTime>0&&!e.paused&&!e.ended&&e.readyState>2},r.prototype.tryPlayVideo=function(e){return Ue(this,void 0,void 0,function(){return Ve(this,function(t){switch(t.label){case 0:if(this.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,e.play()];case 2:return t.sent(),[3,4];case 3:return t.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}})})},r.prototype.getMediaElement=function(e,t){var n=document.getElementById(e);if(!n)throw new ye("element with id '"+e+"' not found");if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new ye("element with id '"+e+"' must be an "+t+" element");return n},r.prototype.decodeFromImage=function(e,t){if(!e&&!t)throw new ye("either imageElement with a src set or an url must be provided");return t&&!e?this.decodeFromImageUrl(t):this.decodeFromImageElement(e)},r.prototype.decodeFromVideo=function(e,t){if(!e&&!t)throw new ye("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrl(t):this.decodeFromVideoElement(e)},r.prototype.decodeFromVideoContinuously=function(e,t,n){if(e===void 0&&t===void 0)throw new ye("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrlContinuously(t,n):this.decodeFromVideoElementContinuously(e,n)},r.prototype.decodeFromImageElement=function(e){if(!e)throw new ye("An image element must be provided.");this.reset();var t=this.prepareImageElement(e);this.imageElement=t;var n;return this.isImageLoaded(t)?n=this.decodeOnce(t,!1,!0):n=this._decodeOnLoadImage(t),n},r.prototype.decodeFromVideoElement=function(e){var t=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideo(t)},r.prototype.decodeFromVideoElementContinuously=function(e,t){var n=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideoContinuously(n,t)},r.prototype._decodeFromVideoElementSetup=function(e){if(!e)throw new ye("A video element must be provided.");this.reset();var t=this.prepareVideoElement(e);return this.videoElement=t,t},r.prototype.decodeFromImageUrl=function(e){if(!e)throw new ye("An URL must be provided.");this.reset();var t=this.prepareImageElement();this.imageElement=t;var n=this._decodeOnLoadImage(t);return t.src=e,n},r.prototype.decodeFromVideoUrl=function(e){if(!e)throw new ye("An URL must be provided.");this.reset();var t=this.prepareVideoElement(),n=this.decodeFromVideoElement(t);return t.src=e,n},r.prototype.decodeFromVideoUrlContinuously=function(e,t){if(!e)throw new ye("An URL must be provided.");this.reset();var n=this.prepareVideoElement(),i=this.decodeFromVideoElementContinuously(n,t);return n.src=e,i},r.prototype._decodeOnLoadImage=function(e){var t=this;return new Promise(function(n,i){t.imageLoadedListener=function(){return t.decodeOnce(e,!1,!0).then(n,i)},e.addEventListener("load",t.imageLoadedListener)})},r.prototype._decodeOnLoadVideo=function(e){return Ue(this,void 0,void 0,function(){return Ve(this,function(t){switch(t.label){case 0:return[4,this.playVideoOnLoadAsync(e)];case 1:return t.sent(),[4,this.decodeOnce(e)];case 2:return[2,t.sent()]}})})},r.prototype._decodeOnLoadVideoContinuously=function(e,t){return Ue(this,void 0,void 0,function(){return Ve(this,function(n){switch(n.label){case 0:return[4,this.playVideoOnLoadAsync(e)];case 1:return n.sent(),this.decodeContinuously(e,t),[2]}})})},r.prototype.isImageLoaded=function(e){return!(!e.complete||e.naturalWidth===0)},r.prototype.prepareImageElement=function(e){var t;return typeof e>"u"&&(t=document.createElement("img"),t.width=200,t.height=200),typeof e=="string"&&(t=this.getMediaElement(e,"img")),e instanceof HTMLImageElement&&(t=e),t},r.prototype.prepareVideoElement=function(e){var t;return!e&&typeof document<"u"&&(t=document.createElement("video"),t.width=200,t.height=200),typeof e=="string"&&(t=this.getMediaElement(e,"video")),e instanceof HTMLVideoElement&&(t=e),t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t},r.prototype.decodeOnce=function(e,t,n){var i=this;t===void 0&&(t=!0),n===void 0&&(n=!0),this._stopAsyncDecode=!1;var a=function(o,s){if(i._stopAsyncDecode){s(new I("Video stream has ended before any code could be detected.")),i._stopAsyncDecode=void 0;return}try{var u=i.decode(e);o(u)}catch(h){var f=t&&h instanceof I,c=h instanceof _e||h instanceof N,l=c&&n;if(f||l)return setTimeout(a,i._timeBetweenDecodingAttempts,o,s);s(h)}};return new Promise(function(o,s){return a(o,s)})},r.prototype.decodeContinuously=function(e,t){var n=this;this._stopContinuousDecode=!1;var i=function(){if(n._stopContinuousDecode){n._stopContinuousDecode=void 0;return}try{var a=n.decode(e);t(a,null),setTimeout(i,n.timeBetweenScansMillis)}catch(u){t(null,u);var o=u instanceof _e||u instanceof N,s=u instanceof I;(o||s)&&setTimeout(i,n._timeBetweenDecodingAttempts)}};i()},r.prototype.decode=function(e){var t=this.createBinaryBitmap(e);return this.decodeBitmap(t)},r.prototype.createBinaryBitmap=function(e){this.getCaptureCanvasContext(e);var t=!1;e instanceof HTMLVideoElement?(this.drawFrameOnCanvas(e),t=!0):this.drawImageOnCanvas(e);var n=this.getCaptureCanvas(e),i=new na(n,t),a=new Dn(i);return new bn(a)},r.prototype.getCaptureCanvasContext=function(e){if(!this.captureCanvasContext){var t=this.getCaptureCanvas(e),n=void 0;try{n=t.getContext("2d",{willReadFrequently:!0})}catch{n=t.getContext("2d")}this.captureCanvasContext=n}return this.captureCanvasContext},r.prototype.getCaptureCanvas=function(e){if(!this.captureCanvas){var t=this.createCaptureCanvas(e);this.captureCanvas=t}return this.captureCanvas},r.prototype.drawFrameOnCanvas=function(e,t,n){t===void 0&&(t={sx:0,sy:0,sWidth:e.videoWidth,sHeight:e.videoHeight,dx:0,dy:0,dWidth:e.videoWidth,dHeight:e.videoHeight}),n===void 0&&(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)},r.prototype.drawImageOnCanvas=function(e,t,n){t===void 0&&(t={sx:0,sy:0,sWidth:e.naturalWidth,sHeight:e.naturalHeight,dx:0,dy:0,dWidth:e.naturalWidth,dHeight:e.naturalHeight}),n===void 0&&(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)},r.prototype.decodeBitmap=function(e){return this.reader.decode(e,this._hints)},r.prototype.createCaptureCanvas=function(e){if(typeof document>"u")return this._destroyCaptureCanvas(),null;var t=document.createElement("canvas"),n,i;return typeof e<"u"&&(e instanceof HTMLVideoElement?(n=e.videoWidth,i=e.videoHeight):e instanceof HTMLImageElement&&(n=e.naturalWidth||e.width,i=e.naturalHeight||e.height)),t.style.width=n+"px",t.style.height=i+"px",t.width=n,t.height=i,t},r.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach(function(e){return e.stop()}),this.stream=void 0),this._stopAsyncDecode===!1&&this.stopAsyncDecode(),this._stopContinuousDecode===!1&&this.stopContinuousDecode()},r.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},r.prototype._destroyVideoElement=function(){this.videoElement&&(typeof this.videoEndedListener<"u"&&this.videoElement.removeEventListener("ended",this.videoEndedListener),typeof this.videoPlayingEventListener<"u"&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),typeof this.videoCanPlayListener<"u"&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},r.prototype._destroyImageElement=function(){this.imageElement&&(this.imageLoadedListener!==void 0&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},r.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},r.prototype.addVideoSource=function(e,t){try{e.srcObject=t}catch{e.src=URL.createObjectURL(t)}},r.prototype.cleanVideoSource=function(e){try{e.srcObject=null}catch{e.src=""}this.videoElement.removeAttribute("src")},r}(),ke=function(){function r(e,t,n,i,a,o){n===void 0&&(n=t==null?0:8*t.length),o===void 0&&(o=ue.currentTimeMillis()),this.text=e,this.rawBytes=t,this.numBits=n,this.resultPoints=i,this.format=a,this.timestamp=o,this.text=e,this.rawBytes=t,n==null?this.numBits=t==null?0:8*t.length:this.numBits=n,this.resultPoints=i,this.format=a,this.resultMetadata=null,o==null?this.timestamp=ue.currentTimeMillis():this.timestamp=o}return r.prototype.getText=function(){return this.text},r.prototype.getRawBytes=function(){return this.rawBytes},r.prototype.getNumBits=function(){return this.numBits},r.prototype.getResultPoints=function(){return this.resultPoints},r.prototype.getBarcodeFormat=function(){return this.format},r.prototype.getResultMetadata=function(){return this.resultMetadata},r.prototype.putMetadata=function(e,t){this.resultMetadata===null&&(this.resultMetadata=new Map),this.resultMetadata.set(e,t)},r.prototype.putAllMetadata=function(e){e!==null&&(this.resultMetadata===null?this.resultMetadata=e:this.resultMetadata=new Map(e))},r.prototype.addResultPoints=function(e){var t=this.resultPoints;if(t===null)this.resultPoints=e;else if(e!==null&&e.length>0){var n=new Array(t.length+e.length);ue.arraycopy(t,0,n,0,t.length),ue.arraycopy(e,0,n,t.length,e.length),this.resultPoints=n}},r.prototype.getTimestamp=function(){return this.timestamp},r.prototype.toString=function(){return this.text},r}(),_r;(function(r){r[r.AZTEC=0]="AZTEC",r[r.CODABAR=1]="CODABAR",r[r.CODE_39=2]="CODE_39",r[r.CODE_93=3]="CODE_93",r[r.CODE_128=4]="CODE_128",r[r.DATA_MATRIX=5]="DATA_MATRIX",r[r.EAN_8=6]="EAN_8",r[r.EAN_13=7]="EAN_13",r[r.ITF=8]="ITF",r[r.MAXICODE=9]="MAXICODE",r[r.PDF_417=10]="PDF_417",r[r.QR_CODE=11]="QR_CODE",r[r.RSS_14=12]="RSS_14",r[r.RSS_EXPANDED=13]="RSS_EXPANDED",r[r.UPC_A=14]="UPC_A",r[r.UPC_E=15]="UPC_E",r[r.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"})(_r||(_r={}));const L=_r;var wr;(function(r){r[r.OTHER=0]="OTHER",r[r.ORIENTATION=1]="ORIENTATION",r[r.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",r[r.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",r[r.ISSUE_NUMBER=4]="ISSUE_NUMBER",r[r.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",r[r.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",r[r.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",r[r.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",r[r.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",r[r.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"})(wr||(wr={}));const Me=wr;var ar=function(){function r(e,t,n,i,a,o){a===void 0&&(a=-1),o===void 0&&(o=-1),this.rawBytes=e,this.text=t,this.byteSegments=n,this.ecLevel=i,this.structuredAppendSequenceNumber=a,this.structuredAppendParity=o,this.numBits=e==null?0:8*e.length}return r.prototype.getRawBytes=function(){return this.rawBytes},r.prototype.getNumBits=function(){return this.numBits},r.prototype.setNumBits=function(e){this.numBits=e},r.prototype.getText=function(){return this.text},r.prototype.getByteSegments=function(){return this.byteSegments},r.prototype.getECLevel=function(){return this.ecLevel},r.prototype.getErrorsCorrected=function(){return this.errorsCorrected},r.prototype.setErrorsCorrected=function(e){this.errorsCorrected=e},r.prototype.getErasures=function(){return this.erasures},r.prototype.setErasures=function(e){this.erasures=e},r.prototype.getOther=function(){return this.other},r.prototype.setOther=function(e){this.other=e},r.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},r.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},r.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},r}(),kt=function(){function r(){}return r.prototype.exp=function(e){return this.expTable[e]},r.prototype.log=function(e){if(e===0)throw new k;return this.logTable[e]},r.addOrSubtract=function(e,t){return e^t},r}(),ft=function(){function r(e,t){if(t.length===0)throw new k;this.field=e;var n=t.length;if(n>1&&t[0]===0){for(var i=1;i<n&&t[i]===0;)i++;i===n?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(n-i),ue.arraycopy(t,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}return r.prototype.getCoefficients=function(){return this.coefficients},r.prototype.getDegree=function(){return this.coefficients.length-1},r.prototype.isZero=function(){return this.coefficients[0]===0},r.prototype.getCoefficient=function(e){return this.coefficients[this.coefficients.length-1-e]},r.prototype.evaluateAt=function(e){if(e===0)return this.getCoefficient(0);var t=this.coefficients,n;if(e===1){n=0;for(var i=0,a=t.length;i!==a;i++){var o=t[i];n=kt.addOrSubtract(n,o)}return n}n=t[0];for(var s=t.length,u=this.field,i=1;i<s;i++)n=kt.addOrSubtract(u.multiply(e,n),t[i]);return n},r.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new k("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var t=this.coefficients,n=e.coefficients;if(t.length>n.length){var i=t;t=n,n=i}var a=new Int32Array(n.length),o=n.length-t.length;ue.arraycopy(n,0,a,0,o);for(var s=o;s<n.length;s++)a[s]=kt.addOrSubtract(t[s-o],n[s]);return new r(this.field,a)},r.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new k("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var t=this.coefficients,n=t.length,i=e.coefficients,a=i.length,o=new Int32Array(n+a-1),s=this.field,u=0;u<n;u++)for(var f=t[u],c=0;c<a;c++)o[u+c]=kt.addOrSubtract(o[u+c],s.multiply(f,i[c]));return new r(s,o)},r.prototype.multiplyScalar=function(e){if(e===0)return this.field.getZero();if(e===1)return this;for(var t=this.coefficients.length,n=this.field,i=new Int32Array(t),a=this.coefficients,o=0;o<t;o++)i[o]=n.multiply(a[o],e);return new r(n,i)},r.prototype.multiplyByMonomial=function(e,t){if(e<0)throw new k;if(t===0)return this.field.getZero();for(var n=this.coefficients,i=n.length,a=new Int32Array(i+e),o=this.field,s=0;s<i;s++)a[s]=o.multiply(n[s],t);return new r(o,a)},r.prototype.divide=function(e){if(!this.field.equals(e.field))throw new k("GenericGFPolys do not have same GenericGF field");if(e.isZero())throw new k("Divide by 0");for(var t=this.field,n=t.getZero(),i=this,a=e.getCoefficient(e.getDegree()),o=t.inverse(a);i.getDegree()>=e.getDegree()&&!i.isZero();){var s=i.getDegree()-e.getDegree(),u=t.multiply(i.getCoefficient(i.getDegree()),o),f=e.multiplyByMonomial(s,u),c=t.buildMonomial(s,u);n=n.addOrSubtract(c),i=i.addOrSubtract(f)}return[n,i]},r.prototype.toString=function(){for(var e="",t=this.getDegree();t>=0;t--){var n=this.getCoefficient(t);if(n!==0){if(n<0?(e+=" - ",n=-n):e.length>0&&(e+=" + "),t===0||n!==1){var i=this.field.log(n);i===0?e+="1":i===1?e+="a":(e+="a^",e+=i)}t!==0&&(t===1?e+="x":(e+="x^",e+=t))}}return e},r}(),oa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Rn=function(r){oa(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ArithmeticException",e}($e),sa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ze=function(r){sa(e,r);function e(t,n,i){var a=r.call(this)||this;a.primitive=t,a.size=n,a.generatorBase=i;for(var o=new Int32Array(n),s=1,u=0;u<n;u++)o[u]=s,s*=2,s>=n&&(s^=t,s&=n-1);a.expTable=o;for(var f=new Int32Array(n),u=0;u<n-1;u++)f[o[u]]=u;return a.logTable=f,a.zero=new ft(a,Int32Array.from([0])),a.one=new ft(a,Int32Array.from([1])),a}return e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,n){if(t<0)throw new k;if(n===0)return this.zero;var i=new Int32Array(t+1);return i[0]=n,new ft(this,i)},e.prototype.inverse=function(t){if(t===0)throw new Rn;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,n){return t===0||n===0?0:this.expTable[(this.logTable[t]+this.logTable[n])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+z.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e}(kt),ua=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Wt=function(r){ua(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ReedSolomonException",e}($e),fa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Gt=function(r){fa(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="IllegalStateException",e}($e),or=function(){function r(e){this.field=e}return r.prototype.decode=function(e,t){for(var n=this.field,i=new ft(n,e),a=new Int32Array(t),o=!0,s=0;s<t;s++){var u=i.evaluateAt(n.exp(s+n.getGeneratorBase()));a[a.length-1-s]=u,u!==0&&(o=!1)}if(!o)for(var f=new ft(n,a),c=this.runEuclideanAlgorithm(n.buildMonomial(t,1),f,t),l=c[0],h=c[1],d=this.findErrorLocations(l),v=this.findErrorMagnitudes(h,d),s=0;s<d.length;s++){var g=e.length-1-n.log(d[s]);if(g<0)throw new Wt("Bad error location");e[g]=ze.addOrSubtract(e[g],v[s])}},r.prototype.runEuclideanAlgorithm=function(e,t,n){if(e.getDegree()<t.getDegree()){var i=e;e=t,t=i}for(var a=this.field,o=e,s=t,u=a.getZero(),f=a.getOne();s.getDegree()>=(n/2|0);){var c=o,l=u;if(o=s,u=f,o.isZero())throw new Wt("r_{i-1} was zero");s=c;for(var h=a.getZero(),d=o.getCoefficient(o.getDegree()),v=a.inverse(d);s.getDegree()>=o.getDegree()&&!s.isZero();){var g=s.getDegree()-o.getDegree(),y=a.multiply(s.getCoefficient(s.getDegree()),v);h=h.addOrSubtract(a.buildMonomial(g,y)),s=s.addOrSubtract(o.multiplyByMonomial(g,y))}if(f=h.multiply(u).addOrSubtract(l),s.getDegree()>=o.getDegree())throw new Gt("Division algorithm failed to reduce polynomial?")}var _=f.getCoefficient(0);if(_===0)throw new Wt("sigmaTilde(0) was zero");var x=a.inverse(_),A=f.multiplyScalar(x),m=s.multiplyScalar(x);return[A,m]},r.prototype.findErrorLocations=function(e){var t=e.getDegree();if(t===1)return Int32Array.from([e.getCoefficient(1)]);for(var n=new Int32Array(t),i=0,a=this.field,o=1;o<a.getSize()&&i<t;o++)e.evaluateAt(o)===0&&(n[i]=a.inverse(o),i++);if(i!==t)throw new Wt("Error locator degree does not match number of roots");return n},r.prototype.findErrorMagnitudes=function(e,t){for(var n=t.length,i=new Int32Array(n),a=this.field,o=0;o<n;o++){for(var s=a.inverse(t[o]),u=1,f=0;f<n;f++)if(o!==f){var c=a.multiply(t[f],s),l=c&1?c&-2:c|1;u=a.multiply(u,l)}i[o]=a.multiply(e.evaluateAt(s),a.inverse(u)),a.getGeneratorBase()!==0&&(i[o]=a.multiply(i[o],s))}return i},r}(),Re;(function(r){r[r.UPPER=0]="UPPER",r[r.LOWER=1]="LOWER",r[r.MIXED=2]="MIXED",r[r.DIGIT=3]="DIGIT",r[r.PUNCT=4]="PUNCT",r[r.BINARY=5]="BINARY"})(Re||(Re={}));var Yr=function(){function r(){}return r.prototype.decode=function(e){this.ddata=e;var t=e.getBits(),n=this.extractBits(t),i=this.correctBits(n),a=r.convertBoolArrayToByteArray(i),o=r.getEncodedData(i),s=new ar(a,o,null,null);return s.setNumBits(i.length),s},r.highLevelDecode=function(e){return this.getEncodedData(e)},r.getEncodedData=function(e){for(var t=e.length,n=Re.UPPER,i=Re.UPPER,a="",o=0;o<t;)if(i===Re.BINARY){if(t-o<5)break;var s=r.readCode(e,o,5);if(o+=5,s===0){if(t-o<11)break;s=r.readCode(e,o,11)+31,o+=11}for(var u=0;u<s;u++){if(t-o<8){o=t;break}var f=r.readCode(e,o,8);a+=$.castAsNonUtf8Char(f),o+=8}i=n}else{var c=i===Re.DIGIT?4:5;if(t-o<c)break;var f=r.readCode(e,o,c);o+=c;var l=r.getCharacter(i,f);l.startsWith("CTRL_")?(n=i,i=r.getTable(l.charAt(5)),l.charAt(6)==="L"&&(n=i)):(a+=l,i=n)}return a},r.getTable=function(e){switch(e){case"L":return Re.LOWER;case"P":return Re.PUNCT;case"M":return Re.MIXED;case"D":return Re.DIGIT;case"B":return Re.BINARY;case"U":default:return Re.UPPER}},r.getCharacter=function(e,t){switch(e){case Re.UPPER:return r.UPPER_TABLE[t];case Re.LOWER:return r.LOWER_TABLE[t];case Re.MIXED:return r.MIXED_TABLE[t];case Re.PUNCT:return r.PUNCT_TABLE[t];case Re.DIGIT:return r.DIGIT_TABLE[t];default:throw new Gt("Bad table")}},r.prototype.correctBits=function(e){var t,n;this.ddata.getNbLayers()<=2?(n=6,t=ze.AZTEC_DATA_6):this.ddata.getNbLayers()<=8?(n=8,t=ze.AZTEC_DATA_8):this.ddata.getNbLayers()<=22?(n=10,t=ze.AZTEC_DATA_10):(n=12,t=ze.AZTEC_DATA_12);var i=this.ddata.getNbDatablocks(),a=e.length/n;if(a<i)throw new N;for(var o=e.length%n,s=new Int32Array(a),u=0;u<a;u++,o+=n)s[u]=r.readCode(e,o,n);try{var f=new or(t);f.decode(s,a-i)}catch(y){throw new N(y)}for(var c=(1<<n)-1,l=0,u=0;u<i;u++){var h=s[u];if(h===0||h===c)throw new N;(h===1||h===c-1)&&l++}for(var d=new Array(i*n-l),v=0,u=0;u<i;u++){var h=s[u];if(h===1||h===c-1)d.fill(h>1,v,v+n-1),v+=n-1;else for(var g=n-1;g>=0;--g)d[v++]=(h&1<<g)!==0}return d},r.prototype.extractBits=function(e){var t=this.ddata.isCompact(),n=this.ddata.getNbLayers(),i=(t?11:14)+n*4,a=new Int32Array(i),o=new Array(this.totalBitsInLayer(n,t));if(t)for(var s=0;s<a.length;s++)a[s]=s;else for(var u=i+1+2*z.truncDivision(z.truncDivision(i,2)-1,15),f=i/2,c=z.truncDivision(u,2),s=0;s<f;s++){var l=s+z.truncDivision(s,15);a[f-s-1]=c-l-1,a[f+s]=c+l+1}for(var s=0,h=0;s<n;s++){for(var d=(n-s)*4+(t?9:12),v=s*2,g=i-1-v,y=0;y<d;y++)for(var _=y*2,x=0;x<2;x++)o[h+_+x]=e.get(a[v+x],a[v+y]),o[h+2*d+_+x]=e.get(a[v+y],a[g-x]),o[h+4*d+_+x]=e.get(a[g-x],a[g-y]),o[h+6*d+_+x]=e.get(a[g-y],a[v+x]);h+=d*8}return o},r.readCode=function(e,t,n){for(var i=0,a=t;a<t+n;a++)i<<=1,e[a]&&(i|=1);return i},r.readByte=function(e,t){var n=e.length-t;return n>=8?r.readCode(e,t,8):r.readCode(e,t,n)<<8-n},r.convertBoolArrayToByteArray=function(e){for(var t=new Uint8Array((e.length+7)/8),n=0;n<t.length;n++)t[n]=r.readByte(e,8*n);return t},r.prototype.totalBitsInLayer=function(e,t){return((t?88:112)+16*e)*e},r.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],r.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],r.MIXED_TABLE=["CTRL_PS"," ","","","","","","","\x07","\b","	",`
`,"\v","\f","\r","\x1B","","","","","@","\\","^","_","`","|","~","","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],r.PUNCT_TABLE=["","\r",`\r
`,". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],r.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],r}(),Y=function(){function r(){}return r.round=function(e){return isNaN(e)?0:e<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:e>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:e+(e<0?-.5:.5)|0},r.distance=function(e,t,n,i){var a=e-n,o=t-i;return Math.sqrt(a*a+o*o)},r.sum=function(e){for(var t=0,n=0,i=e.length;n!==i;n++){var a=e[n];t+=a}return t},r}(),Ar=function(){function r(){}return r.floatToIntBits=function(e){return e},r.MAX_VALUE=Number.MAX_SAFE_INTEGER,r}(),B=function(){function r(e,t){this.x=e,this.y=t}return r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.equals=function(e){if(e instanceof r){var t=e;return this.x===t.x&&this.y===t.y}return!1},r.prototype.hashCode=function(){return 31*Ar.floatToIntBits(this.x)+Ar.floatToIntBits(this.y)},r.prototype.toString=function(){return"("+this.x+","+this.y+")"},r.orderBestPatterns=function(e){var t=this.distance(e[0],e[1]),n=this.distance(e[1],e[2]),i=this.distance(e[0],e[2]),a,o,s;if(n>=t&&n>=i?(o=e[0],a=e[1],s=e[2]):i>=n&&i>=t?(o=e[1],a=e[0],s=e[2]):(o=e[2],a=e[0],s=e[1]),this.crossProductZ(a,o,s)<0){var u=a;a=s,s=u}e[0]=a,e[1]=o,e[2]=s},r.distance=function(e,t){return Y.distance(e.x,e.y,t.x,t.y)},r.crossProductZ=function(e,t,n){var i=t.x,a=t.y;return(n.x-i)*(e.y-a)-(n.y-a)*(e.x-i)},r}(),Rr=function(){function r(e,t){this.bits=e,this.points=t}return r.prototype.getBits=function(){return this.bits},r.prototype.getPoints=function(){return this.points},r}(),ca=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),la=function(r){ca(e,r);function e(t,n,i,a,o){var s=r.call(this,t,n)||this;return s.compact=i,s.nbDatablocks=a,s.nbLayers=o,s}return e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e}(Rr),Cr=function(){function r(e,t,n,i){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),t==null&&(t=r.INIT_SIZE),n==null&&(n=e.getWidth()/2|0),i==null&&(i=e.getHeight()/2|0);var a=t/2|0;if(this.leftInit=n-a,this.rightInit=n+a,this.upInit=i-a,this.downInit=i+a,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new I}return r.prototype.detect=function(){for(var e=this.leftInit,t=this.rightInit,n=this.upInit,i=this.downInit,a=!1,o=!0,s=!1,u=!1,f=!1,c=!1,l=!1,h=this.width,d=this.height;o;){o=!1;for(var v=!0;(v||!u)&&t<h;)v=this.containsBlackPoint(n,i,t,!1),v?(t++,o=!0,u=!0):u||t++;if(t>=h){a=!0;break}for(var g=!0;(g||!f)&&i<d;)g=this.containsBlackPoint(e,t,i,!0),g?(i++,o=!0,f=!0):f||i++;if(i>=d){a=!0;break}for(var y=!0;(y||!c)&&e>=0;)y=this.containsBlackPoint(n,i,e,!1),y?(e--,o=!0,c=!0):c||e--;if(e<0){a=!0;break}for(var _=!0;(_||!l)&&n>=0;)_=this.containsBlackPoint(e,t,n,!0),_?(n--,o=!0,l=!0):l||n--;if(n<0){a=!0;break}o&&(s=!0)}if(!a&&s){for(var x=t-e,A=null,m=1;A===null&&m<x;m++)A=this.getBlackPointOnSegment(e,i-m,e+m,i);if(A==null)throw new I;for(var S=null,m=1;S===null&&m<x;m++)S=this.getBlackPointOnSegment(e,n+m,e+m,n);if(S==null)throw new I;for(var O=null,m=1;O===null&&m<x;m++)O=this.getBlackPointOnSegment(t,n+m,t-m,n);if(O==null)throw new I;for(var b=null,m=1;b===null&&m<x;m++)b=this.getBlackPointOnSegment(t,i-m,t-m,i);if(b==null)throw new I;return this.centerEdges(b,A,O,S)}else throw new I},r.prototype.getBlackPointOnSegment=function(e,t,n,i){for(var a=Y.round(Y.distance(e,t,n,i)),o=(n-e)/a,s=(i-t)/a,u=this.image,f=0;f<a;f++){var c=Y.round(e+f*o),l=Y.round(t+f*s);if(u.get(c,l))return new B(c,l)}return null},r.prototype.centerEdges=function(e,t,n,i){var a=e.getX(),o=e.getY(),s=t.getX(),u=t.getY(),f=n.getX(),c=n.getY(),l=i.getX(),h=i.getY(),d=r.CORR;return a<this.width/2?[new B(l-d,h+d),new B(s+d,u+d),new B(f-d,c-d),new B(a+d,o-d)]:[new B(l+d,h+d),new B(s+d,u-d),new B(f-d,c+d),new B(a-d,o-d)]},r.prototype.containsBlackPoint=function(e,t,n,i){var a=this.image;if(i){for(var o=e;o<=t;o++)if(a.get(o,n))return!0}else for(var s=e;s<=t;s++)if(a.get(n,s))return!0;return!1},r.INIT_SIZE=10,r.CORR=1,r}(),Zr=function(){function r(){}return r.checkAndNudgePoints=function(e,t){for(var n=e.getWidth(),i=e.getHeight(),a=!0,o=0;o<t.length&&a;o+=2){var s=Math.floor(t[o]),u=Math.floor(t[o+1]);if(s<-1||s>n||u<-1||u>i)throw new I;a=!1,s===-1?(t[o]=0,a=!0):s===n&&(t[o]=n-1,a=!0),u===-1?(t[o+1]=0,a=!0):u===i&&(t[o+1]=i-1,a=!0)}a=!0;for(var o=t.length-2;o>=0&&a;o-=2){var s=Math.floor(t[o]),u=Math.floor(t[o+1]);if(s<-1||s>n||u<-1||u>i)throw new I;a=!1,s===-1?(t[o]=0,a=!0):s===n&&(t[o]=n-1,a=!0),u===-1?(t[o+1]=0,a=!0):u===i&&(t[o+1]=i-1,a=!0)}},r}(),Nn=function(){function r(e,t,n,i,a,o,s,u,f){this.a11=e,this.a21=t,this.a31=n,this.a12=i,this.a22=a,this.a32=o,this.a13=s,this.a23=u,this.a33=f}return r.quadrilateralToQuadrilateral=function(e,t,n,i,a,o,s,u,f,c,l,h,d,v,g,y){var _=r.quadrilateralToSquare(e,t,n,i,a,o,s,u),x=r.squareToQuadrilateral(f,c,l,h,d,v,g,y);return x.times(_)},r.prototype.transformPoints=function(e){for(var t=e.length,n=this.a11,i=this.a12,a=this.a13,o=this.a21,s=this.a22,u=this.a23,f=this.a31,c=this.a32,l=this.a33,h=0;h<t;h+=2){var d=e[h],v=e[h+1],g=a*d+u*v+l;e[h]=(n*d+o*v+f)/g,e[h+1]=(i*d+s*v+c)/g}},r.prototype.transformPointsWithValues=function(e,t){for(var n=this.a11,i=this.a12,a=this.a13,o=this.a21,s=this.a22,u=this.a23,f=this.a31,c=this.a32,l=this.a33,h=e.length,d=0;d<h;d++){var v=e[d],g=t[d],y=a*v+u*g+l;e[d]=(n*v+o*g+f)/y,t[d]=(i*v+s*g+c)/y}},r.squareToQuadrilateral=function(e,t,n,i,a,o,s,u){var f=e-n+a-s,c=t-i+o-u;if(f===0&&c===0)return new r(n-e,a-n,e,i-t,o-i,t,0,0,1);var l=n-a,h=s-a,d=i-o,v=u-o,g=l*v-h*d,y=(f*v-h*c)/g,_=(l*c-f*d)/g;return new r(n-e+y*n,s-e+_*s,e,i-t+y*i,u-t+_*u,t,y,_,1)},r.quadrilateralToSquare=function(e,t,n,i,a,o,s,u){return r.squareToQuadrilateral(e,t,n,i,a,o,s,u).buildAdjoint()},r.prototype.buildAdjoint=function(){return new r(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},r.prototype.times=function(e){return new r(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)},r}(),da=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ha=function(r){da(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.sampleGrid=function(t,n,i,a,o,s,u,f,c,l,h,d,v,g,y,_,x,A,m){var S=Nn.quadrilateralToQuadrilateral(a,o,s,u,f,c,l,h,d,v,g,y,_,x,A,m);return this.sampleGridWithTransform(t,n,i,S)},e.prototype.sampleGridWithTransform=function(t,n,i,a){if(n<=0||i<=0)throw new I;for(var o=new it(n,i),s=new Float32Array(2*n),u=0;u<i;u++){for(var f=s.length,c=u+.5,l=0;l<f;l+=2)s[l]=l/2+.5,s[l+1]=c;a.transformPoints(s),Zr.checkAndNudgePoints(t,s);try{for(var l=0;l<f;l+=2)t.get(Math.floor(s[l]),Math.floor(s[l+1]))&&o.set(l/2,u)}catch{throw new I}}return o},e}(Zr),Nr=function(){function r(){}return r.setGridSampler=function(e){r.gridSampler=e},r.getInstance=function(){return r.gridSampler},r.gridSampler=new ha,r}(),Xe=function(){function r(e,t){this.x=e,this.y=t}return r.prototype.toResultPoint=function(){return new B(this.getX(),this.getY())},r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r}(),va=function(){function r(e){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=e}return r.prototype.detect=function(){return this.detectMirror(!1)},r.prototype.detectMirror=function(e){var t=this.getMatrixCenter(),n=this.getBullsEyeCorners(t);if(e){var i=n[0];n[0]=n[2],n[2]=i}this.extractParameters(n);var a=this.sampleGrid(this.image,n[this.shift%4],n[(this.shift+1)%4],n[(this.shift+2)%4],n[(this.shift+3)%4]),o=this.getMatrixCornerPoints(n);return new la(a,o,this.compact,this.nbDataBlocks,this.nbLayers)},r.prototype.extractParameters=function(e){if(!this.isValidPoint(e[0])||!this.isValidPoint(e[1])||!this.isValidPoint(e[2])||!this.isValidPoint(e[3]))throw new I;var t=2*this.nbCenterLayers,n=new Int32Array([this.sampleLine(e[0],e[1],t),this.sampleLine(e[1],e[2],t),this.sampleLine(e[2],e[3],t),this.sampleLine(e[3],e[0],t)]);this.shift=this.getRotation(n,t);for(var i=0,a=0;a<4;a++){var o=n[(this.shift+a)%4];this.compact?(i<<=7,i+=o>>1&127):(i<<=10,i+=(o>>2&992)+(o>>1&31))}var s=this.getCorrectedParameterData(i,this.compact);this.compact?(this.nbLayers=(s>>6)+1,this.nbDataBlocks=(s&63)+1):(this.nbLayers=(s>>11)+1,this.nbDataBlocks=(s&2047)+1)},r.prototype.getRotation=function(e,t){var n=0;e.forEach(function(a,o,s){var u=(a>>t-2<<1)+(a&1);n=(n<<3)+u}),n=((n&1)<<11)+(n>>1);for(var i=0;i<4;i++)if(z.bitCount(n^this.EXPECTED_CORNER_BITS[i])<=2)return i;throw new I},r.prototype.getCorrectedParameterData=function(e,t){var n,i;t?(n=7,i=2):(n=10,i=4);for(var a=n-i,o=new Int32Array(n),s=n-1;s>=0;--s)o[s]=e&15,e>>=4;try{var u=new or(ze.AZTEC_PARAM);u.decode(o,a)}catch{throw new I}for(var f=0,s=0;s<i;s++)f=(f<<4)+o[s];return f},r.prototype.getBullsEyeCorners=function(e){var t=e,n=e,i=e,a=e,o=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var s=this.getFirstDifferent(t,o,1,-1),u=this.getFirstDifferent(n,o,1,1),f=this.getFirstDifferent(i,o,-1,1),c=this.getFirstDifferent(a,o,-1,-1);if(this.nbCenterLayers>2){var l=this.distancePoint(c,s)*this.nbCenterLayers/(this.distancePoint(a,t)*(this.nbCenterLayers+2));if(l<.75||l>1.25||!this.isWhiteOrBlackRectangle(s,u,f,c))break}t=s,n=u,i=f,a=c,o=!o}if(this.nbCenterLayers!==5&&this.nbCenterLayers!==7)throw new I;this.compact=this.nbCenterLayers===5;var h=new B(t.getX()+.5,t.getY()-.5),d=new B(n.getX()+.5,n.getY()+.5),v=new B(i.getX()-.5,i.getY()+.5),g=new B(a.getX()-.5,a.getY()-.5);return this.expandSquare([h,d,v,g],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},r.prototype.getMatrixCenter=function(){var e,t,n,i;try{var a=new Cr(this.image).detect();e=a[0],t=a[1],n=a[2],i=a[3]}catch{var o=this.image.getWidth()/2,s=this.image.getHeight()/2;e=this.getFirstDifferent(new Xe(o+7,s-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new Xe(o+7,s+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new Xe(o-7,s+7),!1,-1,1).toResultPoint(),i=this.getFirstDifferent(new Xe(o-7,s-7),!1,-1,-1).toResultPoint()}var u=Y.round((e.getX()+i.getX()+t.getX()+n.getX())/4),f=Y.round((e.getY()+i.getY()+t.getY()+n.getY())/4);try{var a=new Cr(this.image,15,u,f).detect();e=a[0],t=a[1],n=a[2],i=a[3]}catch{e=this.getFirstDifferent(new Xe(u+7,f-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new Xe(u+7,f+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new Xe(u-7,f+7),!1,-1,1).toResultPoint(),i=this.getFirstDifferent(new Xe(u-7,f-7),!1,-1,-1).toResultPoint()}return u=Y.round((e.getX()+i.getX()+t.getX()+n.getX())/4),f=Y.round((e.getY()+i.getY()+t.getY()+n.getY())/4),new Xe(u,f)},r.prototype.getMatrixCornerPoints=function(e){return this.expandSquare(e,2*this.nbCenterLayers,this.getDimension())},r.prototype.sampleGrid=function(e,t,n,i,a){var o=Nr.getInstance(),s=this.getDimension(),u=s/2-this.nbCenterLayers,f=s/2+this.nbCenterLayers;return o.sampleGrid(e,s,s,u,u,f,u,f,f,u,f,t.getX(),t.getY(),n.getX(),n.getY(),i.getX(),i.getY(),a.getX(),a.getY())},r.prototype.sampleLine=function(e,t,n){for(var i=0,a=this.distanceResultPoint(e,t),o=a/n,s=e.getX(),u=e.getY(),f=o*(t.getX()-e.getX())/a,c=o*(t.getY()-e.getY())/a,l=0;l<n;l++)this.image.get(Y.round(s+l*f),Y.round(u+l*c))&&(i|=1<<n-l-1);return i},r.prototype.isWhiteOrBlackRectangle=function(e,t,n,i){var a=3;e=new Xe(e.getX()-a,e.getY()+a),t=new Xe(t.getX()-a,t.getY()-a),n=new Xe(n.getX()+a,n.getY()-a),i=new Xe(i.getX()+a,i.getY()+a);var o=this.getColor(i,e);if(o===0)return!1;var s=this.getColor(e,t);return s!==o||(s=this.getColor(t,n),s!==o)?!1:(s=this.getColor(n,i),s===o)},r.prototype.getColor=function(e,t){for(var n=this.distancePoint(e,t),i=(t.getX()-e.getX())/n,a=(t.getY()-e.getY())/n,o=0,s=e.getX(),u=e.getY(),f=this.image.get(e.getX(),e.getY()),c=Math.ceil(n),l=0;l<c;l++)s+=i,u+=a,this.image.get(Y.round(s),Y.round(u))!==f&&o++;var h=o/n;return h>.1&&h<.9?0:h<=.1===f?1:-1},r.prototype.getFirstDifferent=function(e,t,n,i){for(var a=e.getX()+n,o=e.getY()+i;this.isValid(a,o)&&this.image.get(a,o)===t;)a+=n,o+=i;for(a-=n,o-=i;this.isValid(a,o)&&this.image.get(a,o)===t;)a+=n;for(a-=n;this.isValid(a,o)&&this.image.get(a,o)===t;)o+=i;return o-=i,new Xe(a,o)},r.prototype.expandSquare=function(e,t,n){var i=n/(2*t),a=e[0].getX()-e[2].getX(),o=e[0].getY()-e[2].getY(),s=(e[0].getX()+e[2].getX())/2,u=(e[0].getY()+e[2].getY())/2,f=new B(s+i*a,u+i*o),c=new B(s-i*a,u-i*o);a=e[1].getX()-e[3].getX(),o=e[1].getY()-e[3].getY(),s=(e[1].getX()+e[3].getX())/2,u=(e[1].getY()+e[3].getY())/2;var l=new B(s+i*a,u+i*o),h=new B(s-i*a,u-i*o),d=[f,l,c,h];return d},r.prototype.isValid=function(e,t){return e>=0&&e<this.image.getWidth()&&t>0&&t<this.image.getHeight()},r.prototype.isValidPoint=function(e){var t=Y.round(e.getX()),n=Y.round(e.getY());return this.isValid(t,n)},r.prototype.distancePoint=function(e,t){return Y.distance(e.getX(),e.getY(),t.getX(),t.getY())},r.prototype.distanceResultPoint=function(e,t){return Y.distance(e.getX(),e.getY(),t.getX(),t.getY())},r.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(z.truncDivision(this.nbLayers-4,8)+1)+15},r}(),Qt=function(){function r(){}return r.prototype.decode=function(e,t){t===void 0&&(t=null);var n=null,i=new va(e.getBlackMatrix()),a=null,o=null;try{var s=i.detectMirror(!1);a=s.getPoints(),this.reportFoundResultPoints(t,a),o=new Yr().decode(s)}catch(l){n=l}if(o==null)try{var s=i.detectMirror(!0);a=s.getPoints(),this.reportFoundResultPoints(t,a),o=new Yr().decode(s)}catch(l){throw n??l}var u=new ke(o.getText(),o.getRawBytes(),o.getNumBits(),a,L.AZTEC,ue.currentTimeMillis()),f=o.getByteSegments();f!=null&&u.putMetadata(Me.BYTE_SEGMENTS,f);var c=o.getECLevel();return c!=null&&u.putMetadata(Me.ERROR_CORRECTION_LEVEL,c),u},r.prototype.reportFoundResultPoints=function(e,t){if(e!=null){var n=e.get(oe.NEED_RESULT_POINT_CALLBACK);n!=null&&t.forEach(function(i,a,o){n.foundPossibleResultPoint(i)})}},r.prototype.reset=function(){},r}(),pa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){pa(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new Qt,t)||this}return e})(Dt);var Ie=function(){function r(){}return r.prototype.decode=function(e,t){try{return this.doDecode(e,t)}catch{var n=t&&t.get(oe.TRY_HARDER)===!0;if(n&&e.isRotateSupported()){var i=e.rotateCounterClockwise(),a=this.doDecode(i,t),o=a.getResultMetadata(),s=270;o!==null&&o.get(Me.ORIENTATION)===!0&&(s=s+o.get(Me.ORIENTATION)%360),a.putMetadata(Me.ORIENTATION,s);var u=a.getResultPoints();if(u!==null)for(var f=i.getHeight(),c=0;c<u.length;c++)u[c]=new B(f-u[c].getY()-1,u[c].getX());return a}else throw new I}},r.prototype.reset=function(){},r.prototype.doDecode=function(e,t){var n=e.getWidth(),i=e.getHeight(),a=new be(n),o=t&&t.get(oe.TRY_HARDER)===!0,s=Math.max(1,i>>(o?8:5)),u;o?u=i:u=15;for(var f=Math.trunc(i/2),c=0;c<u;c++){var l=Math.trunc((c+1)/2),h=(c&1)===0,d=f+s*(h?l:-l);if(d<0||d>=i)break;try{a=e.getBlackRow(d,a)}catch{continue}for(var v=function(x){if(x===1&&(a.reverse(),t&&t.get(oe.NEED_RESULT_POINT_CALLBACK)===!0)){var A=new Map;t.forEach(function(O,b){return A.set(b,O)}),A.delete(oe.NEED_RESULT_POINT_CALLBACK),t=A}try{var m=g.decodeRow(d,a,t);if(x===1){m.putMetadata(Me.ORIENTATION,180);var S=m.getResultPoints();S!==null&&(S[0]=new B(n-S[0].getX()-1,S[0].getY()),S[1]=new B(n-S[1].getX()-1,S[1].getY()))}return{value:m}}catch{}},g=this,y=0;y<2;y++){var _=v(y);if(typeof _=="object")return _.value}}throw new I},r.recordPattern=function(e,t,n){for(var i=n.length,a=0;a<i;a++)n[a]=0;var o=e.getSize();if(t>=o)throw new I;for(var s=!e.get(t),u=0,f=t;f<o;){if(e.get(f)!==s)n[u]++;else{if(++u===i)break;n[u]=1,s=!s}f++}if(!(u===i||u===i-1&&f===o))throw new I},r.recordPatternInReverse=function(e,t,n){for(var i=n.length,a=e.get(t);t>0&&i>=0;)e.get(--t)!==a&&(i--,a=!a);if(i>=0)throw new I;r.recordPattern(e,t+1,n)},r.patternMatchVariance=function(e,t,n){for(var i=e.length,a=0,o=0,s=0;s<i;s++)a+=e[s],o+=t[s];if(a<o)return Number.POSITIVE_INFINITY;var u=a/o;n*=u;for(var f=0,c=0;c<i;c++){var l=e[c],h=t[c]*u,d=l>h?l-h:h-l;if(d>n)return Number.POSITIVE_INFINITY;f+=d}return f/a},r}(),ga=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Kr=function(r){ga(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.findStartPattern=function(t){for(var n=t.getSize(),i=t.getNextSet(0),a=0,o=Int32Array.from([0,0,0,0,0,0]),s=i,u=!1,f=6,c=i;c<n;c++)if(t.get(c)!==u)o[a]++;else{if(a===f-1){for(var l=e.MAX_AVG_VARIANCE,h=-1,d=e.CODE_START_A;d<=e.CODE_START_C;d++){var v=Ie.patternMatchVariance(o,e.CODE_PATTERNS[d],e.MAX_INDIVIDUAL_VARIANCE);v<l&&(l=v,h=d)}if(h>=0&&t.isRange(Math.max(0,s-(c-s)/2),s,!1))return Int32Array.from([s,c,h]);s+=o[0]+o[1],o=o.slice(2,o.length),o[a-1]=0,o[a]=0,a--}else a++;o[a]=1,u=!u}throw new I},e.decodeCode=function(t,n,i){Ie.recordPattern(t,i,n);for(var a=e.MAX_AVG_VARIANCE,o=-1,s=0;s<e.CODE_PATTERNS.length;s++){var u=e.CODE_PATTERNS[s],f=this.patternMatchVariance(n,u,e.MAX_INDIVIDUAL_VARIANCE);f<a&&(a=f,o=s)}if(o>=0)return o;throw new I},e.prototype.decodeRow=function(t,n,i){var a=i&&i.get(oe.ASSUME_GS1)===!0,o=e.findStartPattern(n),s=o[2],u=0,f=new Uint8Array(20);f[u++]=s;var c;switch(s){case e.CODE_START_A:c=e.CODE_CODE_A;break;case e.CODE_START_B:c=e.CODE_CODE_B;break;case e.CODE_START_C:c=e.CODE_CODE_C;break;default:throw new N}for(var l=!1,h=!1,d="",v=o[0],g=o[1],y=Int32Array.from([0,0,0,0,0,0]),_=0,x=0,A=s,m=0,S=!0,O=!1,b=!1;!l;){var T=h;switch(h=!1,_=x,x=e.decodeCode(n,y,g),f[u++]=x,x!==e.CODE_STOP&&(S=!0),x!==e.CODE_STOP&&(m++,A+=m*x),v=g,g+=y.reduce(function(Nt,Pt){return Nt+Pt},0),x){case e.CODE_START_A:case e.CODE_START_B:case e.CODE_START_C:throw new N}switch(c){case e.CODE_CODE_A:if(x<64)b===O?d+=String.fromCharCode(32+x):d+=String.fromCharCode(32+x+128),b=!1;else if(x<96)b===O?d+=String.fromCharCode(x-64):d+=String.fromCharCode(x+64),b=!1;else switch(x!==e.CODE_STOP&&(S=!1),x){case e.CODE_FNC_1:a&&(d.length===0?d+="]C1":d+="");break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_A:!O&&b?(O=!0,b=!1):O&&b?(O=!1,b=!1):b=!0;break;case e.CODE_SHIFT:h=!0,c=e.CODE_CODE_B;break;case e.CODE_CODE_B:c=e.CODE_CODE_B;break;case e.CODE_CODE_C:c=e.CODE_CODE_C;break;case e.CODE_STOP:l=!0;break}break;case e.CODE_CODE_B:if(x<96)b===O?d+=String.fromCharCode(32+x):d+=String.fromCharCode(32+x+128),b=!1;else switch(x!==e.CODE_STOP&&(S=!1),x){case e.CODE_FNC_1:a&&(d.length===0?d+="]C1":d+="");break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_B:!O&&b?(O=!0,b=!1):O&&b?(O=!1,b=!1):b=!0;break;case e.CODE_SHIFT:h=!0,c=e.CODE_CODE_A;break;case e.CODE_CODE_A:c=e.CODE_CODE_A;break;case e.CODE_CODE_C:c=e.CODE_CODE_C;break;case e.CODE_STOP:l=!0;break}break;case e.CODE_CODE_C:if(x<100)x<10&&(d+="0"),d+=x;else switch(x!==e.CODE_STOP&&(S=!1),x){case e.CODE_FNC_1:a&&(d.length===0?d+="]C1":d+="");break;case e.CODE_CODE_A:c=e.CODE_CODE_A;break;case e.CODE_CODE_B:c=e.CODE_CODE_B;break;case e.CODE_STOP:l=!0;break}break}T&&(c=c===e.CODE_CODE_A?e.CODE_CODE_B:e.CODE_CODE_A)}var D=g-v;if(g=n.getNextUnset(g),!n.isRange(g,Math.min(n.getSize(),g+(g-v)/2),!1))throw new I;if(A-=m*_,A%103!==_)throw new _e;var P=d.length;if(P===0)throw new I;P>0&&S&&(c===e.CODE_CODE_C?d=d.substring(0,P-2):d=d.substring(0,P-1));for(var G=(o[1]+o[0])/2,H=v+D/2,se=f.length,Be=new Uint8Array(se),W=0;W<se;W++)Be[W]=f[W];var lt=[new B(G,t),new B(H,t)];return new ke(d,Be,0,lt,L.CODE_128,new Date().getTime())},e.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],e.MAX_AVG_VARIANCE=.25,e.MAX_INDIVIDUAL_VARIANCE=.7,e.CODE_SHIFT=98,e.CODE_CODE_C=99,e.CODE_CODE_B=100,e.CODE_CODE_A=101,e.CODE_FNC_1=102,e.CODE_FNC_2=97,e.CODE_FNC_3=96,e.CODE_FNC_4_A=101,e.CODE_FNC_4_B=100,e.CODE_START_A=103,e.CODE_START_B=104,e.CODE_START_C=105,e.CODE_STOP=106,e}(Ie),xa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ur=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},qr=function(r){xa(e,r);function e(t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var i=r.call(this)||this;return i.usingCheckDigit=t,i.extendedMode=n,i.decodeRowResult="",i.counters=new Int32Array(9),i}return e.prototype.decodeRow=function(t,n,i){var a,o,s,u,f=this.counters;f.fill(0),this.decodeRowResult="";var c=e.findAsteriskPattern(n,f),l=n.getNextSet(c[1]),h=n.getSize(),d,v;do{e.recordPattern(n,l,f);var g=e.toNarrowWidePattern(f);if(g<0)throw new I;d=e.patternToChar(g),this.decodeRowResult+=d,v=l;try{for(var y=(a=void 0,ur(f)),_=y.next();!_.done;_=y.next()){var x=_.value;l+=x}}catch(se){a={error:se}}finally{try{_&&!_.done&&(o=y.return)&&o.call(y)}finally{if(a)throw a.error}}l=n.getNextSet(l)}while(d!=="*");this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var A=0;try{for(var m=ur(f),S=m.next();!S.done;S=m.next()){var x=S.value;A+=x}}catch(se){s={error:se}}finally{try{S&&!S.done&&(u=m.return)&&u.call(m)}finally{if(s)throw s.error}}var O=l-v-A;if(l!==h&&O*2<A)throw new I;if(this.usingCheckDigit){for(var b=this.decodeRowResult.length-1,T=0,D=0;D<b;D++)T+=e.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(D));if(this.decodeRowResult.charAt(b)!==e.ALPHABET_STRING.charAt(T%43))throw new _e;this.decodeRowResult=this.decodeRowResult.substring(0,b)}if(this.decodeRowResult.length===0)throw new I;var P;this.extendedMode?P=e.decodeExtended(this.decodeRowResult):P=this.decodeRowResult;var G=(c[1]+c[0])/2,H=v+A/2;return new ke(P,null,0,[new B(G,t),new B(H,t)],L.CODE_39,new Date().getTime())},e.findAsteriskPattern=function(t,n){for(var i=t.getSize(),a=t.getNextSet(0),o=0,s=a,u=!1,f=n.length,c=a;c<i;c++)if(t.get(c)!==u)n[o]++;else{if(o===f-1){if(this.toNarrowWidePattern(n)===e.ASTERISK_ENCODING&&t.isRange(Math.max(0,s-Math.floor((c-s)/2)),s,!1))return[s,c];s+=n[0]+n[1],n.copyWithin(0,2,2+o-1),n[o-1]=0,n[o]=0,o--}else o++;n[o]=1,u=!u}throw new I},e.toNarrowWidePattern=function(t){var n,i,a=t.length,o=0,s;do{var u=2147483647;try{for(var f=(n=void 0,ur(t)),c=f.next();!c.done;c=f.next()){var l=c.value;l<u&&l>o&&(u=l)}}catch(g){n={error:g}}finally{try{c&&!c.done&&(i=f.return)&&i.call(f)}finally{if(n)throw n.error}}o=u,s=0;for(var h=0,d=0,v=0;v<a;v++){var l=t[v];l>o&&(d|=1<<a-1-v,s++,h+=l)}if(s===3){for(var v=0;v<a&&s>0;v++){var l=t[v];if(l>o&&(s--,l*2>=h))return-1}return d}}while(s>3);return-1},e.patternToChar=function(t){for(var n=0;n<e.CHARACTER_ENCODINGS.length;n++)if(e.CHARACTER_ENCODINGS[n]===t)return e.ALPHABET_STRING.charAt(n);if(t===e.ASTERISK_ENCODING)return"*";throw new I},e.decodeExtended=function(t){for(var n=t.length,i="",a=0;a<n;a++){var o=t.charAt(a);if(o==="+"||o==="$"||o==="%"||o==="/"){var s=t.charAt(a+1),u="\0";switch(o){case"+":if(s>="A"&&s<="Z")u=String.fromCharCode(s.charCodeAt(0)+32);else throw new N;break;case"$":if(s>="A"&&s<="Z")u=String.fromCharCode(s.charCodeAt(0)-64);else throw new N;break;case"%":if(s>="A"&&s<="E")u=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")u=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")u=String.fromCharCode(s.charCodeAt(0)+43);else if(s==="U")u="\0";else if(s==="V")u="@";else if(s==="W")u="`";else if(s==="X"||s==="Y"||s==="Z")u="";else throw new N;break;case"/":if(s>="A"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)-32);else if(s==="Z")u=":";else throw new N;break}i+=u,a++}else i+=o}return i},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",e.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],e.ASTERISK_ENCODING=148,e}(Ie),ya=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),fr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Qr=function(r){ya(e,r);function e(){var t=r.call(this)||this;return t.decodeRowResult="",t.counters=new Int32Array(6),t}return e.prototype.decodeRow=function(t,n,i){var a,o,s,u,f=this.findAsteriskPattern(n),c=n.getNextSet(f[1]),l=n.getSize(),h=this.counters;h.fill(0),this.decodeRowResult="";var d,v;do{e.recordPattern(n,c,h);var g=this.toPattern(h);if(g<0)throw new I;d=this.patternToChar(g),this.decodeRowResult+=d,v=c;try{for(var y=(a=void 0,fr(h)),_=y.next();!_.done;_=y.next()){var x=_.value;c+=x}}catch(D){a={error:D}}finally{try{_&&!_.done&&(o=y.return)&&o.call(y)}finally{if(a)throw a.error}}c=n.getNextSet(c)}while(d!=="*");this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var A=0;try{for(var m=fr(h),S=m.next();!S.done;S=m.next()){var x=S.value;A+=x}}catch(D){s={error:D}}finally{try{S&&!S.done&&(u=m.return)&&u.call(m)}finally{if(s)throw s.error}}if(c===l||!n.get(c))throw new I;if(this.decodeRowResult.length<2)throw new I;this.checkChecksums(this.decodeRowResult),this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-2);var O=this.decodeExtended(this.decodeRowResult),b=(f[1]+f[0])/2,T=v+A/2;return new ke(O,null,0,[new B(b,t),new B(T,t)],L.CODE_93,new Date().getTime())},e.prototype.findAsteriskPattern=function(t){var n=t.getSize(),i=t.getNextSet(0);this.counters.fill(0);for(var a=this.counters,o=i,s=!1,u=a.length,f=0,c=i;c<n;c++)if(t.get(c)!==s)a[f]++;else{if(f===u-1){if(this.toPattern(a)===e.ASTERISK_ENCODING)return new Int32Array([o,c]);o+=a[0]+a[1],a.copyWithin(0,2,2+f-1),a[f-1]=0,a[f]=0,f--}else f++;a[f]=1,s=!s}throw new I},e.prototype.toPattern=function(t){var n,i,a=0;try{for(var o=fr(t),s=o.next();!s.done;s=o.next()){var u=s.value;a+=u}}catch(v){n={error:v}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}for(var f=0,c=t.length,l=0;l<c;l++){var h=Math.round(t[l]*9/a);if(h<1||h>4)return-1;if(l&1)f<<=h;else for(var d=0;d<h;d++)f=f<<1|1}return f},e.prototype.patternToChar=function(t){for(var n=0;n<e.CHARACTER_ENCODINGS.length;n++)if(e.CHARACTER_ENCODINGS[n]===t)return e.ALPHABET_STRING.charAt(n);throw new I},e.prototype.decodeExtended=function(t){for(var n=t.length,i="",a=0;a<n;a++){var o=t.charAt(a);if(o>="a"&&o<="d"){if(a>=n-1)throw new N;var s=t.charAt(a+1),u="\0";switch(o){case"d":if(s>="A"&&s<="Z")u=String.fromCharCode(s.charCodeAt(0)+32);else throw new N;break;case"a":if(s>="A"&&s<="Z")u=String.fromCharCode(s.charCodeAt(0)-64);else throw new N;break;case"b":if(s>="A"&&s<="E")u=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")u=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")u=String.fromCharCode(s.charCodeAt(0)+43);else if(s==="U")u="\0";else if(s==="V")u="@";else if(s==="W")u="`";else if(s>="X"&&s<="Z")u="";else throw new N;break;case"c":if(s>="A"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)-32);else if(s==="Z")u=":";else throw new N;break}i+=u,a++}else i+=o}return i},e.prototype.checkChecksums=function(t){var n=t.length;this.checkOneChecksum(t,n-2,20),this.checkOneChecksum(t,n-1,15)},e.prototype.checkOneChecksum=function(t,n,i){for(var a=1,o=0,s=n-1;s>=0;s--)o+=a*e.ALPHABET_STRING.indexOf(t.charAt(s)),++a>i&&(a=1);if(t.charAt(n)!==e.ALPHABET_STRING[o%47])throw new _e},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*",e.CHARACTER_ENCODINGS=[276,328,324,322,296,292,290,336,274,266,424,420,418,404,402,394,360,356,354,308,282,344,332,326,300,278,436,434,428,422,406,410,364,358,310,314,302,468,466,458,366,374,430,294,474,470,306,350],e.ASTERISK_ENCODING=e.CHARACTER_ENCODINGS[47],e}(Ie),_a=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),wa=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Jr=function(r){_a(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.narrowLineWidth=-1,t}return e.prototype.decodeRow=function(t,n,i){var a,o,s=this.decodeStart(n),u=this.decodeEnd(n),f=new X;e.decodeMiddle(n,s[1],u[0],f);var c=f.toString(),l=null;i!=null&&(l=i.get(oe.ALLOWED_LENGTHS)),l==null&&(l=e.DEFAULT_ALLOWED_LENGTHS);var h=c.length,d=!1,v=0;try{for(var g=wa(l),y=g.next();!y.done;y=g.next()){var _=y.value;if(h===_){d=!0;break}_>v&&(v=_)}}catch(m){a={error:m}}finally{try{y&&!y.done&&(o=g.return)&&o.call(g)}finally{if(a)throw a.error}}if(!d&&h>v&&(d=!0),!d)throw new N;var x=[new B(s[1],t),new B(u[0],t)],A=new ke(c,null,0,x,L.ITF,new Date().getTime());return A},e.decodeMiddle=function(t,n,i,a){var o=new Int32Array(10),s=new Int32Array(5),u=new Int32Array(5);for(o.fill(0),s.fill(0),u.fill(0);n<i;){Ie.recordPattern(t,n,o);for(var f=0;f<5;f++){var c=2*f;s[f]=o[c],u[f]=o[c+1]}var l=e.decodeDigit(s);a.append(l.toString()),l=this.decodeDigit(u),a.append(l.toString()),o.forEach(function(h){n+=h})}},e.prototype.decodeStart=function(t){var n=e.skipWhiteSpace(t),i=e.findGuardPattern(t,n,e.START_PATTERN);return this.narrowLineWidth=(i[1]-i[0])/4,this.validateQuietZone(t,i[0]),i},e.prototype.validateQuietZone=function(t,n){var i=this.narrowLineWidth*10;i=i<n?i:n;for(var a=n-1;i>0&&a>=0&&!t.get(a);a--)i--;if(i!==0)throw new I},e.skipWhiteSpace=function(t){var n=t.getSize(),i=t.getNextSet(0);if(i===n)throw new I;return i},e.prototype.decodeEnd=function(t){t.reverse();try{var n=e.skipWhiteSpace(t),i=void 0;try{i=e.findGuardPattern(t,n,e.END_PATTERN_REVERSED[0])}catch(o){o instanceof I&&(i=e.findGuardPattern(t,n,e.END_PATTERN_REVERSED[1]))}this.validateQuietZone(t,i[0]);var a=i[0];return i[0]=t.getSize()-i[1],i[1]=t.getSize()-a,i}finally{t.reverse()}},e.findGuardPattern=function(t,n,i){var a=i.length,o=new Int32Array(a),s=t.getSize(),u=!1,f=0,c=n;o.fill(0);for(var l=n;l<s;l++)if(t.get(l)!==u)o[f]++;else{if(f===a-1){if(Ie.patternMatchVariance(o,i,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[c,l];c+=o[0]+o[1],ue.arraycopy(o,2,o,0,f-1),o[f-1]=0,o[f]=0,f--}else f++;o[f]=1,u=!u}throw new I},e.decodeDigit=function(t){for(var n=e.MAX_AVG_VARIANCE,i=-1,a=e.PATTERNS.length,o=0;o<a;o++){var s=e.PATTERNS[o],u=Ie.patternMatchVariance(t,s,e.MAX_INDIVIDUAL_VARIANCE);u<n?(n=u,i=o):u===n&&(i=-1)}if(i>=0)return i%10;throw new I},e.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],e.MAX_AVG_VARIANCE=.38,e.MAX_INDIVIDUAL_VARIANCE=.5,e.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],e.START_PATTERN=Int32Array.from([1,1,1,1]),e.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])],e}(Ie),Aa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),bt=function(r){Aa(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.decodeRowStringBuffer="",t}return e.findStartGuardPattern=function(t){for(var n=!1,i,a=0,o=Int32Array.from([0,0,0]);!n;){o=Int32Array.from([0,0,0]),i=e.findGuardPattern(t,a,!1,this.START_END_PATTERN,o);var s=i[0];a=i[1];var u=s-(a-s);u>=0&&(n=t.isRange(u,s,!1))}return i},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var n=t.length;if(n===0)return!1;var i=parseInt(t.charAt(n-1),10);return e.getStandardUPCEANChecksum(t.substring(0,n-1))===i},e.getStandardUPCEANChecksum=function(t){for(var n=t.length,i=0,a=n-1;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new N;i+=o}i*=3;for(var a=n-2;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new N;i+=o}return(1e3-i)%10},e.decodeEnd=function(t,n){return e.findGuardPattern(t,n,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPatternWithoutCounters=function(t,n,i,a){return this.findGuardPattern(t,n,i,a,new Int32Array(a.length))},e.findGuardPattern=function(t,n,i,a,o){var s=t.getSize();n=i?t.getNextUnset(n):t.getNextSet(n);for(var u=0,f=n,c=a.length,l=i,h=n;h<s;h++)if(t.get(h)!==l)o[u]++;else{if(u===c-1){if(Ie.patternMatchVariance(o,a,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return Int32Array.from([f,h]);f+=o[0]+o[1];for(var d=o.slice(2,o.length),v=0;v<u-1;v++)o[v]=d[v];o[u-1]=0,o[u]=0,u--}else u++;o[u]=1,l=!l}throw new I},e.decodeDigit=function(t,n,i,a){this.recordPattern(t,i,n);for(var o=this.MAX_AVG_VARIANCE,s=-1,u=a.length,f=0;f<u;f++){var c=a[f],l=Ie.patternMatchVariance(n,c,e.MAX_INDIVIDUAL_VARIANCE);l<o&&(o=l,s=f)}if(s>=0)return s;throw new I},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=Int32Array.from([1,1,1]),e.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),e.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])],e}(Ie),Ca=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ea=function(){function r(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(e,t,n){var i=this.decodeRowStringBuffer,a=this.decodeMiddle(t,n,i),o=i.toString(),s=r.parseExtensionString(o),u=[new B((n[0]+n[1])/2,e),new B(a,e)],f=new ke(o,null,0,u,L.UPC_EAN_EXTENSION,new Date().getTime());return s!=null&&f.putAllMetadata(s),f},r.prototype.decodeMiddle=function(e,t,n){var i,a,o=this.decodeMiddleCounters;o[0]=0,o[1]=0,o[2]=0,o[3]=0;for(var s=e.getSize(),u=t[1],f=0,c=0;c<5&&u<s;c++){var l=bt.decodeDigit(e,o,u,bt.L_AND_G_PATTERNS);n+=String.fromCharCode(48+l%10);try{for(var h=(i=void 0,Ca(o)),d=h.next();!d.done;d=h.next()){var v=d.value;u+=v}}catch(y){i={error:y}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}l>=10&&(f|=1<<4-c),c!==4&&(u=e.getNextSet(u),u=e.getNextUnset(u))}if(n.length!==5)throw new I;var g=this.determineCheckDigit(f);if(r.extensionChecksum(n.toString())!==g)throw new I;return u},r.extensionChecksum=function(e){for(var t=e.length,n=0,i=t-2;i>=0;i-=2)n+=e.charAt(i).charCodeAt(0)-48;n*=3;for(var i=t-1;i>=0;i-=2)n+=e.charAt(i).charCodeAt(0)-48;return n*=3,n%10},r.prototype.determineCheckDigit=function(e){for(var t=0;t<10;t++)if(e===this.CHECK_DIGIT_ENCODINGS[t])return t;throw new I},r.parseExtensionString=function(e){if(e.length!==5)return null;var t=r.parseExtension5String(e);return t==null?null:new Map([[Me.SUGGESTED_PRICE,t]])},r.parseExtension5String=function(e){var t;switch(e.charAt(0)){case"0":t="£";break;case"5":t="$";break;case"9":switch(e){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}t="";break;default:t="";break}var n=parseInt(e.substring(1)),i=(n/100).toString(),a=n%100,o=a<10?"0"+a:a.toString();return t+i+"."+o},r}(),ma=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Sa=function(){function r(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(e,t,n){var i=this.decodeRowStringBuffer,a=this.decodeMiddle(t,n,i),o=i.toString(),s=r.parseExtensionString(o),u=[new B((n[0]+n[1])/2,e),new B(a,e)],f=new ke(o,null,0,u,L.UPC_EAN_EXTENSION,new Date().getTime());return s!=null&&f.putAllMetadata(s),f},r.prototype.decodeMiddle=function(e,t,n){var i,a,o=this.decodeMiddleCounters;o[0]=0,o[1]=0,o[2]=0,o[3]=0;for(var s=e.getSize(),u=t[1],f=0,c=0;c<2&&u<s;c++){var l=bt.decodeDigit(e,o,u,bt.L_AND_G_PATTERNS);n+=String.fromCharCode(48+l%10);try{for(var h=(i=void 0,ma(o)),d=h.next();!d.done;d=h.next()){var v=d.value;u+=v}}catch(g){i={error:g}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}l>=10&&(f|=1<<1-c),c!==1&&(u=e.getNextSet(u),u=e.getNextUnset(u))}if(n.length!==2)throw new I;if(parseInt(n.toString())%4!==f)throw new I;return u},r.parseExtensionString=function(e){return e.length!==2?null:new Map([[Me.ISSUE_NUMBER,parseInt(e)]])},r}(),Ia=function(){function r(){}return r.decodeRow=function(e,t,n){var i=bt.findGuardPattern(t,n,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{var a=new Ea;return a.decodeRow(e,t,i)}catch{var o=new Sa;return o.decodeRow(e,t,i)}},r.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]),r}(),Oa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ee=function(r){Oa(e,r);function e(){var t=r.call(this)||this;t.decodeRowStringBuffer="",e.L_AND_G_PATTERNS=e.L_PATTERNS.map(function(s){return Int32Array.from(s)});for(var n=10;n<20;n++){for(var i=e.L_PATTERNS[n-10],a=new Int32Array(i.length),o=0;o<i.length;o++)a[o]=i[i.length-o-1];e.L_AND_G_PATTERNS[n]=a}return t}return e.prototype.decodeRow=function(t,n,i){var a=e.findStartGuardPattern(n),o=i==null?null:i.get(oe.NEED_RESULT_POINT_CALLBACK);if(o!=null){var s=new B((a[0]+a[1])/2,t);o.foundPossibleResultPoint(s)}var u=this.decodeMiddle(n,a,this.decodeRowStringBuffer),f=u.rowOffset,c=u.resultString;if(o!=null){var l=new B(f,t);o.foundPossibleResultPoint(l)}var h=e.decodeEnd(n,f);if(o!=null){var d=new B((h[0]+h[1])/2,t);o.foundPossibleResultPoint(d)}var v=h[1],g=v+(v-h[0]);if(g>=n.getSize()||!n.isRange(v,g,!1))throw new I;var y=c.toString();if(y.length<8)throw new N;if(!e.checkChecksum(y))throw new _e;var _=(a[1]+a[0])/2,x=(h[1]+h[0])/2,A=this.getBarcodeFormat(),m=[new B(_,t),new B(x,t)],S=new ke(y,null,0,m,A,new Date().getTime()),O=0;try{var b=Ia.decodeRow(t,n,h[1]);S.putMetadata(Me.UPC_EAN_EXTENSION,b.getText()),S.putAllMetadata(b.getResultMetadata()),S.addResultPoints(b.getResultPoints()),O=b.getText().length}catch{}var T=i==null?null:i.get(oe.ALLOWED_EAN_EXTENSIONS);if(T!=null){var D=!1;for(var P in T)if(O.toString()===P){D=!0;break}if(!D)throw new I}return A===L.EAN_13||L.UPC_A,S},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var n=t.length;if(n===0)return!1;var i=parseInt(t.charAt(n-1),10);return e.getStandardUPCEANChecksum(t.substring(0,n-1))===i},e.getStandardUPCEANChecksum=function(t){for(var n=t.length,i=0,a=n-1;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new N;i+=o}i*=3;for(var a=n-2;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new N;i+=o}return(1e3-i)%10},e.decodeEnd=function(t,n){return e.findGuardPattern(t,n,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e}(bt),Ta=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),en=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Er=function(r){Ta(e,r);function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return e.prototype.decodeMiddle=function(t,n,i){var a,o,s,u,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var c=t.getSize(),l=n[1],h=0,d=0;d<6&&l<c;d++){var v=Ee.decodeDigit(t,f,l,Ee.L_AND_G_PATTERNS);i+=String.fromCharCode(48+v%10);try{for(var g=(a=void 0,en(f)),y=g.next();!y.done;y=g.next()){var _=y.value;l+=_}}catch(S){a={error:S}}finally{try{y&&!y.done&&(o=g.return)&&o.call(g)}finally{if(a)throw a.error}}v>=10&&(h|=1<<5-d)}i=e.determineFirstDigit(i,h);var x=Ee.findGuardPattern(t,l,!0,Ee.MIDDLE_PATTERN,new Int32Array(Ee.MIDDLE_PATTERN.length).fill(0));l=x[1];for(var d=0;d<6&&l<c;d++){var v=Ee.decodeDigit(t,f,l,Ee.L_PATTERNS);i+=String.fromCharCode(48+v);try{for(var A=(s=void 0,en(f)),m=A.next();!m.done;m=A.next()){var _=m.value;l+=_}}catch(b){s={error:b}}finally{try{m&&!m.done&&(u=A.return)&&u.call(A)}finally{if(s)throw s.error}}}return{rowOffset:l,resultString:i}},e.prototype.getBarcodeFormat=function(){return L.EAN_13},e.determineFirstDigit=function(t,n){for(var i=0;i<10;i++)if(n===this.FIRST_DIGIT_ENCODINGS[i])return t=String.fromCharCode(48+i)+t,t;throw new I},e.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],e}(Ee),ba=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),tn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rn=function(r){ba(e,r);function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return e.prototype.decodeMiddle=function(t,n,i){var a,o,s,u,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var c=t.getSize(),l=n[1],h=0;h<4&&l<c;h++){var d=Ee.decodeDigit(t,f,l,Ee.L_PATTERNS);i+=String.fromCharCode(48+d);try{for(var v=(a=void 0,tn(f)),g=v.next();!g.done;g=v.next()){var y=g.value;l+=y}}catch(m){a={error:m}}finally{try{g&&!g.done&&(o=v.return)&&o.call(v)}finally{if(a)throw a.error}}}var _=Ee.findGuardPattern(t,l,!0,Ee.MIDDLE_PATTERN,new Int32Array(Ee.MIDDLE_PATTERN.length).fill(0));l=_[1];for(var h=0;h<4&&l<c;h++){var d=Ee.decodeDigit(t,f,l,Ee.L_PATTERNS);i+=String.fromCharCode(48+d);try{for(var x=(s=void 0,tn(f)),A=x.next();!A.done;A=x.next()){var y=A.value;l+=y}}catch(O){s={error:O}}finally{try{A&&!A.done&&(u=x.return)&&u.call(x)}finally{if(s)throw s.error}}}return{rowOffset:l,resultString:i}},e.prototype.getBarcodeFormat=function(){return L.EAN_8},e}(Ee),Da=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),nn=function(r){Da(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.ean13Reader=new Er,t}return e.prototype.getBarcodeFormat=function(){return L.UPC_A},e.prototype.decode=function(t,n){return this.maybeReturnResult(this.ean13Reader.decode(t))},e.prototype.decodeRow=function(t,n,i){return this.maybeReturnResult(this.ean13Reader.decodeRow(t,n,i))},e.prototype.decodeMiddle=function(t,n,i){return this.ean13Reader.decodeMiddle(t,n,i)},e.prototype.maybeReturnResult=function(t){var n=t.getText();if(n.charAt(0)==="0"){var i=new ke(n.substring(1),null,null,t.getResultPoints(),L.UPC_A);return t.getResultMetadata()!=null&&i.putAllMetadata(t.getResultMetadata()),i}else throw new I},e.prototype.reset=function(){this.ean13Reader.reset()},e}(Ee),Ra=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Na=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},an=function(r){Ra(e,r);function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=new Int32Array(4),t}return e.prototype.decodeMiddle=function(t,n,i){var a,o,s=this.decodeMiddleCounters.map(function(y){return y});s[0]=0,s[1]=0,s[2]=0,s[3]=0;for(var u=t.getSize(),f=n[1],c=0,l=0;l<6&&f<u;l++){var h=e.decodeDigit(t,s,f,e.L_AND_G_PATTERNS);i+=String.fromCharCode(48+h%10);try{for(var d=(a=void 0,Na(s)),v=d.next();!v.done;v=d.next()){var g=v.value;f+=g}}catch(y){a={error:y}}finally{try{v&&!v.done&&(o=d.return)&&o.call(d)}finally{if(a)throw a.error}}h>=10&&(c|=1<<5-l)}return e.determineNumSysAndCheckDigit(new X(i),c),f},e.prototype.decodeEnd=function(t,n){return e.findGuardPatternWithoutCounters(t,n,!0,e.MIDDLE_END_PATTERN)},e.prototype.checkChecksum=function(t){return Ee.checkChecksum(e.convertUPCEtoUPCA(t))},e.determineNumSysAndCheckDigit=function(t,n){for(var i=0;i<=1;i++)for(var a=0;a<10;a++)if(n===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[i][a]){t.insert(0,"0"+i),t.append("0"+a);return}throw I.getNotFoundInstance()},e.prototype.getBarcodeFormat=function(){return L.UPC_E},e.convertUPCEtoUPCA=function(t){var n=t.slice(1,7).split("").map(function(o){return o.charCodeAt(0)}),i=new X;i.append(t.charAt(0));var a=n[5];switch(a){case 0:case 1:case 2:i.appendChars(n,0,2),i.append(a),i.append("0000"),i.appendChars(n,2,3);break;case 3:i.appendChars(n,0,3),i.append("00000"),i.appendChars(n,3,2);break;case 4:i.appendChars(n,0,4),i.append("00000"),i.append(n[4]);break;default:i.appendChars(n,0,5),i.append("0000"),i.append(a);break}return t.length>=8&&i.append(t.charAt(7)),i.toString()},e.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,1])],e}(Ee),Pa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),on=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},cr=function(r){Pa(e,r);function e(t){var n=r.call(this)||this,i=t==null?null:t.get(oe.POSSIBLE_FORMATS),a=[];return i!=null&&(i.indexOf(L.EAN_13)>-1&&a.push(new Er),i.indexOf(L.UPC_A)>-1&&a.push(new nn),i.indexOf(L.EAN_8)>-1&&a.push(new rn),i.indexOf(L.UPC_E)>-1&&a.push(new an)),a.length===0&&(a.push(new Er),a.push(new nn),a.push(new rn),a.push(new an)),n.readers=a,n}return e.prototype.decodeRow=function(t,n,i){var a,o;try{for(var s=on(this.readers),u=s.next();!u.done;u=s.next()){var f=u.value;try{var c=f.decodeRow(t,n,i),l=c.getBarcodeFormat()===L.EAN_13&&c.getText().charAt(0)==="0",h=i==null?null:i.get(oe.POSSIBLE_FORMATS),d=h==null||h.includes(L.UPC_A);if(l&&d){var v=c.getRawBytes(),g=new ke(c.getText().substring(1),v,v?v.length:null,c.getResultPoints(),L.UPC_A);return g.putAllMetadata(c.getResultMetadata()),g}return c}catch{}}}catch(y){a={error:y}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(a)throw a.error}}throw new I},e.prototype.reset=function(){var t,n;try{for(var i=on(this.readers),a=i.next();!a.done;a=i.next()){var o=a.value;o.reset()}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e}(Ie),Ma=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ba=function(r){Ma(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.CODA_BAR_CHAR_SET={nnnnnww:"0",nnnnwwn:"1",nnnwnnw:"2",wwnnnnn:"3",nnwnnwn:"4",wnnnnwn:"5",nwnnnnw:"6",nwnnwnn:"7",nwwnnnn:"8",wnnwnnn:"9",nnnwwnn:"-",nnwwnnn:"$",wnnnwnw:":",wnwnnnw:"/",wnwnwnn:".",nnwwwww:"+",nnwwnwn:"A",nwnwnnw:"B",nnnwnww:"C",nnnwwwn:"D"},t}return e.prototype.decodeRow=function(t,n,i){var a=this.getValidRowData(n);if(!a)throw new I;var o=this.codaBarDecodeRow(a.row);if(!o)throw new I;return new ke(o,null,0,[new B(a.left,t),new B(a.right,t)],L.CODABAR,new Date().getTime())},e.prototype.getValidRowData=function(t){var n=t.toArray(),i=n.indexOf(!0);if(i===-1)return null;var a=n.lastIndexOf(!0);if(a<=i)return null;n=n.slice(i,a+1);for(var o=[],s=n[0],u=1,f=1;f<n.length;f++)n[f]===s?u++:(s=n[f],o.push(u),u=1);return o.push(u),o.length<23&&(o.length+1)%8!==0?null:{row:o,left:i,right:a}},e.prototype.codaBarDecodeRow=function(t){for(var n=[],i=Math.ceil(t.reduce(function(u,f){return(u+f)/2},0));t.length>0;){var a=t.splice(0,8).splice(0,7),o=a.map(function(u){return u<i?"n":"w"}).join("");if(this.CODA_BAR_CHAR_SET[o]===void 0)return null;n.push(this.CODA_BAR_CHAR_SET[o])}var s=n.join("");return this.validCodaBarString(s)?s:null},e.prototype.validCodaBarString=function(t){var n=/^[A-D].{1,}[A-D]$/;return n.test(t)},e}(Ie),Fa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),La=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},pt=function(r){Fa(e,r);function e(){var t=r.call(this)||this;return t.decodeFinderCounters=new Int32Array(4),t.dataCharacterCounters=new Int32Array(8),t.oddRoundingErrors=new Array(4),t.evenRoundingErrors=new Array(4),t.oddCounts=new Array(t.dataCharacterCounters.length/2),t.evenCounts=new Array(t.dataCharacterCounters.length/2),t}return e.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},e.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},e.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},e.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},e.prototype.getOddCounts=function(){return this.oddCounts},e.prototype.getEvenCounts=function(){return this.evenCounts},e.prototype.parseFinderValue=function(t,n){for(var i=0;i<n.length;i++)if(Ie.patternMatchVariance(t,n[i],e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return i;throw new I},e.count=function(t){return Y.sum(new Int32Array(t))},e.increment=function(t,n){for(var i=0,a=n[0],o=1;o<t.length;o++)n[o]>a&&(a=n[o],i=o);t[i]++},e.decrement=function(t,n){for(var i=0,a=n[0],o=1;o<t.length;o++)n[o]<a&&(a=n[o],i=o);t[i]--},e.isFinderPattern=function(t){var n,i,a=t[0]+t[1],o=a+t[2]+t[3],s=a/o;if(s>=e.MIN_FINDER_PATTERN_RATIO&&s<=e.MAX_FINDER_PATTERN_RATIO){var u=Number.MAX_SAFE_INTEGER,f=Number.MIN_SAFE_INTEGER;try{for(var c=La(t),l=c.next();!l.done;l=c.next()){var h=l.value;h>f&&(f=h),h<u&&(u=h)}}catch(d){n={error:d}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}return f<10*u}return!1},e.MAX_AVG_VARIANCE=.2,e.MAX_INDIVIDUAL_VARIANCE=.45,e.MIN_FINDER_PATTERN_RATIO=9.5/12,e.MAX_FINDER_PATTERN_RATIO=12.5/14,e}(Ie),Jt=function(){function r(e,t){this.value=e,this.checksumPortion=t}return r.prototype.getValue=function(){return this.value},r.prototype.getChecksumPortion=function(){return this.checksumPortion},r.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value&&this.checksumPortion===t.checksumPortion},r.prototype.hashCode=function(){return this.value^this.checksumPortion},r}(),Pn=function(){function r(e,t,n,i,a){this.value=e,this.startEnd=t,this.value=e,this.startEnd=t,this.resultPoints=new Array,this.resultPoints.push(new B(n,a)),this.resultPoints.push(new B(i,a))}return r.prototype.getValue=function(){return this.value},r.prototype.getStartEnd=function(){return this.startEnd},r.prototype.getResultPoints=function(){return this.resultPoints},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value},r.prototype.hashCode=function(){return this.value},r}(),ka=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},mt=function(){function r(){}return r.getRSSvalue=function(e,t,n){var i,a,o=0;try{for(var s=ka(e),u=s.next();!u.done;u=s.next()){var f=u.value;o+=f}}catch(x){i={error:x}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}for(var c=0,l=0,h=e.length,d=0;d<h-1;d++){var v=void 0;for(v=1,l|=1<<d;v<e[d];v++,l&=~(1<<d)){var g=r.combins(o-v-1,h-d-2);if(n&&l===0&&o-v-(h-d-1)>=h-d-1&&(g-=r.combins(o-v-(h-d),h-d-2)),h-d-1>1){for(var y=0,_=o-v-(h-d-2);_>t;_--)y+=r.combins(o-v-_-1,h-d-3);g-=y*(h-1-d)}else o-v>t&&g--;c+=g}o-=v}return c},r.combins=function(e,t){var n,i;e-t>t?(i=t,n=e-t):(i=e-t,n=t);for(var a=1,o=1,s=e;s>n;s--)a*=s,o<=i&&(a/=o,o++);for(;o<=i;)a/=o,o++;return a},r}(),Ua=function(){function r(){}return r.buildBitArray=function(e){var t=e.length*2-1;e[e.length-1].getRightChar()==null&&(t-=1);for(var n=12*t,i=new be(n),a=0,o=e[0],s=o.getRightChar().getValue(),u=11;u>=0;--u)s&1<<u&&i.set(a),a++;for(var u=1;u<e.length;++u){for(var f=e[u],c=f.getLeftChar().getValue(),l=11;l>=0;--l)c&1<<l&&i.set(a),a++;if(f.getRightChar()!==null)for(var h=f.getRightChar().getValue(),l=11;l>=0;--l)h&1<<l&&i.set(a),a++}return i},r}(),ht=function(){function r(e,t){t?this.decodedInformation=null:(this.finished=e,this.decodedInformation=t)}return r.prototype.getDecodedInformation=function(){return this.decodedInformation},r.prototype.isFinished=function(){return this.finished},r}(),Pr=function(){function r(e){this.newPosition=e}return r.prototype.getNewPosition=function(){return this.newPosition},r}(),Va=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Qe=function(r){Va(e,r);function e(t,n){var i=r.call(this,t)||this;return i.value=n,i}return e.prototype.getValue=function(){return this.value},e.prototype.isFNC1=function(){return this.value===e.FNC1},e.FNC1="$",e}(Pr),Ha=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),vt=function(r){Ha(e,r);function e(t,n,i){var a=r.call(this,t)||this;return i?(a.remaining=!0,a.remainingValue=a.remainingValue):(a.remaining=!1,a.remainingValue=0),a.newString=n,a}return e.prototype.getNewString=function(){return this.newString},e.prototype.isRemaining=function(){return this.remaining},e.prototype.getRemainingValue=function(){return this.remainingValue},e}(Pr),Ga=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),yt=function(r){Ga(e,r);function e(t,n,i){var a=r.call(this,t)||this;if(n<0||n>10||i<0||i>10)throw new N;return a.firstDigit=n,a.secondDigit=i,a}return e.prototype.getFirstDigit=function(){return this.firstDigit},e.prototype.getSecondDigit=function(){return this.secondDigit},e.prototype.getValue=function(){return this.firstDigit*10+this.secondDigit},e.prototype.isFirstDigitFNC1=function(){return this.firstDigit===e.FNC1},e.prototype.isSecondDigitFNC1=function(){return this.secondDigit===e.FNC1},e.prototype.isAnyFNC1=function(){return this.firstDigit===e.FNC1||this.secondDigit===e.FNC1},e.FNC1=10,e}(Pr),zt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Xa=function(){function r(){}return r.parseFieldsInGeneralPurpose=function(e){var t,n,i,a,o,s,u,f;if(!e)return null;if(e.length<2)throw new I;var c=e.substring(0,2);try{for(var l=zt(r.TWO_DIGIT_DATA_LENGTH),h=l.next();!h.done;h=l.next()){var d=h.value;if(d[0]===c)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(2,d[2],e):r.processFixedAI(2,d[1],e)}}catch(O){t={error:O}}finally{try{h&&!h.done&&(n=l.return)&&n.call(l)}finally{if(t)throw t.error}}if(e.length<3)throw new I;var v=e.substring(0,3);try{for(var g=zt(r.THREE_DIGIT_DATA_LENGTH),y=g.next();!y.done;y=g.next()){var d=y.value;if(d[0]===v)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(3,d[2],e):r.processFixedAI(3,d[1],e)}}catch(O){i={error:O}}finally{try{y&&!y.done&&(a=g.return)&&a.call(g)}finally{if(i)throw i.error}}try{for(var _=zt(r.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),x=_.next();!x.done;x=_.next()){var d=x.value;if(d[0]===v)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(4,d[2],e):r.processFixedAI(4,d[1],e)}}catch(O){o={error:O}}finally{try{x&&!x.done&&(s=_.return)&&s.call(_)}finally{if(o)throw o.error}}if(e.length<4)throw new I;var A=e.substring(0,4);try{for(var m=zt(r.FOUR_DIGIT_DATA_LENGTH),S=m.next();!S.done;S=m.next()){var d=S.value;if(d[0]===A)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(4,d[2],e):r.processFixedAI(4,d[1],e)}}catch(O){u={error:O}}finally{try{S&&!S.done&&(f=m.return)&&f.call(m)}finally{if(u)throw u.error}}throw new I},r.processFixedAI=function(e,t,n){if(n.length<e)throw new I;var i=n.substring(0,e);if(n.length<e+t)throw new I;var a=n.substring(e,e+t),o=n.substring(e+t),s="("+i+")"+a,u=r.parseFieldsInGeneralPurpose(o);return u==null?s:s+u},r.processVariableAI=function(e,t,n){var i=n.substring(0,e),a;n.length<e+t?a=n.length:a=e+t;var o=n.substring(e,a),s=n.substring(a),u="("+i+")"+o,f=r.parseFieldsInGeneralPurpose(s);return f==null?u:u+f},r.VARIABLE_LENGTH=[],r.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",r.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",r.VARIABLE_LENGTH,20],["22",r.VARIABLE_LENGTH,29],["30",r.VARIABLE_LENGTH,8],["37",r.VARIABLE_LENGTH,8],["90",r.VARIABLE_LENGTH,30],["91",r.VARIABLE_LENGTH,30],["92",r.VARIABLE_LENGTH,30],["93",r.VARIABLE_LENGTH,30],["94",r.VARIABLE_LENGTH,30],["95",r.VARIABLE_LENGTH,30],["96",r.VARIABLE_LENGTH,30],["97",r.VARIABLE_LENGTH,3],["98",r.VARIABLE_LENGTH,30],["99",r.VARIABLE_LENGTH,30]],r.THREE_DIGIT_DATA_LENGTH=[["240",r.VARIABLE_LENGTH,30],["241",r.VARIABLE_LENGTH,30],["242",r.VARIABLE_LENGTH,6],["250",r.VARIABLE_LENGTH,30],["251",r.VARIABLE_LENGTH,30],["253",r.VARIABLE_LENGTH,17],["254",r.VARIABLE_LENGTH,20],["400",r.VARIABLE_LENGTH,30],["401",r.VARIABLE_LENGTH,30],["402",17],["403",r.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",r.VARIABLE_LENGTH,20],["421",r.VARIABLE_LENGTH,15],["422",3],["423",r.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],r.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",r.VARIABLE_LENGTH,15],["391",r.VARIABLE_LENGTH,18],["392",r.VARIABLE_LENGTH,15],["393",r.VARIABLE_LENGTH,18],["703",r.VARIABLE_LENGTH,30]],r.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",r.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",r.VARIABLE_LENGTH,20],["8003",r.VARIABLE_LENGTH,30],["8004",r.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",r.VARIABLE_LENGTH,30],["8008",r.VARIABLE_LENGTH,12],["8018",18],["8020",r.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",r.VARIABLE_LENGTH,70],["8200",r.VARIABLE_LENGTH,70]],r}(),Kt=function(){function r(e){this.buffer=new X,this.information=e}return r.prototype.decodeAllCodes=function(e,t){var n=t,i=null;do{var a=this.decodeGeneralPurposeField(n,i),o=Xa.parseFieldsInGeneralPurpose(a.getNewString());if(o!=null&&e.append(o),a.isRemaining()?i=""+a.getRemainingValue():i=null,n===a.getNewPosition())break;n=a.getNewPosition()}while(!0);return e.toString()},r.prototype.isStillNumeric=function(e){if(e+7>this.information.getSize())return e+4<=this.information.getSize();for(var t=e;t<e+3;++t)if(this.information.get(t))return!0;return this.information.get(e+3)},r.prototype.decodeNumeric=function(e){if(e+7>this.information.getSize()){var t=this.extractNumericValueFromBitArray(e,4);return t===0?new yt(this.information.getSize(),yt.FNC1,yt.FNC1):new yt(this.information.getSize(),t-1,yt.FNC1)}var n=this.extractNumericValueFromBitArray(e,7),i=(n-8)/11,a=(n-8)%11;return new yt(e+7,i,a)},r.prototype.extractNumericValueFromBitArray=function(e,t){return r.extractNumericValueFromBitArray(this.information,e,t)},r.extractNumericValueFromBitArray=function(e,t,n){for(var i=0,a=0;a<n;++a)e.get(t+a)&&(i|=1<<n-a-1);return i},r.prototype.decodeGeneralPurposeField=function(e,t){this.buffer.setLengthToZero(),t!=null&&this.buffer.append(t),this.current.setPosition(e);var n=this.parseBlocks();return n!=null&&n.isRemaining()?new vt(this.current.getPosition(),this.buffer.toString(),n.getRemainingValue()):new vt(this.current.getPosition(),this.buffer.toString())},r.prototype.parseBlocks=function(){var e,t;do{var n=this.current.getPosition();this.current.isAlpha()?(t=this.parseAlphaBlock(),e=t.isFinished()):this.current.isIsoIec646()?(t=this.parseIsoIec646Block(),e=t.isFinished()):(t=this.parseNumericBlock(),e=t.isFinished());var i=n!==this.current.getPosition();if(!i&&!e)break}while(!e);return t.getDecodedInformation()},r.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var e=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFirstDigitFNC1()){var t=void 0;return e.isSecondDigitFNC1()?t=new vt(this.current.getPosition(),this.buffer.toString()):t=new vt(this.current.getPosition(),this.buffer.toString(),e.getSecondDigit()),new ht(!0,t)}if(this.buffer.append(e.getFirstDigit()),e.isSecondDigitFNC1()){var t=new vt(this.current.getPosition(),this.buffer.toString());return new ht(!0,t)}this.buffer.append(e.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new ht(!1)},r.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var e=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){var t=new vt(this.current.getPosition(),this.buffer.toString());return new ht(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new ht(!1)},r.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var e=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){var t=new vt(this.current.getPosition(),this.buffer.toString());return new ht(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new ht(!1)},r.prototype.isStillIsoIec646=function(e){if(e+5>this.information.getSize())return!1;var t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+7>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<116)return!0;if(e+8>this.information.getSize())return!1;var i=this.extractNumericValueFromBitArray(e,8);return i>=232&&i<253},r.prototype.decodeIsoIec646=function(e){var t=this.extractNumericValueFromBitArray(e,5);if(t===15)return new Qe(e+5,Qe.FNC1);if(t>=5&&t<15)return new Qe(e+5,"0"+(t-5));var n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<90)return new Qe(e+7,""+(n+1));if(n>=90&&n<116)return new Qe(e+7,""+(n+7));var i=this.extractNumericValueFromBitArray(e,8),a;switch(i){case 232:a="!";break;case 233:a='"';break;case 234:a="%";break;case 235:a="&";break;case 236:a="'";break;case 237:a="(";break;case 238:a=")";break;case 239:a="*";break;case 240:a="+";break;case 241:a=",";break;case 242:a="-";break;case 243:a=".";break;case 244:a="/";break;case 245:a=":";break;case 246:a=";";break;case 247:a="<";break;case 248:a="=";break;case 249:a=">";break;case 250:a="?";break;case 251:a="_";break;case 252:a=" ";break;default:throw new N}return new Qe(e+8,a)},r.prototype.isStillAlpha=function(e){if(e+5>this.information.getSize())return!1;var t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+6>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(e,6);return n>=16&&n<63},r.prototype.decodeAlphanumeric=function(e){var t=this.extractNumericValueFromBitArray(e,5);if(t===15)return new Qe(e+5,Qe.FNC1);if(t>=5&&t<15)return new Qe(e+5,"0"+(t-5));var n=this.extractNumericValueFromBitArray(e,6);if(n>=32&&n<58)return new Qe(e+6,""+(n+33));var i;switch(n){case 58:i="*";break;case 59:i=",";break;case 60:i="-";break;case 61:i=".";break;case 62:i="/";break;default:throw new Gt("Decoding invalid alphanumeric value: "+n)}return new Qe(e+6,i)},r.prototype.isAlphaTo646ToAlphaLatch=function(e){if(e+1>this.information.getSize())return!1;for(var t=0;t<5&&t+e<this.information.getSize();++t)if(t===2){if(!this.information.get(e+2))return!1}else if(this.information.get(e+t))return!1;return!0},r.prototype.isAlphaOr646ToNumericLatch=function(e){if(e+3>this.information.getSize())return!1;for(var t=e;t<e+3;++t)if(this.information.get(t))return!1;return!0},r.prototype.isNumericToAlphaNumericLatch=function(e){if(e+1>this.information.getSize())return!1;for(var t=0;t<4&&t+e<this.information.getSize();++t)if(this.information.get(e+t))return!1;return!0},r}(),Mn=function(){function r(e){this.information=e,this.generalDecoder=new Kt(e)}return r.prototype.getInformation=function(){return this.information},r.prototype.getGeneralDecoder=function(){return this.generalDecoder},r}(),Wa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Je=function(r){Wa(e,r);function e(t){return r.call(this,t)||this}return e.prototype.encodeCompressedGtin=function(t,n){t.append("(01)");var i=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,n,i)},e.prototype.encodeCompressedGtinWithoutAI=function(t,n,i){for(var a=0;a<4;++a){var o=this.getGeneralDecoder().extractNumericValueFromBitArray(n+10*a,10);o/100===0&&t.append("0"),o/10===0&&t.append("0"),t.append(o)}e.appendCheckDigit(t,i)},e.appendCheckDigit=function(t,n){for(var i=0,a=0;a<13;a++){var o=t.charAt(a+n).charCodeAt(0)-48;i+=a&1?o:3*o}i=10-i%10,i===10&&(i=0),t.append(i)},e.GTIN_SIZE=40,e}(Mn),za=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),$a=function(r){za(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){var t=new X;t.append("(01)");var n=t.length(),i=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE,4);return t.append(i),this.encodeCompressedGtinWithoutAI(t,e.HEADER_SIZE+4,n),this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE+44)},e.HEADER_SIZE=4,e}(Je),ja=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ya=function(r){ja(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){var t=new X;return this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE)},e.HEADER_SIZE=5,e}(Mn),Za=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),qt=function(r){Za(e,r);function e(t){return r.call(this,t)||this}return e.prototype.encodeCompressedWeight=function(t,n,i){var a=this.getGeneralDecoder().extractNumericValueFromBitArray(n,i);this.addWeightCode(t,a);for(var o=this.checkWeight(a),s=1e5,u=0;u<5;++u)o/s===0&&t.append("0"),s/=10;t.append(o)},e}(Je),Ka=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Bn=function(r){Ka(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+qt.GTIN_SIZE+e.WEIGHT_SIZE)throw new I;var t=new X;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+qt.GTIN_SIZE,e.WEIGHT_SIZE),t.toString()},e.HEADER_SIZE=5,e.WEIGHT_SIZE=15,e}(qt),qa=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Qa=function(r){qa(e,r);function e(t){return r.call(this,t)||this}return e.prototype.addWeightCode=function(t,n){t.append("(3103)")},e.prototype.checkWeight=function(t){return t},e}(Bn),Ja=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),eo=function(r){Ja(e,r);function e(t){return r.call(this,t)||this}return e.prototype.addWeightCode=function(t,n){n<1e4?t.append("(3202)"):t.append("(3203)")},e.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},e}(Bn),to=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ro=function(r){to(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+Je.GTIN_SIZE)throw new I;var t=new X;this.encodeCompressedGtin(t,e.HEADER_SIZE);var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Je.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(392"),t.append(n),t.append(")");var i=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+Je.GTIN_SIZE+e.LAST_DIGIT_SIZE,null);return t.append(i.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e}(Je),no=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),io=function(r){no(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+Je.GTIN_SIZE)throw new I;var t=new X;this.encodeCompressedGtin(t,e.HEADER_SIZE);var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Je.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(393"),t.append(n),t.append(")");var i=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Je.GTIN_SIZE+e.LAST_DIGIT_SIZE,e.FIRST_THREE_DIGITS_SIZE);i/100===0&&t.append("0"),i/10===0&&t.append("0"),t.append(i);var a=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+Je.GTIN_SIZE+e.LAST_DIGIT_SIZE+e.FIRST_THREE_DIGITS_SIZE,null);return t.append(a.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e.FIRST_THREE_DIGITS_SIZE=10,e}(Je),ao=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ot=function(r){ao(e,r);function e(t,n,i){var a=r.call(this,t)||this;return a.dateCode=i,a.firstAIdigits=n,a}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE+e.DATE_SIZE)throw new I;var t=new X;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+e.GTIN_SIZE,e.WEIGHT_SIZE),this.encodeCompressedDate(t,e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE),t.toString()},e.prototype.encodeCompressedDate=function(t,n){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(n,e.DATE_SIZE);if(i!==38400){t.append("("),t.append(this.dateCode),t.append(")");var a=i%32;i/=32;var o=i%12+1;i/=12;var s=i;s/10===0&&t.append("0"),t.append(s),o/10===0&&t.append("0"),t.append(o),a/10===0&&t.append("0"),t.append(a)}},e.prototype.addWeightCode=function(t,n){t.append("("),t.append(this.firstAIdigits),t.append(n/1e5),t.append(")")},e.prototype.checkWeight=function(t){return t%1e5},e.HEADER_SIZE=8,e.WEIGHT_SIZE=20,e.DATE_SIZE=16,e}(qt);function oo(r){try{if(r.get(1))return new $a(r);if(!r.get(2))return new Ya(r);var e=Kt.extractNumericValueFromBitArray(r,1,4);switch(e){case 4:return new Qa(r);case 5:return new eo(r)}var t=Kt.extractNumericValueFromBitArray(r,1,5);switch(t){case 12:return new ro(r);case 13:return new io(r)}var n=Kt.extractNumericValueFromBitArray(r,1,7);switch(n){case 56:return new ot(r,"310","11");case 57:return new ot(r,"320","11");case 58:return new ot(r,"310","13");case 59:return new ot(r,"320","13");case 60:return new ot(r,"310","15");case 61:return new ot(r,"320","15");case 62:return new ot(r,"310","17");case 63:return new ot(r,"320","17")}}catch(i){throw console.log(i),new Gt("unknown decoder: "+r)}}var sn=function(){function r(e,t,n,i){this.leftchar=e,this.rightchar=t,this.finderpattern=n,this.maybeLast=i}return r.prototype.mayBeLast=function(){return this.maybeLast},r.prototype.getLeftChar=function(){return this.leftchar},r.prototype.getRightChar=function(){return this.rightchar},r.prototype.getFinderPattern=function(){return this.finderpattern},r.prototype.mustBeLast=function(){return this.rightchar==null},r.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(this.finderpattern==null?"null":this.finderpattern.getValue())+" ]"},r.equals=function(e,t){return e instanceof r?r.equalsOrNull(e.leftchar,t.leftchar)&&r.equalsOrNull(e.rightchar,t.rightchar)&&r.equalsOrNull(e.finderpattern,t.finderpattern):!1},r.equalsOrNull=function(e,t){return e===null?t===null:r.equals(e,t)},r.prototype.hashCode=function(){var e=this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue();return e},r}(),so=function(){function r(e,t,n){this.pairs=e,this.rowNumber=t,this.wasReversed=n}return r.prototype.getPairs=function(){return this.pairs},r.prototype.getRowNumber=function(){return this.rowNumber},r.prototype.isReversed=function(){return this.wasReversed},r.prototype.isEquivalent=function(e){return this.checkEqualitity(this,e)},r.prototype.toString=function(){return"{ "+this.pairs+" }"},r.prototype.equals=function(e,t){return e instanceof r?this.checkEqualitity(e,t)&&e.wasReversed===t.wasReversed:!1},r.prototype.checkEqualitity=function(e,t){if(!(!e||!t)){var n;return e.forEach(function(i,a){t.forEach(function(o){i.getLeftChar().getValue()===o.getLeftChar().getValue()&&i.getRightChar().getValue()===o.getRightChar().getValue()&&i.getFinderPatter().getValue()===o.getFinderPatter().getValue()&&(n=!0)})}),n}},r}(),uo=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),st=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},fo=function(r){uo(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.pairs=new Array(e.MAX_PAIRS),t.rows=new Array,t.startEnd=[2],t}return e.prototype.decodeRow=function(t,n,i){this.pairs.length=0,this.startFromEven=!1;try{return e.constructResult(this.decodeRow2pairs(t,n))}catch{}return this.pairs.length=0,this.startFromEven=!0,e.constructResult(this.decodeRow2pairs(t,n))},e.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},e.prototype.decodeRow2pairs=function(t,n){for(var i=!1;!i;)try{this.pairs.push(this.retrieveNextPair(n,this.pairs,t))}catch(s){if(s instanceof I){if(!this.pairs.length)throw new I;i=!0}}if(this.checkChecksum())return this.pairs;var a;if(this.rows.length?a=!0:a=!1,this.storeRow(t,!1),a){var o=this.checkRowsBoolean(!1);if(o!=null||(o=this.checkRowsBoolean(!0),o!=null))return o}throw new I},e.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var n=null;try{n=this.checkRows(new Array,0)}catch(i){console.log(i)}return t&&(this.rows=this.rows.reverse()),n},e.prototype.checkRows=function(t,n){for(var i,a,o=n;o<this.rows.length;o++){var s=this.rows[o];this.pairs.length=0;try{for(var u=(i=void 0,st(t)),f=u.next();!f.done;f=u.next()){var c=f.value;this.pairs.push(c.getPairs())}}catch(h){i={error:h}}finally{try{f&&!f.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}if(this.pairs.push(s.getPairs()),!!e.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var l=new Array(t);l.push(s);try{return this.checkRows(l,o+1)}catch(h){console.log(h)}}}throw new I},e.isValidSequence=function(t){var n,i;try{for(var a=st(e.FINDER_PATTERN_SEQUENCES),o=a.next();!o.done;o=a.next()){var s=o.value;if(!(t.length>s.length)){for(var u=!0,f=0;f<t.length;f++)if(t[f].getFinderPattern().getValue()!==s[f]){u=!1;break}if(u)return!0}}}catch(c){n={error:c}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return!1},e.prototype.storeRow=function(t,n){for(var i=0,a=!1,o=!1;i<this.rows.length;){var s=this.rows[i];if(s.getRowNumber()>t){o=s.isEquivalent(this.pairs);break}a=s.isEquivalent(this.pairs),i++}o||a||e.isPartialRow(this.pairs,this.rows)||(this.rows.push(i,new so(this.pairs,t,n)),this.removePartialRows(this.pairs,this.rows))},e.prototype.removePartialRows=function(t,n){var i,a,o,s,u,f;try{for(var c=st(n),l=c.next();!l.done;l=c.next()){var h=l.value;if(h.getPairs().length!==t.length){var d=!0;try{for(var v=(o=void 0,st(h.getPairs())),g=v.next();!g.done;g=v.next()){var y=g.value,_=!1;try{for(var x=(u=void 0,st(t)),A=x.next();!A.done;A=x.next()){var m=A.value;if(sn.equals(y,m)){_=!0;break}}}catch(S){u={error:S}}finally{try{A&&!A.done&&(f=x.return)&&f.call(x)}finally{if(u)throw u.error}}_||(d=!1)}}catch(S){o={error:S}}finally{try{g&&!g.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}}}}catch(S){i={error:S}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}},e.isPartialRow=function(t,n){var i,a,o,s,u,f;try{for(var c=st(n),l=c.next();!l.done;l=c.next()){var h=l.value,d=!0;try{for(var v=(o=void 0,st(t)),g=v.next();!g.done;g=v.next()){var y=g.value,_=!1;try{for(var x=(u=void 0,st(h.getPairs())),A=x.next();!A.done;A=x.next()){var m=A.value;if(y.equals(m)){_=!0;break}}}catch(S){u={error:S}}finally{try{A&&!A.done&&(f=x.return)&&f.call(x)}finally{if(u)throw u.error}}if(!_){d=!1;break}}}catch(S){o={error:S}}finally{try{g&&!g.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}if(d)return!0}}catch(S){i={error:S}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}return!1},e.prototype.getRows=function(){return this.rows},e.constructResult=function(t){var n=Ua.buildBitArray(t),i=oo(n),a=i.parseInformation(),o=t[0].getFinderPattern().getResultPoints(),s=t[t.length-1].getFinderPattern().getResultPoints(),u=[o[0],o[1],s[0],s[1]];return new ke(a,null,null,u,L.RSS_EXPANDED,null)},e.prototype.checkChecksum=function(){var t=this.pairs.get(0),n=t.getLeftChar(),i=t.getRightChar();if(i===null)return!1;for(var a=i.getChecksumPortion(),o=2,s=1;s<this.pairs.size();++s){var u=this.pairs.get(s);a+=u.getLeftChar().getChecksumPortion(),o++;var f=u.getRightChar();f!=null&&(a+=f.getChecksumPortion(),o++)}a%=211;var c=211*(o-4)+a;return c===n.getValue()},e.getNextSecondBar=function(t,n){var i;return t.get(n)?(i=t.getNextUnset(n),i=t.getNextSet(i)):(i=t.getNextSet(n),i=t.getNextUnset(i)),i},e.prototype.retrieveNextPair=function(t,n,i){var a=n.length%2===0;this.startFromEven&&(a=!a);var o,s=!0,u=-1;do this.findNextPair(t,n,u),o=this.parseFoundFinderPattern(t,i,a),o===null?u=e.getNextSecondBar(t,this.startEnd[0]):s=!1;while(s);var f=this.decodeDataCharacter(t,o,a,!0);if(!this.isEmptyPair(n)&&n[n.length-1].mustBeLast())throw new I;var c;try{c=this.decodeDataCharacter(t,o,a,!1)}catch(l){c=null,console.log(l)}return new sn(f,c,o,!0)},e.prototype.isEmptyPair=function(t){return t.length===0},e.prototype.findNextPair=function(t,n,i){var a=this.getDecodeFinderCounters();a[0]=0,a[1]=0,a[2]=0,a[3]=0;var o=t.getSize(),s;if(i>=0)s=i;else if(this.isEmptyPair(n))s=0;else{var u=n[n.length-1];s=u.getFinderPattern().getStartEnd()[1]}var f=n.length%2!==0;this.startFromEven&&(f=!f);for(var c=!1;s<o&&(c=!t.get(s),!!c);)s++;for(var l=0,h=s,d=s;d<o;d++)if(t.get(d)!==c)a[l]++;else{if(l===3){if(f&&e.reverseCounters(a),e.isFinderPattern(a)){this.startEnd[0]=h,this.startEnd[1]=d;return}f&&e.reverseCounters(a),h+=a[0]+a[1],a[0]=a[2],a[1]=a[3],a[2]=0,a[3]=0,l--}else l++;a[l]=1,c=!c}throw new I},e.reverseCounters=function(t){for(var n=t.length,i=0;i<n/2;++i){var a=t[i];t[i]=t[n-i-1],t[n-i-1]=a}},e.prototype.parseFoundFinderPattern=function(t,n,i){var a,o,s;if(i){for(var u=this.startEnd[0]-1;u>=0&&!t.get(u);)u--;u++,a=this.startEnd[0]-u,o=u,s=this.startEnd[1]}else o=this.startEnd[0],s=t.getNextUnset(this.startEnd[1]+1),a=s-this.startEnd[1];var f=this.getDecodeFinderCounters();ue.arraycopy(f,0,f,1,f.length-1),f[0]=a;var c;try{c=this.parseFinderValue(f,e.FINDER_PATTERNS)}catch{return null}return new Pn(c,[o,s],o,s,n)},e.prototype.decodeDataCharacter=function(t,n,i,a){for(var o=this.getDataCharacterCounters(),s=0;s<o.length;s++)o[s]=0;if(a)e.recordPatternInReverse(t,n.getStartEnd()[0],o);else{e.recordPattern(t,n.getStartEnd()[1],o);for(var u=0,f=o.length-1;u<f;u++,f--){var c=o[u];o[u]=o[f],o[f]=c}}var l=17,h=Y.sum(new Int32Array(o))/l,d=(n.getStartEnd()[1]-n.getStartEnd()[0])/15;if(Math.abs(h-d)/d>.3)throw new I;for(var v=this.getOddCounts(),g=this.getEvenCounts(),y=this.getOddRoundingErrors(),_=this.getEvenRoundingErrors(),u=0;u<o.length;u++){var x=1*o[u]/h,A=x+.5;if(A<1){if(x<.3)throw new I;A=1}else if(A>8){if(x>8.7)throw new I;A=8}var m=u/2;u&1?(g[m]=A,_[m]=x-A):(v[m]=A,y[m]=x-A)}this.adjustOddEvenCounts(l);for(var S=4*n.getValue()+(i?0:2)+(a?0:1)-1,O=0,b=0,u=v.length-1;u>=0;u--){if(e.isNotA1left(n,i,a)){var T=e.WEIGHTS[S][2*u];b+=v[u]*T}O+=v[u]}for(var D=0,u=g.length-1;u>=0;u--)if(e.isNotA1left(n,i,a)){var T=e.WEIGHTS[S][2*u+1];D+=g[u]*T}var P=b+D;if(O&1||O>13||O<4)throw new I;var G=(13-O)/2,H=e.SYMBOL_WIDEST[G],se=9-H,Be=mt.getRSSvalue(v,H,!0),W=mt.getRSSvalue(g,se,!1),lt=e.EVEN_TOTAL_SUBSET[G],Nt=e.GSUM[G],Pt=Be*lt+W+Nt;return new Jt(Pt,P)},e.isNotA1left=function(t,n,i){return!(t.getValue()===0&&n&&i)},e.prototype.adjustOddEvenCounts=function(t){var n=Y.sum(new Int32Array(this.getOddCounts())),i=Y.sum(new Int32Array(this.getEvenCounts())),a=!1,o=!1;n>13?o=!0:n<4&&(a=!0);var s=!1,u=!1;i>13?u=!0:i<4&&(s=!0);var f=n+i-t,c=(n&1)===1,l=(i&1)===0;if(f===1)if(c){if(l)throw new I;o=!0}else{if(!l)throw new I;u=!0}else if(f===-1)if(c){if(l)throw new I;a=!0}else{if(!l)throw new I;s=!0}else if(f===0){if(c){if(!l)throw new I;n<i?(a=!0,u=!0):(o=!0,s=!0)}else if(l)throw new I}else throw new I;if(a){if(o)throw new I;e.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(o&&e.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(u)throw new I;e.increment(this.getEvenCounts(),this.getOddRoundingErrors())}u&&e.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.SYMBOL_WIDEST=[7,5,4,3,1],e.EVEN_TOTAL_SUBSET=[4,20,52,104,204],e.GSUM=[0,348,1388,2948,3988],e.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],e.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],e.FINDER_PAT_A=0,e.FINDER_PAT_B=1,e.FINDER_PAT_C=2,e.FINDER_PAT_D=3,e.FINDER_PAT_E=4,e.FINDER_PAT_F=5,e.FINDER_PATTERN_SEQUENCES=[[e.FINDER_PAT_A,e.FINDER_PAT_A],[e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B],[e.FINDER_PAT_A,e.FINDER_PAT_C,e.FINDER_PAT_B,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_C],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F]],e.MAX_PAIRS=11,e}(pt),co=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),lo=function(r){co(e,r);function e(t,n,i){var a=r.call(this,t,n)||this;return a.count=0,a.finderPattern=i,a}return e.prototype.getFinderPattern=function(){return this.finderPattern},e.prototype.getCount=function(){return this.count},e.prototype.incrementCount=function(){this.count++},e}(Jt),ho=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),lr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},un=function(r){ho(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.possibleLeftPairs=[],t.possibleRightPairs=[],t}return e.prototype.decodeRow=function(t,n,i){var a,o,s,u,f=this.decodePair(n,!1,t,i);e.addOrTally(this.possibleLeftPairs,f),n.reverse();var c=this.decodePair(n,!0,t,i);e.addOrTally(this.possibleRightPairs,c),n.reverse();try{for(var l=lr(this.possibleLeftPairs),h=l.next();!h.done;h=l.next()){var d=h.value;if(d.getCount()>1)try{for(var v=(s=void 0,lr(this.possibleRightPairs)),g=v.next();!g.done;g=v.next()){var y=g.value;if(y.getCount()>1&&e.checkChecksum(d,y))return e.constructResult(d,y)}}catch(_){s={error:_}}finally{try{g&&!g.done&&(u=v.return)&&u.call(v)}finally{if(s)throw s.error}}}}catch(_){a={error:_}}finally{try{h&&!h.done&&(o=l.return)&&o.call(l)}finally{if(a)throw a.error}}throw new I},e.addOrTally=function(t,n){var i,a;if(n!=null){var o=!1;try{for(var s=lr(t),u=s.next();!u.done;u=s.next()){var f=u.value;if(f.getValue()===n.getValue()){f.incrementCount(),o=!0;break}}}catch(c){i={error:c}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}o||t.push(n)}},e.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},e.constructResult=function(t,n){for(var i=4537077*t.getValue()+n.getValue(),a=new String(i).toString(),o=new X,s=13-a.length;s>0;s--)o.append("0");o.append(a);for(var u=0,s=0;s<13;s++){var f=o.charAt(s).charCodeAt(0)-48;u+=s&1?f:3*f}u=10-u%10,u===10&&(u=0),o.append(u.toString());var c=t.getFinderPattern().getResultPoints(),l=n.getFinderPattern().getResultPoints();return new ke(o.toString(),null,0,[c[0],c[1],l[0],l[1]],L.RSS_14,new Date().getTime())},e.checkChecksum=function(t,n){var i=(t.getChecksumPortion()+16*n.getChecksumPortion())%79,a=9*t.getFinderPattern().getValue()+n.getFinderPattern().getValue();return a>72&&a--,a>8&&a--,i===a},e.prototype.decodePair=function(t,n,i,a){try{var o=this.findFinderPattern(t,n),s=this.parseFoundFinderPattern(t,i,n,o),u=a==null?null:a.get(oe.NEED_RESULT_POINT_CALLBACK);if(u!=null){var f=(o[0]+o[1])/2;n&&(f=t.getSize()-1-f),u.foundPossibleResultPoint(new B(f,i))}var c=this.decodeDataCharacter(t,s,!0),l=this.decodeDataCharacter(t,s,!1);return new lo(1597*c.getValue()+l.getValue(),c.getChecksumPortion()+4*l.getChecksumPortion(),s)}catch{return null}},e.prototype.decodeDataCharacter=function(t,n,i){for(var a=this.getDataCharacterCounters(),o=0;o<a.length;o++)a[o]=0;if(i)Ie.recordPatternInReverse(t,n.getStartEnd()[0],a);else{Ie.recordPattern(t,n.getStartEnd()[1]+1,a);for(var s=0,u=a.length-1;s<u;s++,u--){var f=a[s];a[s]=a[u],a[u]=f}}for(var c=i?16:15,l=Y.sum(new Int32Array(a))/c,h=this.getOddCounts(),d=this.getEvenCounts(),v=this.getOddRoundingErrors(),g=this.getEvenRoundingErrors(),s=0;s<a.length;s++){var y=a[s]/l,_=Math.floor(y+.5);_<1?_=1:_>8&&(_=8);var x=Math.floor(s/2);s&1?(d[x]=_,g[x]=y-_):(h[x]=_,v[x]=y-_)}this.adjustOddEvenCounts(i,c);for(var A=0,m=0,s=h.length-1;s>=0;s--)m*=9,m+=h[s],A+=h[s];for(var S=0,O=0,s=d.length-1;s>=0;s--)S*=9,S+=d[s],O+=d[s];var b=m+3*S;if(i){if(A&1||A>12||A<4)throw new I;var T=(12-A)/2,D=e.OUTSIDE_ODD_WIDEST[T],P=9-D,G=mt.getRSSvalue(h,D,!1),H=mt.getRSSvalue(d,P,!0),se=e.OUTSIDE_EVEN_TOTAL_SUBSET[T],Be=e.OUTSIDE_GSUM[T];return new Jt(G*se+H+Be,b)}else{if(O&1||O>10||O<4)throw new I;var T=(10-O)/2,D=e.INSIDE_ODD_WIDEST[T],P=9-D,G=mt.getRSSvalue(h,D,!0),H=mt.getRSSvalue(d,P,!1),W=e.INSIDE_ODD_TOTAL_SUBSET[T],Be=e.INSIDE_GSUM[T];return new Jt(H*W+G+Be,b)}},e.prototype.findFinderPattern=function(t,n){var i=this.getDecodeFinderCounters();i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var a=t.getSize(),o=!1,s=0;s<a&&(o=!t.get(s),n!==o);)s++;for(var u=0,f=s,c=s;c<a;c++)if(t.get(c)!==o)i[u]++;else{if(u===3){if(pt.isFinderPattern(i))return[f,c];f+=i[0]+i[1],i[0]=i[2],i[1]=i[3],i[2]=0,i[3]=0,u--}else u++;i[u]=1,o=!o}throw new I},e.prototype.parseFoundFinderPattern=function(t,n,i,a){for(var o=t.get(a[0]),s=a[0]-1;s>=0&&o!==t.get(s);)s--;s++;var u=a[0]-s,f=this.getDecodeFinderCounters(),c=new Int32Array(f.length);ue.arraycopy(f,0,c,1,f.length-1),c[0]=u;var l=this.parseFinderValue(c,e.FINDER_PATTERNS),h=s,d=a[1];return i&&(h=t.getSize()-1-h,d=t.getSize()-1-d),new Pn(l,[s,a[1]],h,d,n)},e.prototype.adjustOddEvenCounts=function(t,n){var i=Y.sum(new Int32Array(this.getOddCounts())),a=Y.sum(new Int32Array(this.getEvenCounts())),o=!1,s=!1,u=!1,f=!1;t?(i>12?s=!0:i<4&&(o=!0),a>12?f=!0:a<4&&(u=!0)):(i>11?s=!0:i<5&&(o=!0),a>10?f=!0:a<4&&(u=!0));var c=i+a-n,l=(i&1)===(t?1:0),h=(a&1)===1;if(c===1)if(l){if(h)throw new I;s=!0}else{if(!h)throw new I;f=!0}else if(c===-1)if(l){if(h)throw new I;o=!0}else{if(!h)throw new I;u=!0}else if(c===0){if(l){if(!h)throw new I;i<a?(o=!0,f=!0):(s=!0,u=!0)}else if(h)throw new I}else throw new I;if(o){if(s)throw new I;pt.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&pt.decrement(this.getOddCounts(),this.getOddRoundingErrors()),u){if(f)throw new I;pt.increment(this.getEvenCounts(),this.getOddRoundingErrors())}f&&pt.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],e.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],e.OUTSIDE_GSUM=[0,161,961,2015,2715],e.INSIDE_GSUM=[0,336,1036,1516],e.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],e.INSIDE_ODD_WIDEST=[2,4,6,8],e.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])],e}(pt),vo=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),St=function(r){vo(e,r);function e(t){var n=r.call(this)||this;n.readers=[];var i=t?t.get(oe.POSSIBLE_FORMATS):null,a=t&&t.get(oe.ASSUME_CODE_39_CHECK_DIGIT)!==void 0,o=t&&t.get(oe.ENABLE_CODE_39_EXTENDED_MODE)!==void 0;return i&&((i.includes(L.EAN_13)||i.includes(L.UPC_A)||i.includes(L.EAN_8)||i.includes(L.UPC_E))&&n.readers.push(new cr(t)),i.includes(L.CODE_39)&&n.readers.push(new qr(a,o)),i.includes(L.CODE_93)&&n.readers.push(new Qr),i.includes(L.CODE_128)&&n.readers.push(new Kr),i.includes(L.ITF)&&n.readers.push(new Jr),i.includes(L.CODABAR)&&n.readers.push(new Ba),i.includes(L.RSS_14)&&n.readers.push(new un),i.includes(L.RSS_EXPANDED)&&(console.warn("RSS Expanded reader IS NOT ready for production yet! use at your own risk."),n.readers.push(new fo))),n.readers.length===0&&(n.readers.push(new cr(t)),n.readers.push(new qr),n.readers.push(new Qr),n.readers.push(new cr(t)),n.readers.push(new Kr),n.readers.push(new Jr),n.readers.push(new un)),n}return e.prototype.decodeRow=function(t,n,i){for(var a=0;a<this.readers.length;a++)try{return this.readers[a].decodeRow(t,n,i)}catch{}throw new I},e.prototype.reset=function(){this.readers.forEach(function(t){return t.reset()})},e}(Ie),po=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){po(e,r);function e(t,n){return t===void 0&&(t=500),r.call(this,new St(n),t,n)||this}return e})(Dt);var fn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Q=function(){function r(e,t,n){this.ecCodewords=e,this.ecBlocks=[t],n&&this.ecBlocks.push(n)}return r.prototype.getECCodewords=function(){return this.ecCodewords},r.prototype.getECBlocks=function(){return this.ecBlocks},r}(),K=function(){function r(e,t){this.count=e,this.dataCodewords=t}return r.prototype.getCount=function(){return this.count},r.prototype.getDataCodewords=function(){return this.dataCodewords},r}(),go=function(){function r(e,t,n,i,a,o){var s,u;this.versionNumber=e,this.symbolSizeRows=t,this.symbolSizeColumns=n,this.dataRegionSizeRows=i,this.dataRegionSizeColumns=a,this.ecBlocks=o;var f=0,c=o.getECCodewords(),l=o.getECBlocks();try{for(var h=fn(l),d=h.next();!d.done;d=h.next()){var v=d.value;f+=v.getCount()*(v.getDataCodewords()+c)}}catch(g){s={error:g}}finally{try{d&&!d.done&&(u=h.return)&&u.call(h)}finally{if(s)throw s.error}}this.totalCodewords=f}return r.prototype.getVersionNumber=function(){return this.versionNumber},r.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},r.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},r.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},r.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},r.prototype.getTotalCodewords=function(){return this.totalCodewords},r.prototype.getECBlocks=function(){return this.ecBlocks},r.getVersionForDimensions=function(e,t){var n,i;if(e&1||t&1)throw new N;try{for(var a=fn(r.VERSIONS),o=a.next();!o.done;o=a.next()){var s=o.value;if(s.symbolSizeRows===e&&s.symbolSizeColumns===t)return s}}catch(u){n={error:u}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}throw new N},r.prototype.toString=function(){return""+this.versionNumber},r.buildVersions=function(){return[new r(1,10,10,8,8,new Q(5,new K(1,3))),new r(2,12,12,10,10,new Q(7,new K(1,5))),new r(3,14,14,12,12,new Q(10,new K(1,8))),new r(4,16,16,14,14,new Q(12,new K(1,12))),new r(5,18,18,16,16,new Q(14,new K(1,18))),new r(6,20,20,18,18,new Q(18,new K(1,22))),new r(7,22,22,20,20,new Q(20,new K(1,30))),new r(8,24,24,22,22,new Q(24,new K(1,36))),new r(9,26,26,24,24,new Q(28,new K(1,44))),new r(10,32,32,14,14,new Q(36,new K(1,62))),new r(11,36,36,16,16,new Q(42,new K(1,86))),new r(12,40,40,18,18,new Q(48,new K(1,114))),new r(13,44,44,20,20,new Q(56,new K(1,144))),new r(14,48,48,22,22,new Q(68,new K(1,174))),new r(15,52,52,24,24,new Q(42,new K(2,102))),new r(16,64,64,14,14,new Q(56,new K(2,140))),new r(17,72,72,16,16,new Q(36,new K(4,92))),new r(18,80,80,18,18,new Q(48,new K(4,114))),new r(19,88,88,20,20,new Q(56,new K(4,144))),new r(20,96,96,22,22,new Q(68,new K(4,174))),new r(21,104,104,24,24,new Q(56,new K(6,136))),new r(22,120,120,18,18,new Q(68,new K(6,175))),new r(23,132,132,20,20,new Q(62,new K(8,163))),new r(24,144,144,22,22,new Q(62,new K(8,156),new K(2,155))),new r(25,8,18,6,16,new Q(7,new K(1,5))),new r(26,8,32,6,14,new Q(11,new K(1,10))),new r(27,12,26,10,24,new Q(14,new K(1,16))),new r(28,12,36,10,16,new Q(18,new K(1,22))),new r(29,16,36,14,16,new Q(24,new K(1,32))),new r(30,16,48,14,22,new Q(28,new K(1,49)))]},r.VERSIONS=r.buildVersions(),r}(),xo=function(){function r(e){var t=e.getHeight();if(t<8||t>144||t&1)throw new N;this.version=r.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new it(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return r.prototype.getVersion=function(){return this.version},r.readVersion=function(e){var t=e.getHeight(),n=e.getWidth();return go.getVersionForDimensions(t,n)},r.prototype.readCodewords=function(){var e=new Int8Array(this.version.getTotalCodewords()),t=0,n=4,i=0,a=this.mappingBitMatrix.getHeight(),o=this.mappingBitMatrix.getWidth(),s=!1,u=!1,f=!1,c=!1;do if(n===a&&i===0&&!s)e[t++]=this.readCorner1(a,o)&255,n-=2,i+=2,s=!0;else if(n===a-2&&i===0&&o&3&&!u)e[t++]=this.readCorner2(a,o)&255,n-=2,i+=2,u=!0;else if(n===a+4&&i===2&&!(o&7)&&!f)e[t++]=this.readCorner3(a,o)&255,n-=2,i+=2,f=!0;else if(n===a-2&&i===0&&(o&7)===4&&!c)e[t++]=this.readCorner4(a,o)&255,n-=2,i+=2,c=!0;else{do n<a&&i>=0&&!this.readMappingMatrix.get(i,n)&&(e[t++]=this.readUtah(n,i,a,o)&255),n-=2,i+=2;while(n>=0&&i<o);n+=1,i+=3;do n>=0&&i<o&&!this.readMappingMatrix.get(i,n)&&(e[t++]=this.readUtah(n,i,a,o)&255),n+=2,i-=2;while(n<a&&i>=0);n+=3,i+=1}while(n<a||i<o);if(t!==this.version.getTotalCodewords())throw new N;return e},r.prototype.readModule=function(e,t,n,i){return e<0&&(e+=n,t+=4-(n+4&7)),t<0&&(t+=i,e+=4-(i+4&7)),this.readMappingMatrix.set(t,e),this.mappingBitMatrix.get(t,e)},r.prototype.readUtah=function(e,t,n,i){var a=0;return this.readModule(e-2,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e-2,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t,n,i)&&(a|=1),a<<=1,this.readModule(e,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e,t,n,i)&&(a|=1),a},r.prototype.readCorner1=function(e,t){var n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,1,e,t)&&(n|=1),n<<=1,this.readModule(e-1,2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n},r.prototype.readCorner2=function(e,t){var n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-4,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n},r.prototype.readCorner3=function(e,t){var n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-3,e,t)&&(n|=1),n<<=1,this.readModule(1,t-2,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n},r.prototype.readCorner4=function(e,t){var n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n},r.prototype.extractDataRegion=function(e){var t=this.version.getSymbolSizeRows(),n=this.version.getSymbolSizeColumns();if(e.getHeight()!==t)throw new k("Dimension of bitMatrix must match the version size");for(var i=this.version.getDataRegionSizeRows(),a=this.version.getDataRegionSizeColumns(),o=t/i|0,s=n/a|0,u=o*i,f=s*a,c=new it(f,u),l=0;l<o;++l)for(var h=l*i,d=0;d<s;++d)for(var v=d*a,g=0;g<i;++g)for(var y=l*(i+2)+1+g,_=h+g,x=0;x<a;++x){var A=d*(a+2)+1+x;if(e.get(A,y)){var m=v+x;c.set(m,_)}}return c},r}(),cn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},yo=function(){function r(e,t){this.numDataCodewords=e,this.codewords=t}return r.getDataBlocks=function(e,t){var n,i,a,o,s=t.getECBlocks(),u=0,f=s.getECBlocks();try{for(var c=cn(f),l=c.next();!l.done;l=c.next()){var h=l.value;u+=h.getCount()}}catch(Be){n={error:Be}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}var d=new Array(u),v=0;try{for(var g=cn(f),y=g.next();!y.done;y=g.next())for(var h=y.value,_=0;_<h.getCount();_++){var x=h.getDataCodewords(),A=s.getECCodewords()+x;d[v++]=new r(x,new Uint8Array(A))}}catch(Be){a={error:Be}}finally{try{y&&!y.done&&(o=g.return)&&o.call(g)}finally{if(a)throw a.error}}for(var m=d[0].codewords.length,S=m-s.getECCodewords(),O=S-1,b=0,_=0;_<O;_++)for(var T=0;T<v;T++)d[T].codewords[_]=e[b++];for(var D=t.getVersionNumber()===24,P=D?8:v,T=0;T<P;T++)d[T].codewords[S-1]=e[b++];for(var G=d[0].codewords.length,_=S;_<G;_++)for(var T=0;T<v;T++){var H=D?(T+8)%v:T,se=D&&H>7?_-1:_;d[H].codewords[se]=e[b++]}if(b!==e.length)throw new k;return d},r.prototype.getNumDataCodewords=function(){return this.numDataCodewords},r.prototype.getCodewords=function(){return this.codewords},r}(),Fn=function(){function r(e){this.bytes=e,this.byteOffset=0,this.bitOffset=0}return r.prototype.getBitOffset=function(){return this.bitOffset},r.prototype.getByteOffset=function(){return this.byteOffset},r.prototype.readBits=function(e){if(e<1||e>32||e>this.available())throw new k(""+e);var t=0,n=this.bitOffset,i=this.byteOffset,a=this.bytes;if(n>0){var o=8-n,s=e<o?e:o,u=o-s,f=255>>8-s<<u;t=(a[i]&f)>>u,e-=s,n+=s,n===8&&(n=0,i++)}if(e>0){for(;e>=8;)t=t<<8|a[i]&255,i++,e-=8;if(e>0){var u=8-e,f=255>>u<<u;t=t<<e|(a[i]&f)>>u,n+=e}}return this.bitOffset=n,this.byteOffset=i,t},r.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},r}(),we;(function(r){r[r.PAD_ENCODE=0]="PAD_ENCODE",r[r.ASCII_ENCODE=1]="ASCII_ENCODE",r[r.C40_ENCODE=2]="C40_ENCODE",r[r.TEXT_ENCODE=3]="TEXT_ENCODE",r[r.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",r[r.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",r[r.BASE256_ENCODE=6]="BASE256_ENCODE"})(we||(we={}));var _o=function(){function r(){}return r.decode=function(e){var t=new Fn(e),n=new X,i=new X,a=new Array,o=we.ASCII_ENCODE;do if(o===we.ASCII_ENCODE)o=this.decodeAsciiSegment(t,n,i);else{switch(o){case we.C40_ENCODE:this.decodeC40Segment(t,n);break;case we.TEXT_ENCODE:this.decodeTextSegment(t,n);break;case we.ANSIX12_ENCODE:this.decodeAnsiX12Segment(t,n);break;case we.EDIFACT_ENCODE:this.decodeEdifactSegment(t,n);break;case we.BASE256_ENCODE:this.decodeBase256Segment(t,n,a);break;default:throw new N}o=we.ASCII_ENCODE}while(o!==we.PAD_ENCODE&&t.available()>0);return i.length()>0&&n.append(i.toString()),new ar(e,n.toString(),a.length===0?null:a,null)},r.decodeAsciiSegment=function(e,t,n){var i=!1;do{var a=e.readBits(8);if(a===0)throw new N;if(a<=128)return i&&(a+=128),t.append(String.fromCharCode(a-1)),we.ASCII_ENCODE;if(a===129)return we.PAD_ENCODE;if(a<=229){var o=a-130;o<10&&t.append("0"),t.append(""+o)}else switch(a){case 230:return we.C40_ENCODE;case 231:return we.BASE256_ENCODE;case 232:t.append("");break;case 233:case 234:break;case 235:i=!0;break;case 236:t.append("[)>05"),n.insert(0,"");break;case 237:t.append("[)>06"),n.insert(0,"");break;case 238:return we.ANSIX12_ENCODE;case 239:return we.TEXT_ENCODE;case 240:return we.EDIFACT_ENCODE;case 241:break;default:if(a!==254||e.available()!==0)throw new N;break}}while(e.available()>0);return we.ASCII_ENCODE},r.decodeC40Segment=function(e,t){var n=!1,i=[],a=0;do{if(e.available()===8)return;var o=e.readBits(8);if(o===254)return;this.parseTwoBytes(o,e.readBits(8),i);for(var s=0;s<3;s++){var u=i[s];switch(a){case 0:if(u<3)a=u+1;else if(u<this.C40_BASIC_SET_CHARS.length){var f=this.C40_BASIC_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f)}else throw new N;break;case 1:n?(t.append(String.fromCharCode(u+128)),n=!1):t.append(String.fromCharCode(u)),a=0;break;case 2:if(u<this.C40_SHIFT2_SET_CHARS.length){var f=this.C40_SHIFT2_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f)}else switch(u){case 27:t.append("");break;case 30:n=!0;break;default:throw new N}a=0;break;case 3:n?(t.append(String.fromCharCode(u+224)),n=!1):t.append(String.fromCharCode(u+96)),a=0;break;default:throw new N}}}while(e.available()>0)},r.decodeTextSegment=function(e,t){var n=!1,i=[],a=0;do{if(e.available()===8)return;var o=e.readBits(8);if(o===254)return;this.parseTwoBytes(o,e.readBits(8),i);for(var s=0;s<3;s++){var u=i[s];switch(a){case 0:if(u<3)a=u+1;else if(u<this.TEXT_BASIC_SET_CHARS.length){var f=this.TEXT_BASIC_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f)}else throw new N;break;case 1:n?(t.append(String.fromCharCode(u+128)),n=!1):t.append(String.fromCharCode(u)),a=0;break;case 2:if(u<this.TEXT_SHIFT2_SET_CHARS.length){var f=this.TEXT_SHIFT2_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f)}else switch(u){case 27:t.append("");break;case 30:n=!0;break;default:throw new N}a=0;break;case 3:if(u<this.TEXT_SHIFT3_SET_CHARS.length){var f=this.TEXT_SHIFT3_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f),a=0}else throw new N;break;default:throw new N}}}while(e.available()>0)},r.decodeAnsiX12Segment=function(e,t){var n=[];do{if(e.available()===8)return;var i=e.readBits(8);if(i===254)return;this.parseTwoBytes(i,e.readBits(8),n);for(var a=0;a<3;a++){var o=n[a];switch(o){case 0:t.append("\r");break;case 1:t.append("*");break;case 2:t.append(">");break;case 3:t.append(" ");break;default:if(o<14)t.append(String.fromCharCode(o+44));else if(o<40)t.append(String.fromCharCode(o+51));else throw new N;break}}}while(e.available()>0)},r.parseTwoBytes=function(e,t,n){var i=(e<<8)+t-1,a=Math.floor(i/1600);n[0]=a,i-=a*1600,a=Math.floor(i/40),n[1]=a,n[2]=i-a*40},r.decodeEdifactSegment=function(e,t){do{if(e.available()<=16)return;for(var n=0;n<4;n++){var i=e.readBits(6);if(i===31){var a=8-e.getBitOffset();a!==8&&e.readBits(a);return}i&32||(i|=64),t.append(String.fromCharCode(i))}}while(e.available()>0)},r.decodeBase256Segment=function(e,t,n){var i=1+e.getByteOffset(),a=this.unrandomize255State(e.readBits(8),i++),o;if(a===0?o=e.available()/8|0:a<250?o=a:o=250*(a-249)+this.unrandomize255State(e.readBits(8),i++),o<0)throw new N;for(var s=new Uint8Array(o),u=0;u<o;u++){if(e.available()<8)throw new N;s[u]=this.unrandomize255State(e.readBits(8),i++)}n.push(s);try{t.append(qe.decode(s,$.ISO88591))}catch(f){throw new Gt("Platform does not support required encoding: "+f.message)}},r.unrandomize255State=function(e,t){var n=149*t%255+1,i=e-n;return i>=0?i:i+256},r.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],r.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],r.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],r.TEXT_SHIFT2_SET_CHARS=r.C40_SHIFT2_SET_CHARS,r.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",""],r}(),wo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ao=function(){function r(){this.rsDecoder=new or(ze.DATA_MATRIX_FIELD_256)}return r.prototype.decode=function(e){var t,n,i=new xo(e),a=i.getVersion(),o=i.readCodewords(),s=yo.getDataBlocks(o,a),u=0;try{for(var f=wo(s),c=f.next();!c.done;c=f.next()){var l=c.value;u+=l.getNumDataCodewords()}}catch(A){t={error:A}}finally{try{c&&!c.done&&(n=f.return)&&n.call(f)}finally{if(t)throw t.error}}for(var h=new Uint8Array(u),d=s.length,v=0;v<d;v++){var g=s[v],y=g.getCodewords(),_=g.getNumDataCodewords();this.correctErrors(y,_);for(var x=0;x<_;x++)h[x*d+v]=y[x]}return _o.decode(h)},r.prototype.correctErrors=function(e,t){var n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new _e}for(var i=0;i<t;i++)e[i]=n[i]},r}(),Co=function(){function r(e){this.image=e,this.rectangleDetector=new Cr(this.image)}return r.prototype.detect=function(){var e=this.rectangleDetector.detect(),t=this.detectSolid1(e);if(t=this.detectSolid2(t),t[3]=this.correctTopRight(t),!t[3])throw new I;t=this.shiftToModuleCenter(t);var n=t[0],i=t[1],a=t[2],o=t[3],s=this.transitionsBetween(n,o)+1,u=this.transitionsBetween(a,o)+1;(s&1)===1&&(s+=1),(u&1)===1&&(u+=1),4*s<7*u&&4*u<7*s&&(s=u=Math.max(s,u));var f=r.sampleGrid(this.image,n,i,a,o,s,u);return new Rr(f,[n,i,a,o])},r.shiftPoint=function(e,t,n){var i=(t.getX()-e.getX())/(n+1),a=(t.getY()-e.getY())/(n+1);return new B(e.getX()+i,e.getY()+a)},r.moveAway=function(e,t,n){var i=e.getX(),a=e.getY();return i<t?i-=1:i+=1,a<n?a-=1:a+=1,new B(i,a)},r.prototype.detectSolid1=function(e){var t=e[0],n=e[1],i=e[3],a=e[2],o=this.transitionsBetween(t,n),s=this.transitionsBetween(n,i),u=this.transitionsBetween(i,a),f=this.transitionsBetween(a,t),c=o,l=[a,t,n,i];return c>s&&(c=s,l[0]=t,l[1]=n,l[2]=i,l[3]=a),c>u&&(c=u,l[0]=n,l[1]=i,l[2]=a,l[3]=t),c>f&&(l[0]=i,l[1]=a,l[2]=t,l[3]=n),l},r.prototype.detectSolid2=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a),s=r.shiftPoint(n,i,(o+1)*4),u=r.shiftPoint(i,n,(o+1)*4),f=this.transitionsBetween(s,t),c=this.transitionsBetween(u,a);return f<c?(e[0]=t,e[1]=n,e[2]=i,e[3]=a):(e[0]=n,e[1]=i,e[2]=a,e[3]=t),e},r.prototype.correctTopRight=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a),s=this.transitionsBetween(n,a),u=r.shiftPoint(t,n,(s+1)*4),f=r.shiftPoint(i,n,(o+1)*4);o=this.transitionsBetween(u,a),s=this.transitionsBetween(f,a);var c=new B(a.getX()+(i.getX()-n.getX())/(o+1),a.getY()+(i.getY()-n.getY())/(o+1)),l=new B(a.getX()+(t.getX()-n.getX())/(s+1),a.getY()+(t.getY()-n.getY())/(s+1));if(!this.isValid(c))return this.isValid(l)?l:null;if(!this.isValid(l))return c;var h=this.transitionsBetween(u,c)+this.transitionsBetween(f,c),d=this.transitionsBetween(u,l)+this.transitionsBetween(f,l);return h>d?c:l},r.prototype.shiftToModuleCenter=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a)+1,s=this.transitionsBetween(i,a)+1,u=r.shiftPoint(t,n,s*4),f=r.shiftPoint(i,n,o*4);o=this.transitionsBetween(u,a)+1,s=this.transitionsBetween(f,a)+1,(o&1)===1&&(o+=1),(s&1)===1&&(s+=1);var c=(t.getX()+n.getX()+i.getX()+a.getX())/4,l=(t.getY()+n.getY()+i.getY()+a.getY())/4;t=r.moveAway(t,c,l),n=r.moveAway(n,c,l),i=r.moveAway(i,c,l),a=r.moveAway(a,c,l);var h,d;return u=r.shiftPoint(t,n,s*4),u=r.shiftPoint(u,a,o*4),h=r.shiftPoint(n,t,s*4),h=r.shiftPoint(h,i,o*4),f=r.shiftPoint(i,a,s*4),f=r.shiftPoint(f,n,o*4),d=r.shiftPoint(a,i,s*4),d=r.shiftPoint(d,t,o*4),[u,h,f,d]},r.prototype.isValid=function(e){return e.getX()>=0&&e.getX()<this.image.getWidth()&&e.getY()>0&&e.getY()<this.image.getHeight()},r.sampleGrid=function(e,t,n,i,a,o,s){var u=Nr.getInstance();return u.sampleGrid(e,o,s,.5,.5,o-.5,.5,o-.5,s-.5,.5,s-.5,t.getX(),t.getY(),a.getX(),a.getY(),i.getX(),i.getY(),n.getX(),n.getY())},r.prototype.transitionsBetween=function(e,t){var n=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.trunc(t.getX()),o=Math.trunc(t.getY()),s=Math.abs(o-i)>Math.abs(a-n);if(s){var u=n;n=i,i=u,u=a,a=o,o=u}for(var f=Math.abs(a-n),c=Math.abs(o-i),l=-f/2,h=i<o?1:-1,d=n<a?1:-1,v=0,g=this.image.get(s?i:n,s?n:i),y=n,_=i;y!==a;y+=d){var x=this.image.get(s?_:y,s?y:_);if(x!==g&&(v++,g=x),l+=c,l>0){if(_===o)break;_+=h,l-=f}}return v},r}(),er=function(){function r(){this.decoder=new Ao}return r.prototype.decode=function(e,t){t===void 0&&(t=null);var n,i;if(t!=null&&t.has(oe.PURE_BARCODE)){var a=r.extractPureBits(e.getBlackMatrix());n=this.decoder.decode(a),i=r.NO_POINTS}else{var o=new Co(e.getBlackMatrix()).detect();n=this.decoder.decode(o.getBits()),i=o.getPoints()}var s=n.getRawBytes(),u=new ke(n.getText(),s,8*s.length,i,L.DATA_MATRIX,ue.currentTimeMillis()),f=n.getByteSegments();f!=null&&u.putMetadata(Me.BYTE_SEGMENTS,f);var c=n.getECLevel();return c!=null&&u.putMetadata(Me.ERROR_CORRECTION_LEVEL,c),u},r.prototype.reset=function(){},r.extractPureBits=function(e){var t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(t==null||n==null)throw new I;var i=this.moduleSize(t,e),a=t[1],o=n[1],s=t[0],u=n[0],f=(u-s+1)/i,c=(o-a+1)/i;if(f<=0||c<=0)throw new I;var l=i/2;a+=l,s+=l;for(var h=new it(f,c),d=0;d<c;d++)for(var v=a+d*i,g=0;g<f;g++)e.get(s+g*i,v)&&h.set(g,d);return h},r.moduleSize=function(e,t){for(var n=t.getWidth(),i=e[0],a=e[1];i<n&&t.get(i,a);)i++;if(i===n)throw new I;var o=i-e[0];if(o===0)throw new I;return o},r.NO_POINTS=[],r}(),Eo=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){Eo(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new er,t)||this}return e})(Dt);var It;(function(r){r[r.L=0]="L",r[r.M=1]="M",r[r.Q=2]="Q",r[r.H=3]="H"})(It||(It={}));var mo=function(){function r(e,t,n){this.value=e,this.stringValue=t,this.bits=n,r.FOR_BITS.set(n,this),r.FOR_VALUE.set(e,this)}return r.prototype.getValue=function(){return this.value},r.prototype.getBits=function(){return this.bits},r.fromString=function(e){switch(e){case"L":return r.L;case"M":return r.M;case"Q":return r.Q;case"H":return r.H;default:throw new ye(e+"not available")}},r.prototype.toString=function(){return this.stringValue},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value},r.forBits=function(e){if(e<0||e>=r.FOR_BITS.size)throw new k;return r.FOR_BITS.get(e)},r.FOR_BITS=new Map,r.FOR_VALUE=new Map,r.L=new r(It.L,"L",1),r.M=new r(It.M,"M",0),r.Q=new r(It.Q,"Q",3),r.H=new r(It.H,"H",2),r}(),So=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ln=function(){function r(e){this.errorCorrectionLevel=mo.forBits(e>>3&3),this.dataMask=e&7}return r.numBitsDiffering=function(e,t){return z.bitCount(e^t)},r.decodeFormatInformation=function(e,t){var n=r.doDecodeFormatInformation(e,t);return n!==null?n:r.doDecodeFormatInformation(e^r.FORMAT_INFO_MASK_QR,t^r.FORMAT_INFO_MASK_QR)},r.doDecodeFormatInformation=function(e,t){var n,i,a=Number.MAX_SAFE_INTEGER,o=0;try{for(var s=So(r.FORMAT_INFO_DECODE_LOOKUP),u=s.next();!u.done;u=s.next()){var f=u.value,c=f[0];if(c===e||c===t)return new r(f[1]);var l=r.numBitsDiffering(e,c);l<a&&(o=f[1],a=l),e!==t&&(l=r.numBitsDiffering(t,c),l<a&&(o=f[1],a=l))}}catch(h){n={error:h}}finally{try{u&&!u.done&&(i=s.return)&&i.call(s)}finally{if(n)throw n.error}}return a<=3?new r(o):null},r.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},r.prototype.getDataMask=function(){return this.dataMask},r.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.errorCorrectionLevel===t.errorCorrectionLevel&&this.dataMask===t.dataMask},r.FORMAT_INFO_MASK_QR=21522,r.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],r}(),Io=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E=function(){function r(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.ecCodewordsPerBlock=e,this.ecBlocks=t}return r.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},r.prototype.getNumBlocks=function(){var e,t,n=0,i=this.ecBlocks;try{for(var a=Io(i),o=a.next();!o.done;o=a.next()){var s=o.value;n+=s.getCount()}}catch(u){e={error:u}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n},r.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},r.prototype.getECBlocks=function(){return this.ecBlocks},r}(),p=function(){function r(e,t){this.count=e,this.dataCodewords=t}return r.prototype.getCount=function(){return this.count},r.prototype.getDataCodewords=function(){return this.dataCodewords},r}(),Oo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},xt=function(){function r(e,t){for(var n,i,a=[],o=2;o<arguments.length;o++)a[o-2]=arguments[o];this.versionNumber=e,this.alignmentPatternCenters=t,this.ecBlocks=a;var s=0,u=a[0].getECCodewordsPerBlock(),f=a[0].getECBlocks();try{for(var c=Oo(f),l=c.next();!l.done;l=c.next()){var h=l.value;s+=h.getCount()*(h.getDataCodewords()+u)}}catch(d){n={error:d}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}this.totalCodewords=s}return r.prototype.getVersionNumber=function(){return this.versionNumber},r.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},r.prototype.getTotalCodewords=function(){return this.totalCodewords},r.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},r.prototype.getECBlocksForLevel=function(e){return this.ecBlocks[e.getValue()]},r.getProvisionalVersionForDimension=function(e){if(e%4!==1)throw new N;try{return this.getVersionForNumber((e-17)/4)}catch{throw new N}},r.getVersionForNumber=function(e){if(e<1||e>40)throw new k;return r.VERSIONS[e-1]},r.decodeVersionInformation=function(e){for(var t=Number.MAX_SAFE_INTEGER,n=0,i=0;i<r.VERSION_DECODE_INFO.length;i++){var a=r.VERSION_DECODE_INFO[i];if(a===e)return r.getVersionForNumber(i+7);var o=Ln.numBitsDiffering(e,a);o<t&&(n=i+7,t=o)}return t<=3?r.getVersionForNumber(n):null},r.prototype.buildFunctionPattern=function(){var e=this.getDimensionForVersion(),t=new it(e);t.setRegion(0,0,9,9),t.setRegion(e-8,0,8,9),t.setRegion(0,e-8,9,8);for(var n=this.alignmentPatternCenters.length,i=0;i<n;i++)for(var a=this.alignmentPatternCenters[i]-2,o=0;o<n;o++)i===0&&(o===0||o===n-1)||i===n-1&&o===0||t.setRegion(this.alignmentPatternCenters[o]-2,a,5,5);return t.setRegion(6,9,1,e-17),t.setRegion(9,6,e-17,1),this.versionNumber>6&&(t.setRegion(e-11,0,3,6),t.setRegion(0,e-11,6,3)),t},r.prototype.toString=function(){return""+this.versionNumber},r.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),r.VERSIONS=[new r(1,new Int32Array(0),new E(7,new p(1,19)),new E(10,new p(1,16)),new E(13,new p(1,13)),new E(17,new p(1,9))),new r(2,Int32Array.from([6,18]),new E(10,new p(1,34)),new E(16,new p(1,28)),new E(22,new p(1,22)),new E(28,new p(1,16))),new r(3,Int32Array.from([6,22]),new E(15,new p(1,55)),new E(26,new p(1,44)),new E(18,new p(2,17)),new E(22,new p(2,13))),new r(4,Int32Array.from([6,26]),new E(20,new p(1,80)),new E(18,new p(2,32)),new E(26,new p(2,24)),new E(16,new p(4,9))),new r(5,Int32Array.from([6,30]),new E(26,new p(1,108)),new E(24,new p(2,43)),new E(18,new p(2,15),new p(2,16)),new E(22,new p(2,11),new p(2,12))),new r(6,Int32Array.from([6,34]),new E(18,new p(2,68)),new E(16,new p(4,27)),new E(24,new p(4,19)),new E(28,new p(4,15))),new r(7,Int32Array.from([6,22,38]),new E(20,new p(2,78)),new E(18,new p(4,31)),new E(18,new p(2,14),new p(4,15)),new E(26,new p(4,13),new p(1,14))),new r(8,Int32Array.from([6,24,42]),new E(24,new p(2,97)),new E(22,new p(2,38),new p(2,39)),new E(22,new p(4,18),new p(2,19)),new E(26,new p(4,14),new p(2,15))),new r(9,Int32Array.from([6,26,46]),new E(30,new p(2,116)),new E(22,new p(3,36),new p(2,37)),new E(20,new p(4,16),new p(4,17)),new E(24,new p(4,12),new p(4,13))),new r(10,Int32Array.from([6,28,50]),new E(18,new p(2,68),new p(2,69)),new E(26,new p(4,43),new p(1,44)),new E(24,new p(6,19),new p(2,20)),new E(28,new p(6,15),new p(2,16))),new r(11,Int32Array.from([6,30,54]),new E(20,new p(4,81)),new E(30,new p(1,50),new p(4,51)),new E(28,new p(4,22),new p(4,23)),new E(24,new p(3,12),new p(8,13))),new r(12,Int32Array.from([6,32,58]),new E(24,new p(2,92),new p(2,93)),new E(22,new p(6,36),new p(2,37)),new E(26,new p(4,20),new p(6,21)),new E(28,new p(7,14),new p(4,15))),new r(13,Int32Array.from([6,34,62]),new E(26,new p(4,107)),new E(22,new p(8,37),new p(1,38)),new E(24,new p(8,20),new p(4,21)),new E(22,new p(12,11),new p(4,12))),new r(14,Int32Array.from([6,26,46,66]),new E(30,new p(3,115),new p(1,116)),new E(24,new p(4,40),new p(5,41)),new E(20,new p(11,16),new p(5,17)),new E(24,new p(11,12),new p(5,13))),new r(15,Int32Array.from([6,26,48,70]),new E(22,new p(5,87),new p(1,88)),new E(24,new p(5,41),new p(5,42)),new E(30,new p(5,24),new p(7,25)),new E(24,new p(11,12),new p(7,13))),new r(16,Int32Array.from([6,26,50,74]),new E(24,new p(5,98),new p(1,99)),new E(28,new p(7,45),new p(3,46)),new E(24,new p(15,19),new p(2,20)),new E(30,new p(3,15),new p(13,16))),new r(17,Int32Array.from([6,30,54,78]),new E(28,new p(1,107),new p(5,108)),new E(28,new p(10,46),new p(1,47)),new E(28,new p(1,22),new p(15,23)),new E(28,new p(2,14),new p(17,15))),new r(18,Int32Array.from([6,30,56,82]),new E(30,new p(5,120),new p(1,121)),new E(26,new p(9,43),new p(4,44)),new E(28,new p(17,22),new p(1,23)),new E(28,new p(2,14),new p(19,15))),new r(19,Int32Array.from([6,30,58,86]),new E(28,new p(3,113),new p(4,114)),new E(26,new p(3,44),new p(11,45)),new E(26,new p(17,21),new p(4,22)),new E(26,new p(9,13),new p(16,14))),new r(20,Int32Array.from([6,34,62,90]),new E(28,new p(3,107),new p(5,108)),new E(26,new p(3,41),new p(13,42)),new E(30,new p(15,24),new p(5,25)),new E(28,new p(15,15),new p(10,16))),new r(21,Int32Array.from([6,28,50,72,94]),new E(28,new p(4,116),new p(4,117)),new E(26,new p(17,42)),new E(28,new p(17,22),new p(6,23)),new E(30,new p(19,16),new p(6,17))),new r(22,Int32Array.from([6,26,50,74,98]),new E(28,new p(2,111),new p(7,112)),new E(28,new p(17,46)),new E(30,new p(7,24),new p(16,25)),new E(24,new p(34,13))),new r(23,Int32Array.from([6,30,54,78,102]),new E(30,new p(4,121),new p(5,122)),new E(28,new p(4,47),new p(14,48)),new E(30,new p(11,24),new p(14,25)),new E(30,new p(16,15),new p(14,16))),new r(24,Int32Array.from([6,28,54,80,106]),new E(30,new p(6,117),new p(4,118)),new E(28,new p(6,45),new p(14,46)),new E(30,new p(11,24),new p(16,25)),new E(30,new p(30,16),new p(2,17))),new r(25,Int32Array.from([6,32,58,84,110]),new E(26,new p(8,106),new p(4,107)),new E(28,new p(8,47),new p(13,48)),new E(30,new p(7,24),new p(22,25)),new E(30,new p(22,15),new p(13,16))),new r(26,Int32Array.from([6,30,58,86,114]),new E(28,new p(10,114),new p(2,115)),new E(28,new p(19,46),new p(4,47)),new E(28,new p(28,22),new p(6,23)),new E(30,new p(33,16),new p(4,17))),new r(27,Int32Array.from([6,34,62,90,118]),new E(30,new p(8,122),new p(4,123)),new E(28,new p(22,45),new p(3,46)),new E(30,new p(8,23),new p(26,24)),new E(30,new p(12,15),new p(28,16))),new r(28,Int32Array.from([6,26,50,74,98,122]),new E(30,new p(3,117),new p(10,118)),new E(28,new p(3,45),new p(23,46)),new E(30,new p(4,24),new p(31,25)),new E(30,new p(11,15),new p(31,16))),new r(29,Int32Array.from([6,30,54,78,102,126]),new E(30,new p(7,116),new p(7,117)),new E(28,new p(21,45),new p(7,46)),new E(30,new p(1,23),new p(37,24)),new E(30,new p(19,15),new p(26,16))),new r(30,Int32Array.from([6,26,52,78,104,130]),new E(30,new p(5,115),new p(10,116)),new E(28,new p(19,47),new p(10,48)),new E(30,new p(15,24),new p(25,25)),new E(30,new p(23,15),new p(25,16))),new r(31,Int32Array.from([6,30,56,82,108,134]),new E(30,new p(13,115),new p(3,116)),new E(28,new p(2,46),new p(29,47)),new E(30,new p(42,24),new p(1,25)),new E(30,new p(23,15),new p(28,16))),new r(32,Int32Array.from([6,34,60,86,112,138]),new E(30,new p(17,115)),new E(28,new p(10,46),new p(23,47)),new E(30,new p(10,24),new p(35,25)),new E(30,new p(19,15),new p(35,16))),new r(33,Int32Array.from([6,30,58,86,114,142]),new E(30,new p(17,115),new p(1,116)),new E(28,new p(14,46),new p(21,47)),new E(30,new p(29,24),new p(19,25)),new E(30,new p(11,15),new p(46,16))),new r(34,Int32Array.from([6,34,62,90,118,146]),new E(30,new p(13,115),new p(6,116)),new E(28,new p(14,46),new p(23,47)),new E(30,new p(44,24),new p(7,25)),new E(30,new p(59,16),new p(1,17))),new r(35,Int32Array.from([6,30,54,78,102,126,150]),new E(30,new p(12,121),new p(7,122)),new E(28,new p(12,47),new p(26,48)),new E(30,new p(39,24),new p(14,25)),new E(30,new p(22,15),new p(41,16))),new r(36,Int32Array.from([6,24,50,76,102,128,154]),new E(30,new p(6,121),new p(14,122)),new E(28,new p(6,47),new p(34,48)),new E(30,new p(46,24),new p(10,25)),new E(30,new p(2,15),new p(64,16))),new r(37,Int32Array.from([6,28,54,80,106,132,158]),new E(30,new p(17,122),new p(4,123)),new E(28,new p(29,46),new p(14,47)),new E(30,new p(49,24),new p(10,25)),new E(30,new p(24,15),new p(46,16))),new r(38,Int32Array.from([6,32,58,84,110,136,162]),new E(30,new p(4,122),new p(18,123)),new E(28,new p(13,46),new p(32,47)),new E(30,new p(48,24),new p(14,25)),new E(30,new p(42,15),new p(32,16))),new r(39,Int32Array.from([6,26,54,82,110,138,166]),new E(30,new p(20,117),new p(4,118)),new E(28,new p(40,47),new p(7,48)),new E(30,new p(43,24),new p(22,25)),new E(30,new p(10,15),new p(67,16))),new r(40,Int32Array.from([6,30,58,86,114,142,170]),new E(30,new p(19,118),new p(6,119)),new E(28,new p(18,47),new p(31,48)),new E(30,new p(34,24),new p(34,25)),new E(30,new p(20,15),new p(61,16)))],r}(),Oe;(function(r){r[r.DATA_MASK_000=0]="DATA_MASK_000",r[r.DATA_MASK_001=1]="DATA_MASK_001",r[r.DATA_MASK_010=2]="DATA_MASK_010",r[r.DATA_MASK_011=3]="DATA_MASK_011",r[r.DATA_MASK_100=4]="DATA_MASK_100",r[r.DATA_MASK_101=5]="DATA_MASK_101",r[r.DATA_MASK_110=6]="DATA_MASK_110",r[r.DATA_MASK_111=7]="DATA_MASK_111"})(Oe||(Oe={}));var ln=function(){function r(e,t){this.value=e,this.isMasked=t}return r.prototype.unmaskBitMatrix=function(e,t){for(var n=0;n<t;n++)for(var i=0;i<t;i++)this.isMasked(n,i)&&e.flip(i,n)},r.values=new Map([[Oe.DATA_MASK_000,new r(Oe.DATA_MASK_000,function(e,t){return(e+t&1)===0})],[Oe.DATA_MASK_001,new r(Oe.DATA_MASK_001,function(e,t){return(e&1)===0})],[Oe.DATA_MASK_010,new r(Oe.DATA_MASK_010,function(e,t){return t%3===0})],[Oe.DATA_MASK_011,new r(Oe.DATA_MASK_011,function(e,t){return(e+t)%3===0})],[Oe.DATA_MASK_100,new r(Oe.DATA_MASK_100,function(e,t){return(Math.floor(e/2)+Math.floor(t/3)&1)===0})],[Oe.DATA_MASK_101,new r(Oe.DATA_MASK_101,function(e,t){return e*t%6===0})],[Oe.DATA_MASK_110,new r(Oe.DATA_MASK_110,function(e,t){return e*t%6<3})],[Oe.DATA_MASK_111,new r(Oe.DATA_MASK_111,function(e,t){return(e+t+e*t%3&1)===0})]]),r}(),To=function(){function r(e){var t=e.getHeight();if(t<21||(t&3)!==1)throw new N;this.bitMatrix=e}return r.prototype.readFormatInformation=function(){if(this.parsedFormatInfo!==null&&this.parsedFormatInfo!==void 0)return this.parsedFormatInfo;for(var e=0,t=0;t<6;t++)e=this.copyBit(t,8,e);e=this.copyBit(7,8,e),e=this.copyBit(8,8,e),e=this.copyBit(8,7,e);for(var n=5;n>=0;n--)e=this.copyBit(8,n,e);for(var i=this.bitMatrix.getHeight(),a=0,o=i-7,n=i-1;n>=o;n--)a=this.copyBit(8,n,a);for(var t=i-8;t<i;t++)a=this.copyBit(t,8,a);if(this.parsedFormatInfo=Ln.decodeFormatInformation(e,a),this.parsedFormatInfo!==null)return this.parsedFormatInfo;throw new N},r.prototype.readVersion=function(){if(this.parsedVersion!==null&&this.parsedVersion!==void 0)return this.parsedVersion;var e=this.bitMatrix.getHeight(),t=Math.floor((e-17)/4);if(t<=6)return xt.getVersionForNumber(t);for(var n=0,i=e-11,a=5;a>=0;a--)for(var o=e-9;o>=i;o--)n=this.copyBit(o,a,n);var s=xt.decodeVersionInformation(n);if(s!==null&&s.getDimensionForVersion()===e)return this.parsedVersion=s,s;n=0;for(var o=5;o>=0;o--)for(var a=e-9;a>=i;a--)n=this.copyBit(o,a,n);if(s=xt.decodeVersionInformation(n),s!==null&&s.getDimensionForVersion()===e)return this.parsedVersion=s,s;throw new N},r.prototype.copyBit=function(e,t,n){var i=this.isMirror?this.bitMatrix.get(t,e):this.bitMatrix.get(e,t);return i?n<<1|1:n<<1},r.prototype.readCodewords=function(){var e=this.readFormatInformation(),t=this.readVersion(),n=ln.values.get(e.getDataMask()),i=this.bitMatrix.getHeight();n.unmaskBitMatrix(this.bitMatrix,i);for(var a=t.buildFunctionPattern(),o=!0,s=new Uint8Array(t.getTotalCodewords()),u=0,f=0,c=0,l=i-1;l>0;l-=2){l===6&&l--;for(var h=0;h<i;h++)for(var d=o?i-1-h:h,v=0;v<2;v++)a.get(l-v,d)||(c++,f<<=1,this.bitMatrix.get(l-v,d)&&(f|=1),c===8&&(s[u++]=f,c=0,f=0));o=!o}if(u!==t.getTotalCodewords())throw new N;return s},r.prototype.remask=function(){if(this.parsedFormatInfo!==null){var e=ln.values.get(this.parsedFormatInfo.getDataMask()),t=this.bitMatrix.getHeight();e.unmaskBitMatrix(this.bitMatrix,t)}},r.prototype.setMirror=function(e){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=e},r.prototype.mirror=function(){for(var e=this.bitMatrix,t=0,n=e.getWidth();t<n;t++)for(var i=t+1,a=e.getHeight();i<a;i++)e.get(t,i)!==e.get(i,t)&&(e.flip(i,t),e.flip(t,i))},r}(),dn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},bo=function(){function r(e,t){this.numDataCodewords=e,this.codewords=t}return r.getDataBlocks=function(e,t,n){var i,a,o,s;if(e.length!==t.getTotalCodewords())throw new k;var u=t.getECBlocksForLevel(n),f=0,c=u.getECBlocks();try{for(var l=dn(c),h=l.next();!h.done;h=l.next()){var d=h.value;f+=d.getCount()}}catch(se){i={error:se}}finally{try{h&&!h.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}var v=new Array(f),g=0;try{for(var y=dn(c),_=y.next();!_.done;_=y.next())for(var d=_.value,x=0;x<d.getCount();x++){var A=d.getDataCodewords(),m=u.getECCodewordsPerBlock()+A;v[g++]=new r(A,new Uint8Array(m))}}catch(se){o={error:se}}finally{try{_&&!_.done&&(s=y.return)&&s.call(y)}finally{if(o)throw o.error}}for(var S=v[0].codewords.length,O=v.length-1;O>=0;){var b=v[O].codewords.length;if(b===S)break;O--}O++;for(var T=S-u.getECCodewordsPerBlock(),D=0,x=0;x<T;x++)for(var P=0;P<g;P++)v[P].codewords[x]=e[D++];for(var P=O;P<g;P++)v[P].codewords[T]=e[D++];for(var G=v[0].codewords.length,x=T;x<G;x++)for(var P=0;P<g;P++){var H=P<O?x:x+1;v[P].codewords[H]=e[D++]}return v},r.prototype.getNumDataCodewords=function(){return this.numDataCodewords},r.prototype.getCodewords=function(){return this.codewords},r}(),Ze;(function(r){r[r.TERMINATOR=0]="TERMINATOR",r[r.NUMERIC=1]="NUMERIC",r[r.ALPHANUMERIC=2]="ALPHANUMERIC",r[r.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",r[r.BYTE=4]="BYTE",r[r.ECI=5]="ECI",r[r.KANJI=6]="KANJI",r[r.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",r[r.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",r[r.HANZI=9]="HANZI"})(Ze||(Ze={}));var le=function(){function r(e,t,n,i){this.value=e,this.stringValue=t,this.characterCountBitsForVersions=n,this.bits=i,r.FOR_BITS.set(i,this),r.FOR_VALUE.set(e,this)}return r.forBits=function(e){var t=r.FOR_BITS.get(e);if(t===void 0)throw new k;return t},r.prototype.getCharacterCountBits=function(e){var t=e.getVersionNumber(),n;return t<=9?n=0:t<=26?n=1:n=2,this.characterCountBitsForVersions[n]},r.prototype.getValue=function(){return this.value},r.prototype.getBits=function(){return this.bits},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value},r.prototype.toString=function(){return this.stringValue},r.FOR_BITS=new Map,r.FOR_VALUE=new Map,r.TERMINATOR=new r(Ze.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),r.NUMERIC=new r(Ze.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),r.ALPHANUMERIC=new r(Ze.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),r.STRUCTURED_APPEND=new r(Ze.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),r.BYTE=new r(Ze.BYTE,"BYTE",Int32Array.from([8,16,16]),4),r.ECI=new r(Ze.ECI,"ECI",Int32Array.from([0,0,0]),7),r.KANJI=new r(Ze.KANJI,"KANJI",Int32Array.from([8,10,12]),8),r.FNC1_FIRST_POSITION=new r(Ze.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),r.FNC1_SECOND_POSITION=new r(Ze.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),r.HANZI=new r(Ze.HANZI,"HANZI",Int32Array.from([8,10,12]),13),r}(),Do=function(){function r(){}return r.decode=function(e,t,n,i){var a=new Fn(e),o=new X,s=new Array,u=-1,f=-1;try{var c=null,l=!1,h=void 0;do{if(a.available()<4)h=le.TERMINATOR;else{var d=a.readBits(4);h=le.forBits(d)}switch(h){case le.TERMINATOR:break;case le.FNC1_FIRST_POSITION:case le.FNC1_SECOND_POSITION:l=!0;break;case le.STRUCTURED_APPEND:if(a.available()<16)throw new N;u=a.readBits(8),f=a.readBits(8);break;case le.ECI:var v=r.parseECIValue(a);if(c=me.getCharacterSetECIByValue(v),c===null)throw new N;break;case le.HANZI:var g=a.readBits(4),y=a.readBits(h.getCharacterCountBits(t));g===r.GB2312_SUBSET&&r.decodeHanziSegment(a,o,y);break;default:var _=a.readBits(h.getCharacterCountBits(t));switch(h){case le.NUMERIC:r.decodeNumericSegment(a,o,_);break;case le.ALPHANUMERIC:r.decodeAlphanumericSegment(a,o,_,l);break;case le.BYTE:r.decodeByteSegment(a,o,_,c,s,i);break;case le.KANJI:r.decodeKanjiSegment(a,o,_);break;default:throw new N}break}}while(h!==le.TERMINATOR)}catch{throw new N}return new ar(e,o.toString(),s.length===0?null:s,n===null?null:n.toString(),u,f)},r.decodeHanziSegment=function(e,t,n){if(n*13>e.available())throw new N;for(var i=new Uint8Array(2*n),a=0;n>0;){var o=e.readBits(13),s=o/96<<8&4294967295|o%96;s<959?s+=41377:s+=42657,i[a]=s>>8&255,i[a+1]=s&255,a+=2,n--}try{t.append(qe.decode(i,$.GB2312))}catch(u){throw new N(u)}},r.decodeKanjiSegment=function(e,t,n){if(n*13>e.available())throw new N;for(var i=new Uint8Array(2*n),a=0;n>0;){var o=e.readBits(13),s=o/192<<8&4294967295|o%192;s<7936?s+=33088:s+=49472,i[a]=s>>8,i[a+1]=s,a+=2,n--}try{t.append(qe.decode(i,$.SHIFT_JIS))}catch(u){throw new N(u)}},r.decodeByteSegment=function(e,t,n,i,a,o){if(8*n>e.available())throw new N;for(var s=new Uint8Array(n),u=0;u<n;u++)s[u]=e.readBits(8);var f;i===null?f=$.guessEncoding(s,o):f=i.getName();try{t.append(qe.decode(s,f))}catch(c){throw new N(c)}a.push(s)},r.toAlphaNumericChar=function(e){if(e>=r.ALPHANUMERIC_CHARS.length)throw new N;return r.ALPHANUMERIC_CHARS[e]},r.decodeAlphanumericSegment=function(e,t,n,i){for(var a=t.length();n>1;){if(e.available()<11)throw new N;var o=e.readBits(11);t.append(r.toAlphaNumericChar(Math.floor(o/45))),t.append(r.toAlphaNumericChar(o%45)),n-=2}if(n===1){if(e.available()<6)throw new N;t.append(r.toAlphaNumericChar(e.readBits(6)))}if(i)for(var s=a;s<t.length();s++)t.charAt(s)==="%"&&(s<t.length()-1&&t.charAt(s+1)==="%"?t.deleteCharAt(s+1):t.setCharAt(s,""))},r.decodeNumericSegment=function(e,t,n){for(;n>=3;){if(e.available()<10)throw new N;var i=e.readBits(10);if(i>=1e3)throw new N;t.append(r.toAlphaNumericChar(Math.floor(i/100))),t.append(r.toAlphaNumericChar(Math.floor(i/10)%10)),t.append(r.toAlphaNumericChar(i%10)),n-=3}if(n===2){if(e.available()<7)throw new N;var a=e.readBits(7);if(a>=100)throw new N;t.append(r.toAlphaNumericChar(Math.floor(a/10))),t.append(r.toAlphaNumericChar(a%10))}else if(n===1){if(e.available()<4)throw new N;var o=e.readBits(4);if(o>=10)throw new N;t.append(r.toAlphaNumericChar(o))}},r.parseECIValue=function(e){var t=e.readBits(8);if(!(t&128))return t&127;if((t&192)===128){var n=e.readBits(8);return(t&63)<<8&4294967295|n}if((t&224)===192){var i=e.readBits(16);return(t&31)<<16&4294967295|i}throw new N},r.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",r.GB2312_SUBSET=1,r}(),kn=function(){function r(e){this.mirrored=e}return r.prototype.isMirrored=function(){return this.mirrored},r.prototype.applyMirroredCorrection=function(e){if(!(!this.mirrored||e===null||e.length<3)){var t=e[0];e[0]=e[2],e[2]=t}},r}(),hn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ro=function(){function r(){this.rsDecoder=new or(ze.QR_CODE_FIELD_256)}return r.prototype.decodeBooleanArray=function(e,t){return this.decodeBitMatrix(it.parseFromBooleanArray(e),t)},r.prototype.decodeBitMatrix=function(e,t){var n=new To(e),i=null;try{return this.decodeBitMatrixParser(n,t)}catch(o){i=o}try{n.remask(),n.setMirror(!0),n.readVersion(),n.readFormatInformation(),n.mirror();var a=this.decodeBitMatrixParser(n,t);return a.setOther(new kn(!0)),a}catch(o){throw i!==null?i:o}},r.prototype.decodeBitMatrixParser=function(e,t){var n,i,a,o,s=e.readVersion(),u=e.readFormatInformation().getErrorCorrectionLevel(),f=e.readCodewords(),c=bo.getDataBlocks(f,s,u),l=0;try{for(var h=hn(c),d=h.next();!d.done;d=h.next()){var v=d.value;l+=v.getNumDataCodewords()}}catch(O){n={error:O}}finally{try{d&&!d.done&&(i=h.return)&&i.call(h)}finally{if(n)throw n.error}}var g=new Uint8Array(l),y=0;try{for(var _=hn(c),x=_.next();!x.done;x=_.next()){var v=x.value,A=v.getCodewords(),m=v.getNumDataCodewords();this.correctErrors(A,m);for(var S=0;S<m;S++)g[y++]=A[S]}}catch(O){a={error:O}}finally{try{x&&!x.done&&(o=_.return)&&o.call(_)}finally{if(a)throw a.error}}return Do.decode(g,s,u,t)},r.prototype.correctErrors=function(e,t){var n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new _e}for(var i=0;i<t;i++)e[i]=n[i]},r}(),No=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Po=function(r){No(e,r);function e(t,n,i){var a=r.call(this,t,n)||this;return a.estimatedModuleSize=i,a}return e.prototype.aboutEquals=function(t,n,i){if(Math.abs(n-this.getY())<=t&&Math.abs(i-this.getX())<=t){var a=Math.abs(t-this.estimatedModuleSize);return a<=1||a<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,n,i){var a=(this.getX()+n)/2,o=(this.getY()+t)/2,s=(this.estimatedModuleSize+i)/2;return new e(a,o,s)},e}(B),Mo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Bo=function(){function r(e,t,n,i,a,o,s){this.image=e,this.startX=t,this.startY=n,this.width=i,this.height=a,this.moduleSize=o,this.resultPointCallback=s,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return r.prototype.find=function(){for(var e=this.startX,t=this.height,n=this.width,i=e+n,a=this.startY+t/2,o=new Int32Array(3),s=this.image,u=0;u<t;u++){var f=a+(u&1?-Math.floor((u+1)/2):Math.floor((u+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var c=e;c<i&&!s.get(c,f);)c++;for(var l=0;c<i;){if(s.get(c,f))if(l===1)o[1]++;else if(l===2){if(this.foundPatternCross(o)){var h=this.handlePossibleCenter(o,f,c);if(h!==null)return h}o[0]=o[2],o[1]=1,o[2]=0,l=1}else o[++l]++;else l===1&&l++,o[l]++;c++}if(this.foundPatternCross(o)){var h=this.handlePossibleCenter(o,f,i);if(h!==null)return h}}if(this.possibleCenters.length!==0)return this.possibleCenters[0];throw new I},r.centerFromEnd=function(e,t){return t-e[2]-e[1]/2},r.prototype.foundPatternCross=function(e){for(var t=this.moduleSize,n=t/2,i=0;i<3;i++)if(Math.abs(t-e[i])>=n)return!1;return!0},r.prototype.crossCheckVertical=function(e,t,n,i){var a=this.image,o=a.getHeight(),s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var u=e;u>=0&&a.get(t,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&!a.get(t,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<o&&a.get(t,u)&&s[1]<=n;)s[1]++,u++;if(u===o||s[1]>n)return NaN;for(;u<o&&!a.get(t,u)&&s[2]<=n;)s[2]++,u++;if(s[2]>n)return NaN;var f=s[0]+s[1]+s[2];return 5*Math.abs(f-i)>=2*i?NaN:this.foundPatternCross(s)?r.centerFromEnd(s,u):NaN},r.prototype.handlePossibleCenter=function(e,t,n){var i,a,o=e[0]+e[1]+e[2],s=r.centerFromEnd(e,n),u=this.crossCheckVertical(t,s,2*e[1],o);if(!isNaN(u)){var f=(e[0]+e[1]+e[2])/3;try{for(var c=Mo(this.possibleCenters),l=c.next();!l.done;l=c.next()){var h=l.value;if(h.aboutEquals(f,u,s))return h.combineEstimate(u,s,f)}}catch(v){i={error:v}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}var d=new Po(s,u,f);this.possibleCenters.push(d),this.resultPointCallback!==null&&this.resultPointCallback!==void 0&&this.resultPointCallback.foundPossibleResultPoint(d)}return null},r}(),Fo=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Lo=function(r){Fo(e,r);function e(t,n,i,a){var o=r.call(this,t,n)||this;return o.estimatedModuleSize=i,o.count=a,a===void 0&&(o.count=1),o}return e.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},e.prototype.getCount=function(){return this.count},e.prototype.aboutEquals=function(t,n,i){if(Math.abs(n-this.getY())<=t&&Math.abs(i-this.getX())<=t){var a=Math.abs(t-this.estimatedModuleSize);return a<=1||a<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,n,i){var a=this.count+1,o=(this.count*this.getX()+n)/a,s=(this.count*this.getY()+t)/a,u=(this.count*this.estimatedModuleSize+i)/a;return new e(o,s,u,a)},e}(B),ko=function(){function r(e){this.bottomLeft=e[0],this.topLeft=e[1],this.topRight=e[2]}return r.prototype.getBottomLeft=function(){return this.bottomLeft},r.prototype.getTopLeft=function(){return this.topLeft},r.prototype.getTopRight=function(){return this.topRight},r}(),Bt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Uo=function(){function r(e,t){this.image=e,this.resultPointCallback=t,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=t}return r.prototype.getImage=function(){return this.image},r.prototype.getPossibleCenters=function(){return this.possibleCenters},r.prototype.find=function(e){var t=e!=null&&e.get(oe.TRY_HARDER)!==void 0,n=e!=null&&e.get(oe.PURE_BARCODE)!==void 0,i=this.image,a=i.getHeight(),o=i.getWidth(),s=Math.floor(3*a/(4*r.MAX_MODULES));(s<r.MIN_SKIP||t)&&(s=r.MIN_SKIP);for(var u=!1,f=new Int32Array(5),c=s-1;c<a&&!u;c+=s){f[0]=0,f[1]=0,f[2]=0,f[3]=0,f[4]=0;for(var l=0,h=0;h<o;h++)if(i.get(h,c))(l&1)===1&&l++,f[l]++;else if(l&1)f[l]++;else if(l===4)if(r.foundPatternCross(f)){var d=this.handlePossibleCenter(f,c,h,n);if(d===!0)if(s=2,this.hasSkipped===!0)u=this.haveMultiplyConfirmedCenters();else{var v=this.findRowSkip();v>f[2]&&(c+=v-f[2]-s,h=o-1)}else{f[0]=f[2],f[1]=f[3],f[2]=f[4],f[3]=1,f[4]=0,l=3;continue}l=0,f[0]=0,f[1]=0,f[2]=0,f[3]=0,f[4]=0}else f[0]=f[2],f[1]=f[3],f[2]=f[4],f[3]=1,f[4]=0,l=3;else f[++l]++;if(r.foundPatternCross(f)){var d=this.handlePossibleCenter(f,c,o,n);d===!0&&(s=f[0],this.hasSkipped&&(u=this.haveMultiplyConfirmedCenters()))}}var g=this.selectBestPatterns();return B.orderBestPatterns(g),new ko(g)},r.centerFromEnd=function(e,t){return t-e[4]-e[3]-e[2]/2},r.foundPatternCross=function(e){for(var t=0,n=0;n<5;n++){var i=e[n];if(i===0)return!1;t+=i}if(t<7)return!1;var a=t/7,o=a/2;return Math.abs(a-e[0])<o&&Math.abs(a-e[1])<o&&Math.abs(3*a-e[2])<3*o&&Math.abs(a-e[3])<o&&Math.abs(a-e[4])<o},r.prototype.getCrossCheckStateCount=function(){var e=this.crossCheckStateCount;return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e},r.prototype.crossCheckDiagonal=function(e,t,n,i){for(var a=this.getCrossCheckStateCount(),o=0,s=this.image;e>=o&&t>=o&&s.get(t-o,e-o);)a[2]++,o++;if(e<o||t<o)return!1;for(;e>=o&&t>=o&&!s.get(t-o,e-o)&&a[1]<=n;)a[1]++,o++;if(e<o||t<o||a[1]>n)return!1;for(;e>=o&&t>=o&&s.get(t-o,e-o)&&a[0]<=n;)a[0]++,o++;if(a[0]>n)return!1;var u=s.getHeight(),f=s.getWidth();for(o=1;e+o<u&&t+o<f&&s.get(t+o,e+o);)a[2]++,o++;if(e+o>=u||t+o>=f)return!1;for(;e+o<u&&t+o<f&&!s.get(t+o,e+o)&&a[3]<n;)a[3]++,o++;if(e+o>=u||t+o>=f||a[3]>=n)return!1;for(;e+o<u&&t+o<f&&s.get(t+o,e+o)&&a[4]<n;)a[4]++,o++;if(a[4]>=n)return!1;var c=a[0]+a[1]+a[2]+a[3]+a[4];return Math.abs(c-i)<2*i&&r.foundPatternCross(a)},r.prototype.crossCheckVertical=function(e,t,n,i){for(var a=this.image,o=a.getHeight(),s=this.getCrossCheckStateCount(),u=e;u>=0&&a.get(t,u);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!a.get(t,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&a.get(t,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<o&&a.get(t,u);)s[2]++,u++;if(u===o)return NaN;for(;u<o&&!a.get(t,u)&&s[3]<n;)s[3]++,u++;if(u===o||s[3]>=n)return NaN;for(;u<o&&a.get(t,u)&&s[4]<n;)s[4]++,u++;if(s[4]>=n)return NaN;var f=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(f-i)>=2*i?NaN:r.foundPatternCross(s)?r.centerFromEnd(s,u):NaN},r.prototype.crossCheckHorizontal=function(e,t,n,i){for(var a=this.image,o=a.getWidth(),s=this.getCrossCheckStateCount(),u=e;u>=0&&a.get(u,t);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!a.get(u,t)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&a.get(u,t)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<o&&a.get(u,t);)s[2]++,u++;if(u===o)return NaN;for(;u<o&&!a.get(u,t)&&s[3]<n;)s[3]++,u++;if(u===o||s[3]>=n)return NaN;for(;u<o&&a.get(u,t)&&s[4]<n;)s[4]++,u++;if(s[4]>=n)return NaN;var f=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(f-i)>=i?NaN:r.foundPatternCross(s)?r.centerFromEnd(s,u):NaN},r.prototype.handlePossibleCenter=function(e,t,n,i){var a=e[0]+e[1]+e[2]+e[3]+e[4],o=r.centerFromEnd(e,n),s=this.crossCheckVertical(t,Math.floor(o),e[2],a);if(!isNaN(s)&&(o=this.crossCheckHorizontal(Math.floor(o),Math.floor(s),e[2],a),!isNaN(o)&&(!i||this.crossCheckDiagonal(Math.floor(s),Math.floor(o),e[2],a)))){for(var u=a/7,f=!1,c=this.possibleCenters,l=0,h=c.length;l<h;l++){var d=c[l];if(d.aboutEquals(u,s,o)){c[l]=d.combineEstimate(s,o,u),f=!0;break}}if(!f){var v=new Lo(o,s,u);c.push(v),this.resultPointCallback!==null&&this.resultPointCallback!==void 0&&this.resultPointCallback.foundPossibleResultPoint(v)}return!0}return!1},r.prototype.findRowSkip=function(){var e,t,n=this.possibleCenters.length;if(n<=1)return 0;var i=null;try{for(var a=Bt(this.possibleCenters),o=a.next();!o.done;o=a.next()){var s=o.value;if(s.getCount()>=r.CENTER_QUORUM)if(i==null)i=s;else return this.hasSkipped=!0,Math.floor((Math.abs(i.getX()-s.getX())-Math.abs(i.getY()-s.getY()))/2)}}catch(u){e={error:u}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return 0},r.prototype.haveMultiplyConfirmedCenters=function(){var e,t,n,i,a=0,o=0,s=this.possibleCenters.length;try{for(var u=Bt(this.possibleCenters),f=u.next();!f.done;f=u.next()){var c=f.value;c.getCount()>=r.CENTER_QUORUM&&(a++,o+=c.getEstimatedModuleSize())}}catch(g){e={error:g}}finally{try{f&&!f.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}if(a<3)return!1;var l=o/s,h=0;try{for(var d=Bt(this.possibleCenters),v=d.next();!v.done;v=d.next()){var c=v.value;h+=Math.abs(c.getEstimatedModuleSize()-l)}}catch(g){n={error:g}}finally{try{v&&!v.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}return h<=.05*o},r.prototype.selectBestPatterns=function(){var e,t,n,i,a=this.possibleCenters.length;if(a<3)throw new I;var o=this.possibleCenters,s;if(a>3){var u=0,f=0;try{for(var c=Bt(this.possibleCenters),l=c.next();!l.done;l=c.next()){var h=l.value,d=h.getEstimatedModuleSize();u+=d,f+=d*d}}catch(S){e={error:S}}finally{try{l&&!l.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}s=u/a;var v=Math.sqrt(f/a-s*s);o.sort(function(S,O){var b=Math.abs(O.getEstimatedModuleSize()-s),T=Math.abs(S.getEstimatedModuleSize()-s);return b<T?-1:b>T?1:0});for(var g=Math.max(.2*s,v),y=0;y<o.length&&o.length>3;y++){var _=o[y];Math.abs(_.getEstimatedModuleSize()-s)>g&&(o.splice(y,1),y--)}}if(o.length>3){var u=0;try{for(var x=Bt(o),A=x.next();!A.done;A=x.next()){var m=A.value;u+=m.getEstimatedModuleSize()}}catch(O){n={error:O}}finally{try{A&&!A.done&&(i=x.return)&&i.call(x)}finally{if(n)throw n.error}}s=u/o.length,o.sort(function(O,b){if(b.getCount()===O.getCount()){var T=Math.abs(b.getEstimatedModuleSize()-s),D=Math.abs(O.getEstimatedModuleSize()-s);return T<D?1:T>D?-1:0}else return b.getCount()-O.getCount()}),o.splice(3)}return[o[0],o[1],o[2]]},r.CENTER_QUORUM=2,r.MIN_SKIP=3,r.MAX_MODULES=57,r}(),Vo=function(){function r(e){this.image=e}return r.prototype.getImage=function(){return this.image},r.prototype.getResultPointCallback=function(){return this.resultPointCallback},r.prototype.detect=function(e){this.resultPointCallback=e==null?null:e.get(oe.NEED_RESULT_POINT_CALLBACK);var t=new Uo(this.image,this.resultPointCallback),n=t.find(e);return this.processFinderPatternInfo(n)},r.prototype.processFinderPatternInfo=function(e){var t=e.getTopLeft(),n=e.getTopRight(),i=e.getBottomLeft(),a=this.calculateModuleSize(t,n,i);if(a<1)throw new I("No pattern found in proccess finder.");var o=r.computeDimension(t,n,i,a),s=xt.getProvisionalVersionForDimension(o),u=s.getDimensionForVersion()-7,f=null;if(s.getAlignmentPatternCenters().length>0)for(var c=n.getX()-t.getX()+i.getX(),l=n.getY()-t.getY()+i.getY(),h=1-3/u,d=Math.floor(t.getX()+h*(c-t.getX())),v=Math.floor(t.getY()+h*(l-t.getY())),g=4;g<=16;g<<=1)try{f=this.findAlignmentInRegion(a,d,v,g);break}catch(A){if(!(A instanceof I))throw A}var y=r.createTransform(t,n,i,f,o),_=r.sampleGrid(this.image,y,o),x;return f===null?x=[i,t,n]:x=[i,t,n,f],new Rr(_,x)},r.createTransform=function(e,t,n,i,a){var o=a-3.5,s,u,f,c;return i!==null?(s=i.getX(),u=i.getY(),f=o-3,c=f):(s=t.getX()-e.getX()+n.getX(),u=t.getY()-e.getY()+n.getY(),f=o,c=o),Nn.quadrilateralToQuadrilateral(3.5,3.5,o,3.5,f,c,3.5,o,e.getX(),e.getY(),t.getX(),t.getY(),s,u,n.getX(),n.getY())},r.sampleGrid=function(e,t,n){var i=Nr.getInstance();return i.sampleGridWithTransform(e,n,n,t)},r.computeDimension=function(e,t,n,i){var a=Y.round(B.distance(e,t)/i),o=Y.round(B.distance(e,n)/i),s=Math.floor((a+o)/2)+7;switch(s&3){case 0:s++;break;case 2:s--;break;case 3:throw new I("Dimensions could be not found.")}return s},r.prototype.calculateModuleSize=function(e,t,n){return(this.calculateModuleSizeOneWay(e,t)+this.calculateModuleSizeOneWay(e,n))/2},r.prototype.calculateModuleSizeOneWay=function(e,t){var n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY())),i=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY()));return isNaN(n)?i/7:isNaN(i)?n/7:(n+i)/14},r.prototype.sizeOfBlackWhiteBlackRunBothWays=function(e,t,n,i){var a=this.sizeOfBlackWhiteBlackRun(e,t,n,i),o=1,s=e-(n-e);s<0?(o=e/(e-s),s=0):s>=this.image.getWidth()&&(o=(this.image.getWidth()-1-e)/(s-e),s=this.image.getWidth()-1);var u=Math.floor(t-(i-t)*o);return o=1,u<0?(o=t/(t-u),u=0):u>=this.image.getHeight()&&(o=(this.image.getHeight()-1-t)/(u-t),u=this.image.getHeight()-1),s=Math.floor(e+(s-e)*o),a+=this.sizeOfBlackWhiteBlackRun(e,t,s,u),a-1},r.prototype.sizeOfBlackWhiteBlackRun=function(e,t,n,i){var a=Math.abs(i-t)>Math.abs(n-e);if(a){var o=e;e=t,t=o,o=n,n=i,i=o}for(var s=Math.abs(n-e),u=Math.abs(i-t),f=-s/2,c=e<n?1:-1,l=t<i?1:-1,h=0,d=n+c,v=e,g=t;v!==d;v+=c){var y=a?g:v,_=a?v:g;if(h===1===this.image.get(y,_)){if(h===2)return Y.distance(v,g,e,t);h++}if(f+=u,f>0){if(g===i)break;g+=l,f-=s}}return h===2?Y.distance(n+c,i,e,t):NaN},r.prototype.findAlignmentInRegion=function(e,t,n,i){var a=Math.floor(i*e),o=Math.max(0,t-a),s=Math.min(this.image.getWidth()-1,t+a);if(s-o<e*3)throw new I("Alignment top exceeds estimated module size.");var u=Math.max(0,n-a),f=Math.min(this.image.getHeight()-1,n+a);if(f-u<e*3)throw new I("Alignment bottom exceeds estimated module size.");var c=new Bo(this.image,o,u,s-o,f-u,e,this.resultPointCallback);return c.find()},r}(),tr=function(){function r(){this.decoder=new Ro}return r.prototype.getDecoder=function(){return this.decoder},r.prototype.decode=function(e,t){var n,i;if(t!=null&&t.get(oe.PURE_BARCODE)!==void 0){var a=r.extractPureBits(e.getBlackMatrix());n=this.decoder.decodeBitMatrix(a,t),i=r.NO_POINTS}else{var o=new Vo(e.getBlackMatrix()).detect(t);n=this.decoder.decodeBitMatrix(o.getBits(),t),i=o.getPoints()}n.getOther()instanceof kn&&n.getOther().applyMirroredCorrection(i);var s=new ke(n.getText(),n.getRawBytes(),void 0,i,L.QR_CODE,void 0),u=n.getByteSegments();u!==null&&s.putMetadata(Me.BYTE_SEGMENTS,u);var f=n.getECLevel();return f!==null&&s.putMetadata(Me.ERROR_CORRECTION_LEVEL,f),n.hasStructuredAppend()&&(s.putMetadata(Me.STRUCTURED_APPEND_SEQUENCE,n.getStructuredAppendSequenceNumber()),s.putMetadata(Me.STRUCTURED_APPEND_PARITY,n.getStructuredAppendParity())),s},r.prototype.reset=function(){},r.extractPureBits=function(e){var t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(t===null||n===null)throw new I;var i=this.moduleSize(t,e),a=t[1],o=n[1],s=t[0],u=n[0];if(s>=u||a>=o)throw new I;if(o-a!==u-s&&(u=s+(o-a),u>=e.getWidth()))throw new I;var f=Math.round((u-s+1)/i),c=Math.round((o-a+1)/i);if(f<=0||c<=0)throw new I;if(c!==f)throw new I;var l=Math.floor(i/2);a+=l,s+=l;var h=s+Math.floor((f-1)*i)-u;if(h>0){if(h>l)throw new I;s-=h}var d=a+Math.floor((c-1)*i)-o;if(d>0){if(d>l)throw new I;a-=d}for(var v=new it(f,c),g=0;g<c;g++)for(var y=a+Math.floor(g*i),_=0;_<f;_++)e.get(s+Math.floor(_*i),y)&&v.set(_,g);return v},r.moduleSize=function(e,t){for(var n=t.getHeight(),i=t.getWidth(),a=e[0],o=e[1],s=!0,u=0;a<i&&o<n;){if(s!==t.get(a,o)){if(++u===5)break;s=!s}a++,o++}if(a===i||o===n)throw new I;return(a-e[0])/7},r.NO_POINTS=new Array,r}(),Ho=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},j=function(){function r(){}return r.prototype.PDF417Common=function(){},r.getBitCountSum=function(e){return Y.sum(e)},r.toIntArray=function(e){var t,n;if(e==null||!e.length)return r.EMPTY_INT_ARRAY;var i=new Int32Array(e.length),a=0;try{for(var o=Ho(e),s=o.next();!s.done;s=o.next()){var u=s.value;i[a++]=u}}catch(f){t={error:f}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return i},r.getCodeword=function(e){var t=Se.binarySearch(r.SYMBOL_TABLE,e&262143);return t<0?-1:(r.CODEWORD_TABLE[t]-1)%r.NUMBER_OF_CODEWORDS},r.NUMBER_OF_CODEWORDS=929,r.MAX_CODEWORDS_IN_BARCODE=r.NUMBER_OF_CODEWORDS-1,r.MIN_ROWS_IN_BARCODE=3,r.MAX_ROWS_IN_BARCODE=90,r.MODULES_IN_CODEWORD=17,r.MODULES_IN_STOP_PATTERN=18,r.BARS_IN_MODULE=8,r.EMPTY_INT_ARRAY=new Int32Array([]),r.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),r.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),r}(),Go=function(){function r(e,t){this.bits=e,this.points=t}return r.prototype.getBits=function(){return this.bits},r.prototype.getPoints=function(){return this.points},r}(),Xo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Wo=function(){function r(){}return r.detectMultiple=function(e,t,n){var i=e.getBlackMatrix(),a=r.detect(n,i);return a.length||(i=i.clone(),i.rotate180(),a=r.detect(n,i)),new Go(i,a)},r.detect=function(e,t){for(var n,i,a=new Array,o=0,s=0,u=!1;o<t.getHeight();){var f=r.findVertices(t,o,s);if(f[0]==null&&f[3]==null){if(!u)break;u=!1,s=0;try{for(var c=(n=void 0,Xo(a)),l=c.next();!l.done;l=c.next()){var h=l.value;h[1]!=null&&(o=Math.trunc(Math.max(o,h[1].getY()))),h[3]!=null&&(o=Math.max(o,Math.trunc(h[3].getY())))}}catch(d){n={error:d}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}o+=r.ROW_STEP;continue}if(u=!0,a.push(f),!e)break;f[2]!=null?(s=Math.trunc(f[2].getX()),o=Math.trunc(f[2].getY())):(s=Math.trunc(f[4].getX()),o=Math.trunc(f[4].getY()))}return a},r.findVertices=function(e,t,n){var i=e.getHeight(),a=e.getWidth(),o=new Array(8);return r.copyToResult(o,r.findRowsWithPattern(e,i,a,t,n,r.START_PATTERN),r.INDEXES_START_PATTERN),o[4]!=null&&(n=Math.trunc(o[4].getX()),t=Math.trunc(o[4].getY())),r.copyToResult(o,r.findRowsWithPattern(e,i,a,t,n,r.STOP_PATTERN),r.INDEXES_STOP_PATTERN),o},r.copyToResult=function(e,t,n){for(var i=0;i<n.length;i++)e[n[i]]=t[i]},r.findRowsWithPattern=function(e,t,n,i,a,o){for(var s=new Array(4),u=!1,f=new Int32Array(o.length);i<t;i+=r.ROW_STEP){var c=r.findGuardPattern(e,a,i,n,!1,o,f);if(c!=null){for(;i>0;){var l=r.findGuardPattern(e,a,--i,n,!1,o,f);if(l!=null)c=l;else{i++;break}}s[0]=new B(c[0],i),s[1]=new B(c[1],i),u=!0;break}}var h=i+1;if(u){for(var d=0,l=Int32Array.from([Math.trunc(s[0].getX()),Math.trunc(s[1].getX())]);h<t;h++){var c=r.findGuardPattern(e,l[0],h,n,!1,o,f);if(c!=null&&Math.abs(l[0]-c[0])<r.MAX_PATTERN_DRIFT&&Math.abs(l[1]-c[1])<r.MAX_PATTERN_DRIFT)l=c,d=0;else{if(d>r.SKIPPED_ROW_COUNT_MAX)break;d++}}h-=d+1,s[2]=new B(l[0],h),s[3]=new B(l[1],h)}return h-i<r.BARCODE_MIN_HEIGHT&&Se.fill(s,null),s},r.findGuardPattern=function(e,t,n,i,a,o,s){Se.fillWithin(s,0,s.length,0);for(var u=t,f=0;e.get(u,n)&&u>0&&f++<r.MAX_PIXEL_DRIFT;)u--;for(var c=u,l=0,h=o.length,d=a;c<i;c++){var v=e.get(c,n);if(v!==d)s[l]++;else{if(l===h-1){if(r.patternMatchVariance(s,o,r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE)return new Int32Array([u,c]);u+=s[0]+s[1],ue.arraycopy(s,2,s,0,l-1),s[l-1]=0,s[l]=0,l--}else l++;s[l]=1,d=!d}}return l===h-1&&r.patternMatchVariance(s,o,r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE?new Int32Array([u,c-1]):null},r.patternMatchVariance=function(e,t,n){for(var i=e.length,a=0,o=0,s=0;s<i;s++)a+=e[s],o+=t[s];if(a<o)return 1/0;var u=a/o;n*=u;for(var f=0,c=0;c<i;c++){var l=e[c],h=t[c]*u,d=l>h?l-h:h-l;if(d>n)return 1/0;f+=d}return f/a},r.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),r.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),r.MAX_AVG_VARIANCE=.42,r.MAX_INDIVIDUAL_VARIANCE=.8,r.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),r.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),r.MAX_PIXEL_DRIFT=3,r.MAX_PATTERN_DRIFT=5,r.SKIPPED_ROW_COUNT_MAX=25,r.ROW_STEP=5,r.BARCODE_MIN_HEIGHT=10,r}(),zo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},gt=function(){function r(e,t){if(t.length===0)throw new k;this.field=e;var n=t.length;if(n>1&&t[0]===0){for(var i=1;i<n&&t[i]===0;)i++;i===n?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(n-i),ue.arraycopy(t,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}return r.prototype.getCoefficients=function(){return this.coefficients},r.prototype.getDegree=function(){return this.coefficients.length-1},r.prototype.isZero=function(){return this.coefficients[0]===0},r.prototype.getCoefficient=function(e){return this.coefficients[this.coefficients.length-1-e]},r.prototype.evaluateAt=function(e){var t,n;if(e===0)return this.getCoefficient(0);if(e===1){var i=0;try{for(var a=zo(this.coefficients),o=a.next();!o.done;o=a.next()){var s=o.value;i=this.field.add(i,s)}}catch(l){t={error:l}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i}for(var u=this.coefficients[0],f=this.coefficients.length,c=1;c<f;c++)u=this.field.add(this.field.multiply(e,u),this.coefficients[c]);return u},r.prototype.add=function(e){if(!this.field.equals(e.field))throw new k("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var t=this.coefficients,n=e.coefficients;if(t.length>n.length){var i=t;t=n,n=i}var a=new Int32Array(n.length),o=n.length-t.length;ue.arraycopy(n,0,a,0,o);for(var s=o;s<n.length;s++)a[s]=this.field.add(t[s-o],n[s]);return new r(this.field,a)},r.prototype.subtract=function(e){if(!this.field.equals(e.field))throw new k("ModulusPolys do not have same ModulusGF field");return e.isZero()?this:this.add(e.negative())},r.prototype.multiply=function(e){return e instanceof r?this.multiplyOther(e):this.multiplyScalar(e)},r.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new k("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new r(this.field,new Int32Array([0]));for(var t=this.coefficients,n=t.length,i=e.coefficients,a=i.length,o=new Int32Array(n+a-1),s=0;s<n;s++)for(var u=t[s],f=0;f<a;f++)o[s+f]=this.field.add(o[s+f],this.field.multiply(u,i[f]));return new r(this.field,o)},r.prototype.negative=function(){for(var e=this.coefficients.length,t=new Int32Array(e),n=0;n<e;n++)t[n]=this.field.subtract(0,this.coefficients[n]);return new r(this.field,t)},r.prototype.multiplyScalar=function(e){if(e===0)return new r(this.field,new Int32Array([0]));if(e===1)return this;for(var t=this.coefficients.length,n=new Int32Array(t),i=0;i<t;i++)n[i]=this.field.multiply(this.coefficients[i],e);return new r(this.field,n)},r.prototype.multiplyByMonomial=function(e,t){if(e<0)throw new k;if(t===0)return new r(this.field,new Int32Array([0]));for(var n=this.coefficients.length,i=new Int32Array(n+e),a=0;a<n;a++)i[a]=this.field.multiply(this.coefficients[a],t);return new r(this.field,i)},r.prototype.toString=function(){for(var e=new X,t=this.getDegree();t>=0;t--){var n=this.getCoefficient(t);n!==0&&(n<0?(e.append(" - "),n=-n):e.length()>0&&e.append(" + "),(t===0||n!==1)&&e.append(n),t!==0&&(t===1?e.append("x"):(e.append("x^"),e.append(t))))}return e.toString()},r}(),$o=function(){function r(){}return r.prototype.add=function(e,t){return(e+t)%this.modulus},r.prototype.subtract=function(e,t){return(this.modulus+e-t)%this.modulus},r.prototype.exp=function(e){return this.expTable[e]},r.prototype.log=function(e){if(e===0)throw new k;return this.logTable[e]},r.prototype.inverse=function(e){if(e===0)throw new Rn;return this.expTable[this.modulus-this.logTable[e]-1]},r.prototype.multiply=function(e,t){return e===0||t===0?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.modulus-1)]},r.prototype.getSize=function(){return this.modulus},r.prototype.equals=function(e){return e===this},r}(),jo=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Yo=function(r){jo(e,r);function e(t,n){var i=r.call(this)||this;i.modulus=t,i.expTable=new Int32Array(t),i.logTable=new Int32Array(t);for(var a=1,o=0;o<t;o++)i.expTable[o]=a,a=a*n%t;for(var o=0;o<t-1;o++)i.logTable[i.expTable[o]]=o;return i.zero=new gt(i,new Int32Array([0])),i.one=new gt(i,new Int32Array([1])),i}return e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,n){if(t<0)throw new k;if(n===0)return this.zero;var i=new Int32Array(t+1);return i[0]=n,new gt(this,i)},e.PDF417_GF=new e(j.NUMBER_OF_CODEWORDS,3),e}($o),Zo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ko=function(){function r(){this.field=Yo.PDF417_GF}return r.prototype.decode=function(e,t,n){for(var i,a,o=new gt(this.field,e),s=new Int32Array(t),u=!1,f=t;f>0;f--){var c=o.evaluateAt(this.field.exp(f));s[t-f]=c,c!==0&&(u=!0)}if(!u)return 0;var l=this.field.getOne();if(n!=null)try{for(var h=Zo(n),d=h.next();!d.done;d=h.next()){var v=d.value,g=this.field.exp(e.length-1-v),y=new gt(this.field,new Int32Array([this.field.subtract(0,g),1]));l=l.multiply(y)}}catch(T){i={error:T}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}for(var _=new gt(this.field,s),x=this.runEuclideanAlgorithm(this.field.buildMonomial(t,1),_,t),A=x[0],m=x[1],S=this.findErrorLocations(A),O=this.findErrorMagnitudes(m,A,S),f=0;f<S.length;f++){var b=e.length-1-this.field.log(S[f]);if(b<0)throw _e.getChecksumInstance();e[b]=this.field.subtract(e[b],O[f])}return S.length},r.prototype.runEuclideanAlgorithm=function(e,t,n){if(e.getDegree()<t.getDegree()){var i=e;e=t,t=i}for(var a=e,o=t,s=this.field.getZero(),u=this.field.getOne();o.getDegree()>=Math.round(n/2);){var f=a,c=s;if(a=o,s=u,a.isZero())throw _e.getChecksumInstance();o=f;for(var l=this.field.getZero(),h=a.getCoefficient(a.getDegree()),d=this.field.inverse(h);o.getDegree()>=a.getDegree()&&!o.isZero();){var v=o.getDegree()-a.getDegree(),g=this.field.multiply(o.getCoefficient(o.getDegree()),d);l=l.add(this.field.buildMonomial(v,g)),o=o.subtract(a.multiplyByMonomial(v,g))}u=l.multiply(s).subtract(c).negative()}var y=u.getCoefficient(0);if(y===0)throw _e.getChecksumInstance();var _=this.field.inverse(y),x=u.multiply(_),A=o.multiply(_);return[x,A]},r.prototype.findErrorLocations=function(e){for(var t=e.getDegree(),n=new Int32Array(t),i=0,a=1;a<this.field.getSize()&&i<t;a++)e.evaluateAt(a)===0&&(n[i]=this.field.inverse(a),i++);if(i!==t)throw _e.getChecksumInstance();return n},r.prototype.findErrorMagnitudes=function(e,t,n){for(var i=t.getDegree(),a=new Int32Array(i),o=1;o<=i;o++)a[i-o]=this.field.multiply(o,t.getCoefficient(o));for(var s=new gt(this.field,a),u=n.length,f=new Int32Array(u),o=0;o<u;o++){var c=this.field.inverse(n[o]),l=this.field.subtract(0,e.evaluateAt(c)),h=this.field.inverse(s.evaluateAt(c));f[o]=this.field.multiply(l,h)}return f},r}(),mr=function(){function r(e,t,n,i,a){e instanceof r?this.constructor_2(e):this.constructor_1(e,t,n,i,a)}return r.prototype.constructor_1=function(e,t,n,i,a){var o=t==null||n==null,s=i==null||a==null;if(o&&s)throw new I;o?(t=new B(0,i.getY()),n=new B(0,a.getY())):s&&(i=new B(e.getWidth()-1,t.getY()),a=new B(e.getWidth()-1,n.getY())),this.image=e,this.topLeft=t,this.bottomLeft=n,this.topRight=i,this.bottomRight=a,this.minX=Math.trunc(Math.min(t.getX(),n.getX())),this.maxX=Math.trunc(Math.max(i.getX(),a.getX())),this.minY=Math.trunc(Math.min(t.getY(),i.getY())),this.maxY=Math.trunc(Math.max(n.getY(),a.getY()))},r.prototype.constructor_2=function(e){this.image=e.image,this.topLeft=e.getTopLeft(),this.bottomLeft=e.getBottomLeft(),this.topRight=e.getTopRight(),this.bottomRight=e.getBottomRight(),this.minX=e.getMinX(),this.maxX=e.getMaxX(),this.minY=e.getMinY(),this.maxY=e.getMaxY()},r.merge=function(e,t){return e==null?t:t==null?e:new r(e.image,e.topLeft,e.bottomLeft,t.topRight,t.bottomRight)},r.prototype.addMissingRows=function(e,t,n){var i=this.topLeft,a=this.bottomLeft,o=this.topRight,s=this.bottomRight;if(e>0){var u=n?this.topLeft:this.topRight,f=Math.trunc(u.getY()-e);f<0&&(f=0);var c=new B(u.getX(),f);n?i=c:o=c}if(t>0){var l=n?this.bottomLeft:this.bottomRight,h=Math.trunc(l.getY()+t);h>=this.image.getHeight()&&(h=this.image.getHeight()-1);var d=new B(l.getX(),h);n?a=d:s=d}return new r(this.image,i,a,o,s)},r.prototype.getMinX=function(){return this.minX},r.prototype.getMaxX=function(){return this.maxX},r.prototype.getMinY=function(){return this.minY},r.prototype.getMaxY=function(){return this.maxY},r.prototype.getTopLeft=function(){return this.topLeft},r.prototype.getTopRight=function(){return this.topRight},r.prototype.getBottomLeft=function(){return this.bottomLeft},r.prototype.getBottomRight=function(){return this.bottomRight},r}(),qo=function(){function r(e,t,n,i){this.columnCount=e,this.errorCorrectionLevel=i,this.rowCountUpperPart=t,this.rowCountLowerPart=n,this.rowCount=t+n}return r.prototype.getColumnCount=function(){return this.columnCount},r.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},r.prototype.getRowCount=function(){return this.rowCount},r.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},r.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},r}(),Mr=function(){function r(){this.buffer=""}return r.form=function(e,t){var n=-1;function i(o,s,u,f,c,l){if(o==="%%")return"%";if(t[++n]!==void 0){o=f?parseInt(f.substr(1)):void 0;var h=c?parseInt(c.substr(1)):void 0,d;switch(l){case"s":d=t[n];break;case"c":d=t[n][0];break;case"f":d=parseFloat(t[n]).toFixed(o);break;case"p":d=parseFloat(t[n]).toPrecision(o);break;case"e":d=parseFloat(t[n]).toExponential(o);break;case"x":d=parseInt(t[n]).toString(h||16);break;case"d":d=parseFloat(parseInt(t[n],h||10).toPrecision(o)).toFixed(0);break}d=typeof d=="object"?JSON.stringify(d):(+d).toString(h);for(var v=parseInt(u),g=u&&u[0]+""=="0"?"0":" ";d.length<v;)d=s!==void 0?d+g:g+d;return d}}var a=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return e.replace(a,i)},r.prototype.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.buffer+=r.form(e,t)},r.prototype.toString=function(){return this.buffer},r}(),Qo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Un=function(){function r(e){this.boundingBox=new mr(e),this.codewords=new Array(e.getMaxY()-e.getMinY()+1)}return r.prototype.getCodewordNearby=function(e){var t=this.getCodeword(e);if(t!=null)return t;for(var n=1;n<r.MAX_NEARBY_DISTANCE;n++){var i=this.imageRowToCodewordIndex(e)-n;if(i>=0&&(t=this.codewords[i],t!=null)||(i=this.imageRowToCodewordIndex(e)+n,i<this.codewords.length&&(t=this.codewords[i],t!=null)))return t}return null},r.prototype.imageRowToCodewordIndex=function(e){return e-this.boundingBox.getMinY()},r.prototype.setCodeword=function(e,t){this.codewords[this.imageRowToCodewordIndex(e)]=t},r.prototype.getCodeword=function(e){return this.codewords[this.imageRowToCodewordIndex(e)]},r.prototype.getBoundingBox=function(){return this.boundingBox},r.prototype.getCodewords=function(){return this.codewords},r.prototype.toString=function(){var e,t,n=new Mr,i=0;try{for(var a=Qo(this.codewords),o=a.next();!o.done;o=a.next()){var s=o.value;if(s==null){n.format("%3d:    |   %n",i++);continue}n.format("%3d: %3d|%3d%n",i++,s.getRowNumber(),s.getValue())}}catch(u){e={error:u}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n.toString()},r.MAX_NEARBY_DISTANCE=5,r}(),Jo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},es=function(r,e){var t=typeof Symbol=="function"&&r[Symbol.iterator];if(!t)return r;var n=t.call(r),i,a=[],o;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(o)throw o.error}}return a},Ut=function(){function r(){this.values=new Map}return r.prototype.setValue=function(e){e=Math.trunc(e);var t=this.values.get(e);t==null&&(t=0),t++,this.values.set(e,t)},r.prototype.getValue=function(){var e,t,n=-1,i=new Array,a=function(l,h){var d={getKey:function(){return l},getValue:function(){return h}};d.getValue()>n?(n=d.getValue(),i=[],i.push(d.getKey())):d.getValue()===n&&i.push(d.getKey())};try{for(var o=Jo(this.values.entries()),s=o.next();!s.done;s=o.next()){var u=es(s.value,2),f=u[0],c=u[1];a(f,c)}}catch(l){e={error:l}}finally{try{s&&!s.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}return j.toIntArray(i)},r.prototype.getConfidence=function(e){return this.values.get(e)},r}(),ts=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),dr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},vn=function(r){ts(e,r);function e(t,n){var i=r.call(this,t)||this;return i._isLeft=n,i}return e.prototype.setRowNumbers=function(){var t,n;try{for(var i=dr(this.getCodewords()),a=i.next();!a.done;a=i.next()){var o=a.value;o!=null&&o.setRowNumberAsRowIndicatorColumn()}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var n=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(n,t);for(var i=this.getBoundingBox(),a=this._isLeft?i.getTopLeft():i.getTopRight(),o=this._isLeft?i.getBottomLeft():i.getBottomRight(),s=this.imageRowToCodewordIndex(Math.trunc(a.getY())),u=this.imageRowToCodewordIndex(Math.trunc(o.getY())),f=-1,c=1,l=0,h=s;h<u;h++)if(n[h]!=null){var d=n[h],v=d.getRowNumber()-f;if(v===0)l++;else if(v===1)c=Math.max(c,l),l=1,f=d.getRowNumber();else if(v<0||d.getRowNumber()>=t.getRowCount()||v>h)n[h]=null;else{var g=void 0;c>2?g=(c-2)*v:g=v;for(var y=g>=h,_=1;_<=g&&!y;_++)y=n[h-_]!=null;y?n[h]=null:(f=d.getRowNumber(),l=1)}}},e.prototype.getRowHeights=function(){var t,n,i=this.getBarcodeMetadata();if(i==null)return null;this.adjustIncompleteIndicatorColumnRowNumbers(i);var a=new Int32Array(i.getRowCount());try{for(var o=dr(this.getCodewords()),s=o.next();!s.done;s=o.next()){var u=s.value;if(u!=null){var f=u.getRowNumber();if(f>=a.length)continue;a[f]++}}}catch(c){t={error:c}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return a},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var n=this.getBoundingBox(),i=this._isLeft?n.getTopLeft():n.getTopRight(),a=this._isLeft?n.getBottomLeft():n.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(i.getY())),s=this.imageRowToCodewordIndex(Math.trunc(a.getY())),u=this.getCodewords(),f=-1,c=o;c<s;c++)if(u[c]!=null){var l=u[c];l.setRowNumberAsRowIndicatorColumn();var h=l.getRowNumber()-f;h===0||(h===1?f=l.getRowNumber():l.getRowNumber()>=t.getRowCount()?u[c]=null:f=l.getRowNumber())}},e.prototype.getBarcodeMetadata=function(){var t,n,i=this.getCodewords(),a=new Ut,o=new Ut,s=new Ut,u=new Ut;try{for(var f=dr(i),c=f.next();!c.done;c=f.next()){var l=c.value;if(l!=null){l.setRowNumberAsRowIndicatorColumn();var h=l.getValue()%30,d=l.getRowNumber();switch(this._isLeft||(d+=2),d%3){case 0:o.setValue(h*3+1);break;case 1:u.setValue(h/3),s.setValue(h%3);break;case 2:a.setValue(h+1);break}}}}catch(g){t={error:g}}finally{try{c&&!c.done&&(n=f.return)&&n.call(f)}finally{if(t)throw t.error}}if(a.getValue().length===0||o.getValue().length===0||s.getValue().length===0||u.getValue().length===0||a.getValue()[0]<1||o.getValue()[0]+s.getValue()[0]<j.MIN_ROWS_IN_BARCODE||o.getValue()[0]+s.getValue()[0]>j.MAX_ROWS_IN_BARCODE)return null;var v=new qo(a.getValue()[0],o.getValue()[0],s.getValue()[0],u.getValue()[0]);return this.removeIncorrectCodewords(i,v),v},e.prototype.removeIncorrectCodewords=function(t,n){for(var i=0;i<t.length;i++){var a=t[i];if(t[i]!=null){var o=a.getValue()%30,s=a.getRowNumber();if(s>n.getRowCount()){t[i]=null;continue}switch(this._isLeft||(s+=2),s%3){case 0:o*3+1!==n.getRowCountUpperPart()&&(t[i]=null);break;case 1:(Math.trunc(o/3)!==n.getErrorCorrectionLevel()||o%3!==n.getRowCountLowerPart())&&(t[i]=null);break;case 2:o+1!==n.getColumnCount()&&(t[i]=null);break}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+`
`+r.prototype.toString.call(this)},e}(Un),rs=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ns=function(){function r(e,t){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=e,this.barcodeColumnCount=e.getColumnCount(),this.boundingBox=t,this.detectionResultColumns=new Array(this.barcodeColumnCount+2)}return r.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var e=j.MAX_CODEWORDS_IN_BARCODE,t;do t=e,e=this.adjustRowNumbersAndGetCount();while(e>0&&e<t);return this.detectionResultColumns},r.prototype.adjustIndicatorColumnRowNumbers=function(e){e!=null&&e.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},r.prototype.adjustRowNumbersAndGetCount=function(){var e=this.adjustRowNumbersByRow();if(e===0)return 0;for(var t=1;t<this.barcodeColumnCount+1;t++)for(var n=this.detectionResultColumns[t].getCodewords(),i=0;i<n.length;i++)n[i]!=null&&(n[i].hasValidRowNumber()||this.adjustRowNumbers(t,i,n));return e},r.prototype.adjustRowNumbersByRow=function(){this.adjustRowNumbersFromBothRI();var e=this.adjustRowNumbersFromLRI();return e+this.adjustRowNumbersFromRRI()},r.prototype.adjustRowNumbersFromBothRI=function(){if(!(this.detectionResultColumns[0]==null||this.detectionResultColumns[this.barcodeColumnCount+1]==null)){for(var e=this.detectionResultColumns[0].getCodewords(),t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<e.length;n++)if(e[n]!=null&&t[n]!=null&&e[n].getRowNumber()===t[n].getRowNumber())for(var i=1;i<=this.barcodeColumnCount;i++){var a=this.detectionResultColumns[i].getCodewords()[n];a!=null&&(a.setRowNumber(e[n].getRowNumber()),a.hasValidRowNumber()||(this.detectionResultColumns[i].getCodewords()[n]=null))}}},r.prototype.adjustRowNumbersFromRRI=function(){if(this.detectionResultColumns[this.barcodeColumnCount+1]==null)return 0;for(var e=0,t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<t.length;n++)if(t[n]!=null)for(var i=t[n].getRowNumber(),a=0,o=this.barcodeColumnCount+1;o>0&&a<this.ADJUST_ROW_NUMBER_SKIP;o--){var s=this.detectionResultColumns[o].getCodewords()[n];s!=null&&(a=r.adjustRowNumberIfValid(i,a,s),s.hasValidRowNumber()||e++)}return e},r.prototype.adjustRowNumbersFromLRI=function(){if(this.detectionResultColumns[0]==null)return 0;for(var e=0,t=this.detectionResultColumns[0].getCodewords(),n=0;n<t.length;n++)if(t[n]!=null)for(var i=t[n].getRowNumber(),a=0,o=1;o<this.barcodeColumnCount+1&&a<this.ADJUST_ROW_NUMBER_SKIP;o++){var s=this.detectionResultColumns[o].getCodewords()[n];s!=null&&(a=r.adjustRowNumberIfValid(i,a,s),s.hasValidRowNumber()||e++)}return e},r.adjustRowNumberIfValid=function(e,t,n){return n==null||n.hasValidRowNumber()||(n.isValidRowNumber(e)?(n.setRowNumber(e),t=0):++t),t},r.prototype.adjustRowNumbers=function(e,t,n){var i,a;if(this.detectionResultColumns[e-1]!=null){var o=n[t],s=this.detectionResultColumns[e-1].getCodewords(),u=s;this.detectionResultColumns[e+1]!=null&&(u=this.detectionResultColumns[e+1].getCodewords());var f=new Array(14);f[2]=s[t],f[3]=u[t],t>0&&(f[0]=n[t-1],f[4]=s[t-1],f[5]=u[t-1]),t>1&&(f[8]=n[t-2],f[10]=s[t-2],f[11]=u[t-2]),t<n.length-1&&(f[1]=n[t+1],f[6]=s[t+1],f[7]=u[t+1]),t<n.length-2&&(f[9]=n[t+2],f[12]=s[t+2],f[13]=u[t+2]);try{for(var c=rs(f),l=c.next();!l.done;l=c.next()){var h=l.value;if(r.adjustRowNumber(o,h))return}}catch(d){i={error:d}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}}},r.adjustRowNumber=function(e,t){return t==null?!1:t.hasValidRowNumber()&&t.getBucket()===e.getBucket()?(e.setRowNumber(t.getRowNumber()),!0):!1},r.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},r.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},r.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},r.prototype.setBoundingBox=function(e){this.boundingBox=e},r.prototype.getBoundingBox=function(){return this.boundingBox},r.prototype.setDetectionResultColumn=function(e,t){this.detectionResultColumns[e]=t},r.prototype.getDetectionResultColumn=function(e){return this.detectionResultColumns[e]},r.prototype.toString=function(){var e=this.detectionResultColumns[0];e==null&&(e=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var t=new Mr,n=0;n<e.getCodewords().length;n++){t.format("CW %3d:",n);for(var i=0;i<this.barcodeColumnCount+2;i++){if(this.detectionResultColumns[i]==null){t.format("    |   ");continue}var a=this.detectionResultColumns[i].getCodewords()[n];if(a==null){t.format("    |   ");continue}t.format(" %3d|%3d",a.getRowNumber(),a.getValue())}t.format("%n")}return t.toString()},r}(),is=function(){function r(e,t,n,i){this.rowNumber=r.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(t),this.bucket=Math.trunc(n),this.value=Math.trunc(i)}return r.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},r.prototype.isValidRowNumber=function(e){return e!==r.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},r.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(Math.trunc(this.value/30)*3+Math.trunc(this.bucket/3))},r.prototype.getWidth=function(){return this.endX-this.startX},r.prototype.getStartX=function(){return this.startX},r.prototype.getEndX=function(){return this.endX},r.prototype.getBucket=function(){return this.bucket},r.prototype.getValue=function(){return this.value},r.prototype.getRowNumber=function(){return this.rowNumber},r.prototype.setRowNumber=function(e){this.rowNumber=e},r.prototype.toString=function(){return this.rowNumber+"|"+this.value},r.BARCODE_ROW_UNKNOWN=-1,r}(),as=function(){function r(){}return r.initialize=function(){for(var e=0;e<j.SYMBOL_TABLE.length;e++)for(var t=j.SYMBOL_TABLE[e],n=t&1,i=0;i<j.BARS_IN_MODULE;i++){for(var a=0;(t&1)===n;)a+=1,t>>=1;n=t&1,r.RATIOS_TABLE[e]||(r.RATIOS_TABLE[e]=new Array(j.BARS_IN_MODULE)),r.RATIOS_TABLE[e][j.BARS_IN_MODULE-i-1]=Math.fround(a/j.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},r.getDecodedValue=function(e){var t=r.getDecodedCodewordValue(r.sampleBitCounts(e));return t!==-1?t:r.getClosestDecodedValue(e)},r.sampleBitCounts=function(e){for(var t=Y.sum(e),n=new Int32Array(j.BARS_IN_MODULE),i=0,a=0,o=0;o<j.MODULES_IN_CODEWORD;o++){var s=t/(2*j.MODULES_IN_CODEWORD)+o*t/j.MODULES_IN_CODEWORD;a+e[i]<=s&&(a+=e[i],i++),n[i]++}return n},r.getDecodedCodewordValue=function(e){var t=r.getBitValue(e);return j.getCodeword(t)===-1?-1:t},r.getBitValue=function(e){for(var t=0,n=0;n<e.length;n++)for(var i=0;i<e[n];i++)t=t<<1|(n%2===0?1:0);return Math.trunc(t)},r.getClosestDecodedValue=function(e){var t=Y.sum(e),n=new Array(j.BARS_IN_MODULE);if(t>1)for(var i=0;i<n.length;i++)n[i]=Math.fround(e[i]/t);var a=Ar.MAX_VALUE,o=-1;this.bSymbolTableReady||r.initialize();for(var s=0;s<r.RATIOS_TABLE.length;s++){for(var u=0,f=r.RATIOS_TABLE[s],c=0;c<j.BARS_IN_MODULE;c++){var l=Math.fround(f[c]-n[c]);if(u+=Math.fround(l*l),u>=a)break}u<a&&(a=u,o=j.SYMBOL_TABLE[s])}return o},r.bSymbolTableReady=!1,r.RATIOS_TABLE=new Array(j.SYMBOL_TABLE.length).map(function(e){return new Array(j.BARS_IN_MODULE)}),r}(),os=function(){function r(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return r.prototype.getSegmentIndex=function(){return this.segmentIndex},r.prototype.setSegmentIndex=function(e){this.segmentIndex=e},r.prototype.getFileId=function(){return this.fileId},r.prototype.setFileId=function(e){this.fileId=e},r.prototype.getOptionalData=function(){return this.optionalData},r.prototype.setOptionalData=function(e){this.optionalData=e},r.prototype.isLastSegment=function(){return this.lastSegment},r.prototype.setLastSegment=function(e){this.lastSegment=e},r.prototype.getSegmentCount=function(){return this.segmentCount},r.prototype.setSegmentCount=function(e){this.segmentCount=e},r.prototype.getSender=function(){return this.sender||null},r.prototype.setSender=function(e){this.sender=e},r.prototype.getAddressee=function(){return this.addressee||null},r.prototype.setAddressee=function(e){this.addressee=e},r.prototype.getFileName=function(){return this.fileName},r.prototype.setFileName=function(e){this.fileName=e},r.prototype.getFileSize=function(){return this.fileSize},r.prototype.setFileSize=function(e){this.fileSize=e},r.prototype.getChecksum=function(){return this.checksum},r.prototype.setChecksum=function(e){this.checksum=e},r.prototype.getTimestamp=function(){return this.timestamp},r.prototype.setTimestamp=function(e){this.timestamp=e},r}(),pn=function(){function r(){}return r.parseLong=function(e,t){return t===void 0&&(t=void 0),parseInt(e,t)},r}(),ss=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),us=function(r){ss(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="NullPointerException",e}($e),fs=function(){function r(){}return r.prototype.writeBytes=function(e){this.writeBytesOffset(e,0,e.length)},r.prototype.writeBytesOffset=function(e,t,n){if(e==null)throw new us;if(t<0||t>e.length||n<0||t+n>e.length||t+n<0)throw new Dr;if(n===0)return;for(var i=0;i<n;i++)this.write(e[t+i])},r.prototype.flush=function(){},r.prototype.close=function(){},r}(),cs=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ls=function(r){cs(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e}($e),ds=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),hs=function(r){ds(e,r);function e(t){t===void 0&&(t=32);var n=r.call(this)||this;if(n.count=0,t<0)throw new k("Negative initial size: "+t);return n.buf=new Uint8Array(t),n}return e.prototype.ensureCapacity=function(t){t-this.buf.length>0&&this.grow(t)},e.prototype.grow=function(t){var n=this.buf.length,i=n<<1;if(i-t<0&&(i=t),i<0){if(t<0)throw new ls;i=z.MAX_VALUE}this.buf=Se.copyOfUint8Array(this.buf,i)},e.prototype.write=function(t){this.ensureCapacity(this.count+1),this.buf[this.count]=t,this.count+=1},e.prototype.writeBytesOffset=function(t,n,i){if(n<0||n>t.length||i<0||n+i-t.length>0)throw new Dr;this.ensureCapacity(this.count+i),ue.arraycopy(t,n,this.buf,this.count,i),this.count+=i},e.prototype.writeTo=function(t){t.writeBytesOffset(this.buf,0,this.count)},e.prototype.reset=function(){this.count=0},e.prototype.toByteArray=function(){return Se.copyOfUint8Array(this.buf,this.count)},e.prototype.size=function(){return this.count},e.prototype.toString=function(t){return t?typeof t=="string"?this.toString_string(t):this.toString_number(t):this.toString_void()},e.prototype.toString_void=function(){return new String(this.buf).toString()},e.prototype.toString_string=function(t){return new String(this.buf).toString()},e.prototype.toString_number=function(t){return new String(this.buf).toString()},e.prototype.close=function(){},e}(fs),ae;(function(r){r[r.ALPHA=0]="ALPHA",r[r.LOWER=1]="LOWER",r[r.MIXED=2]="MIXED",r[r.PUNCT=3]="PUNCT",r[r.ALPHA_SHIFT=4]="ALPHA_SHIFT",r[r.PUNCT_SHIFT=5]="PUNCT_SHIFT"})(ae||(ae={}));function Vn(){if(typeof window<"u")return window.BigInt||null;if(typeof global<"u")return global.BigInt||null;if(typeof self<"u")return self.BigInt||null;throw new Error("Can't search globals for BigInt!")}var $t;function ut(r){if(typeof $t>"u"&&($t=Vn()),$t===null)throw new Error("BigInt is not supported!");return $t(r)}function vs(){var r=[];r[0]=ut(1);var e=ut(900);r[1]=e;for(var t=2;t<16;t++)r[t]=r[t-1]*e;return r}var ps=function(){function r(){}return r.decode=function(e,t){var n=new X(""),i=me.ISO8859_1;n.enableDecoding(i);for(var a=1,o=e[a++],s=new os;a<e[0];){switch(o){case r.TEXT_COMPACTION_MODE_LATCH:a=r.textCompaction(e,a,n);break;case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:a=r.byteCompaction(o,e,i,a,n);break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[a++]);break;case r.NUMERIC_COMPACTION_MODE_LATCH:a=r.numericCompaction(e,a,n);break;case r.ECI_CHARSET:me.getCharacterSetECIByValue(e[a++]);break;case r.ECI_GENERAL_PURPOSE:a+=2;break;case r.ECI_USER_DEFINED:a++;break;case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:a=r.decodeMacroBlock(e,a,s);break;case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:throw new N;default:a--,a=r.textCompaction(e,a,n);break}if(a<e.length)o=e[a++];else throw N.getFormatInstance()}if(n.length()===0)throw N.getFormatInstance();var u=new ar(null,n.toString(),null,t);return u.setOther(s),u},r.decodeMacroBlock=function(e,t,n){if(t+r.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw N.getFormatInstance();for(var i=new Int32Array(r.NUMBER_OF_SEQUENCE_CODEWORDS),a=0;a<r.NUMBER_OF_SEQUENCE_CODEWORDS;a++,t++)i[a]=e[t];n.setSegmentIndex(z.parseInt(r.decodeBase900toBase10(i,r.NUMBER_OF_SEQUENCE_CODEWORDS)));var o=new X;t=r.textCompaction(e,t,o),n.setFileId(o.toString());var s=-1;for(e[t]===r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=t+1);t<e[0];)switch(e[t]){case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(t++,e[t]){case r.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var u=new X;t=r.textCompaction(e,t+1,u),n.setFileName(u.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var f=new X;t=r.textCompaction(e,t+1,f),n.setSender(f.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var c=new X;t=r.textCompaction(e,t+1,c),n.setAddressee(c.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var l=new X;t=r.numericCompaction(e,t+1,l),n.setSegmentCount(z.parseInt(l.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var h=new X;t=r.numericCompaction(e,t+1,h),n.setTimestamp(pn.parseLong(h.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var d=new X;t=r.numericCompaction(e,t+1,d),n.setChecksum(z.parseInt(d.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var v=new X;t=r.numericCompaction(e,t+1,v),n.setFileSize(pn.parseLong(v.toString()));break;default:throw N.getFormatInstance()}break;case r.MACRO_PDF417_TERMINATOR:t++,n.setLastSegment(!0);break;default:throw N.getFormatInstance()}if(s!==-1){var g=t-s;n.isLastSegment()&&g--,n.setOptionalData(Se.copyOfRange(e,s,s+g))}return t},r.textCompaction=function(e,t,n){for(var i=new Int32Array((e[0]-t)*2),a=new Int32Array((e[0]-t)*2),o=0,s=!1;t<e[0]&&!s;){var u=e[t++];if(u<r.TEXT_COMPACTION_MODE_LATCH)i[o]=u/30,i[o+1]=u%30,o+=2;else switch(u){case r.TEXT_COMPACTION_MODE_LATCH:i[o++]=r.TEXT_COMPACTION_MODE_LATCH;break;case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:t--,s=!0;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i[o]=r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,u=e[t++],a[o]=u,o++;break}}return r.decodeTextCompaction(i,a,o,n),t},r.decodeTextCompaction=function(e,t,n,i){for(var a=ae.ALPHA,o=ae.ALPHA,s=0;s<n;){var u=e[s],f="";switch(a){case ae.ALPHA:if(u<26)f=String.fromCharCode(65+u);else switch(u){case 26:f=" ";break;case r.LL:a=ae.LOWER;break;case r.ML:a=ae.MIXED;break;case r.PS:o=a,a=ae.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ae.ALPHA;break}break;case ae.LOWER:if(u<26)f=String.fromCharCode(97+u);else switch(u){case 26:f=" ";break;case r.AS:o=a,a=ae.ALPHA_SHIFT;break;case r.ML:a=ae.MIXED;break;case r.PS:o=a,a=ae.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ae.ALPHA;break}break;case ae.MIXED:if(u<r.PL)f=r.MIXED_CHARS[u];else switch(u){case r.PL:a=ae.PUNCT;break;case 26:f=" ";break;case r.LL:a=ae.LOWER;break;case r.AL:a=ae.ALPHA;break;case r.PS:o=a,a=ae.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ae.ALPHA;break}break;case ae.PUNCT:if(u<r.PAL)f=r.PUNCT_CHARS[u];else switch(u){case r.PAL:a=ae.ALPHA;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ae.ALPHA;break}break;case ae.ALPHA_SHIFT:if(a=o,u<26)f=String.fromCharCode(65+u);else switch(u){case 26:f=" ";break;case r.TEXT_COMPACTION_MODE_LATCH:a=ae.ALPHA;break}break;case ae.PUNCT_SHIFT:if(a=o,u<r.PAL)f=r.PUNCT_CHARS[u];else switch(u){case r.PAL:a=ae.ALPHA;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ae.ALPHA;break}break}f!==""&&i.append(f),s++}},r.byteCompaction=function(e,t,n,i,a){var o=new hs,s=0,u=0,f=!1;switch(e){case r.BYTE_COMPACTION_MODE_LATCH:for(var c=new Int32Array(6),l=t[i++];i<t[0]&&!f;)switch(c[s++]=l,u=900*u+l,l=t[i++],l){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:i--,f=!0;break;default:if(s%5===0&&s>0){for(var h=0;h<6;++h)o.write(Number(ut(u)>>ut(8*(5-h))));u=0,s=0}break}i===t[0]&&l<r.TEXT_COMPACTION_MODE_LATCH&&(c[s++]=l);for(var d=0;d<s;d++)o.write(c[d]);break;case r.BYTE_COMPACTION_MODE_LATCH_6:for(;i<t[0]&&!f;){var v=t[i++];if(v<r.TEXT_COMPACTION_MODE_LATCH)s++,u=900*u+v;else switch(v){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:i--,f=!0;break}if(s%5===0&&s>0){for(var h=0;h<6;++h)o.write(Number(ut(u)>>ut(8*(5-h))));u=0,s=0}}break}return a.append(qe.decode(o.toByteArray(),n)),i},r.numericCompaction=function(e,t,n){for(var i=0,a=!1,o=new Int32Array(r.MAX_NUMERIC_CODEWORDS);t<e[0]&&!a;){var s=e[t++];if(t===e[0]&&(a=!0),s<r.TEXT_COMPACTION_MODE_LATCH)o[i]=s,i++;else switch(s){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:t--,a=!0;break}(i%r.MAX_NUMERIC_CODEWORDS===0||s===r.NUMERIC_COMPACTION_MODE_LATCH||a)&&i>0&&(n.append(r.decodeBase900toBase10(o,i)),i=0)}return t},r.decodeBase900toBase10=function(e,t){for(var n=ut(0),i=0;i<t;i++)n+=r.EXP900[t-i-1]*ut(e[i]);var a=n.toString();if(a.charAt(0)!=="1")throw new N;return a.substring(1)},r.TEXT_COMPACTION_MODE_LATCH=900,r.BYTE_COMPACTION_MODE_LATCH=901,r.NUMERIC_COMPACTION_MODE_LATCH=902,r.BYTE_COMPACTION_MODE_LATCH_6=924,r.ECI_USER_DEFINED=925,r.ECI_GENERAL_PURPOSE=926,r.ECI_CHARSET=927,r.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,r.MACRO_PDF417_TERMINATOR=922,r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,r.MAX_NUMERIC_CODEWORDS=15,r.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,r.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,r.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,r.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,r.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,r.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,r.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,r.PL=25,r.LL=27,r.AS=27,r.ML=28,r.AL=28,r.PS=29,r.PAL=29,r.PUNCT_CHARS=`;<>@[\\]_\`~!\r	,:
-.$/"|*()?{}'`,r.MIXED_CHARS="0123456789&\r	,:#-.$/+%*=^",r.EXP900=Vn()?vs():[],r.NUMBER_OF_SEQUENCE_CODEWORDS=2,r}(),Ft=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},gs=function(){function r(){}return r.decode=function(e,t,n,i,a,o,s){for(var u=new mr(e,t,n,i,a),f=null,c=null,l,h=!0;;h=!1){if(t!=null&&(f=r.getRowIndicatorColumn(e,u,t,!0,o,s)),i!=null&&(c=r.getRowIndicatorColumn(e,u,i,!1,o,s)),l=r.merge(f,c),l==null)throw I.getNotFoundInstance();var d=l.getBoundingBox();if(h&&d!=null&&(d.getMinY()<u.getMinY()||d.getMaxY()>u.getMaxY()))u=d;else break}l.setBoundingBox(u);var v=l.getBarcodeColumnCount()+1;l.setDetectionResultColumn(0,f),l.setDetectionResultColumn(v,c);for(var g=f!=null,y=1;y<=v;y++){var _=g?y:v-y;if(l.getDetectionResultColumn(_)===void 0){var x=void 0;_===0||_===v?x=new vn(u,_===0):x=new Un(u),l.setDetectionResultColumn(_,x);for(var A=-1,m=A,S=u.getMinY();S<=u.getMaxY();S++){if(A=r.getStartColumn(l,_,S,g),A<0||A>u.getMaxX()){if(m===-1)continue;A=m}var O=r.detectCodeword(e,u.getMinX(),u.getMaxX(),g,A,S,o,s);O!=null&&(x.setCodeword(S,O),m=A,o=Math.min(o,O.getWidth()),s=Math.max(s,O.getWidth()))}}}return r.createDecoderResult(l)},r.merge=function(e,t){if(e==null&&t==null)return null;var n=r.getBarcodeMetadata(e,t);if(n==null)return null;var i=mr.merge(r.adjustBoundingBox(e),r.adjustBoundingBox(t));return new ns(n,i)},r.adjustBoundingBox=function(e){var t,n;if(e==null)return null;var i=e.getRowHeights();if(i==null)return null;var a=r.getMax(i),o=0;try{for(var s=Ft(i),u=s.next();!u.done;u=s.next()){var f=u.value;if(o+=a-f,f>0)break}}catch(d){t={error:d}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}for(var c=e.getCodewords(),l=0;o>0&&c[l]==null;l++)o--;for(var h=0,l=i.length-1;l>=0&&(h+=a-i[l],!(i[l]>0));l--);for(var l=c.length-1;h>0&&c[l]==null;l--)h--;return e.getBoundingBox().addMissingRows(o,h,e.isLeft())},r.getMax=function(e){var t,n,i=-1;try{for(var a=Ft(e),o=a.next();!o.done;o=a.next()){var s=o.value;i=Math.max(i,s)}}catch(u){t={error:u}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i},r.getBarcodeMetadata=function(e,t){var n;if(e==null||(n=e.getBarcodeMetadata())==null)return t==null?null:t.getBarcodeMetadata();var i;return t==null||(i=t.getBarcodeMetadata())==null?n:n.getColumnCount()!==i.getColumnCount()&&n.getErrorCorrectionLevel()!==i.getErrorCorrectionLevel()&&n.getRowCount()!==i.getRowCount()?null:n},r.getRowIndicatorColumn=function(e,t,n,i,a,o){for(var s=new vn(t,i),u=0;u<2;u++)for(var f=u===0?1:-1,c=Math.trunc(Math.trunc(n.getX())),l=Math.trunc(Math.trunc(n.getY()));l<=t.getMaxY()&&l>=t.getMinY();l+=f){var h=r.detectCodeword(e,0,e.getWidth(),i,c,l,a,o);h!=null&&(s.setCodeword(l,h),i?c=h.getStartX():c=h.getEndX())}return s},r.adjustCodewordCount=function(e,t){var n=t[0][1],i=n.getValue(),a=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-r.getNumberOfECCodeWords(e.getBarcodeECLevel());if(i.length===0){if(a<1||a>j.MAX_CODEWORDS_IN_BARCODE)throw I.getNotFoundInstance();n.setValue(a)}else i[0]!==a&&n.setValue(a)},r.createDecoderResult=function(e){var t=r.createBarcodeMatrix(e);r.adjustCodewordCount(e,t);for(var n=new Array,i=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),a=[],o=new Array,s=0;s<e.getBarcodeRowCount();s++)for(var u=0;u<e.getBarcodeColumnCount();u++){var f=t[s][u+1].getValue(),c=s*e.getBarcodeColumnCount()+u;f.length===0?n.push(c):f.length===1?i[c]=f[0]:(o.push(c),a.push(f))}for(var l=new Array(a.length),h=0;h<l.length;h++)l[h]=a[h];return r.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),i,j.toIntArray(n),j.toIntArray(o),l)},r.createDecoderResultFromAmbiguousValues=function(e,t,n,i,a){for(var o=new Int32Array(i.length),s=100;s-- >0;){for(var u=0;u<o.length;u++)t[i[u]]=a[u][o[u]];try{return r.decodeCodewords(t,e,n)}catch(c){var f=c instanceof _e;if(!f)throw c}if(o.length===0)throw _e.getChecksumInstance();for(var u=0;u<o.length;u++)if(o[u]<a[u].length-1){o[u]++;break}else if(o[u]=0,u===o.length-1)throw _e.getChecksumInstance()}throw _e.getChecksumInstance()},r.createBarcodeMatrix=function(e){for(var t,n,i,a,o=Array.from({length:e.getBarcodeRowCount()},function(){return new Array(e.getBarcodeColumnCount()+2)}),s=0;s<o.length;s++)for(var u=0;u<o[s].length;u++)o[s][u]=new Ut;var f=0;try{for(var c=Ft(e.getDetectionResultColumns()),l=c.next();!l.done;l=c.next()){var h=l.value;if(h!=null)try{for(var d=(i=void 0,Ft(h.getCodewords())),v=d.next();!v.done;v=d.next()){var g=v.value;if(g!=null){var y=g.getRowNumber();if(y>=0){if(y>=o.length)continue;o[y][f].setValue(g.getValue())}}}}catch(_){i={error:_}}finally{try{v&&!v.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}f++}}catch(_){t={error:_}}finally{try{l&&!l.done&&(n=c.return)&&n.call(c)}finally{if(t)throw t.error}}return o},r.isValidBarcodeColumn=function(e,t){return t>=0&&t<=e.getBarcodeColumnCount()+1},r.getStartColumn=function(e,t,n,i){var a,o,s=i?1:-1,u=null;if(r.isValidBarcodeColumn(e,t-s)&&(u=e.getDetectionResultColumn(t-s).getCodeword(n)),u!=null)return i?u.getEndX():u.getStartX();if(u=e.getDetectionResultColumn(t).getCodewordNearby(n),u!=null)return i?u.getStartX():u.getEndX();if(r.isValidBarcodeColumn(e,t-s)&&(u=e.getDetectionResultColumn(t-s).getCodewordNearby(n)),u!=null)return i?u.getEndX():u.getStartX();for(var f=0;r.isValidBarcodeColumn(e,t-s);){t-=s;try{for(var c=(a=void 0,Ft(e.getDetectionResultColumn(t).getCodewords())),l=c.next();!l.done;l=c.next()){var h=l.value;if(h!=null)return(i?h.getEndX():h.getStartX())+s*f*(h.getEndX()-h.getStartX())}}catch(d){a={error:d}}finally{try{l&&!l.done&&(o=c.return)&&o.call(c)}finally{if(a)throw a.error}}f++}return i?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},r.detectCodeword=function(e,t,n,i,a,o,s,u){a=r.adjustCodewordStartColumn(e,t,n,i,a,o);var f=r.getModuleBitCount(e,t,n,i,a,o);if(f==null)return null;var c,l=Y.sum(f);if(i)c=a+l;else{for(var h=0;h<f.length/2;h++){var d=f[h];f[h]=f[f.length-1-h],f[f.length-1-h]=d}c=a,a=c-l}if(!r.checkCodewordSkew(l,s,u))return null;var v=as.getDecodedValue(f),g=j.getCodeword(v);return g===-1?null:new is(a,c,r.getCodewordBucketNumber(v),g)},r.getModuleBitCount=function(e,t,n,i,a,o){for(var s=a,u=new Int32Array(8),f=0,c=i?1:-1,l=i;(i?s<n:s>=t)&&f<u.length;)e.get(s,o)===l?(u[f]++,s+=c):(f++,l=!l);return f===u.length||s===(i?n:t)&&f===u.length-1?u:null},r.getNumberOfECCodeWords=function(e){return 2<<e},r.adjustCodewordStartColumn=function(e,t,n,i,a,o){for(var s=a,u=i?-1:1,f=0;f<2;f++){for(;(i?s>=t:s<n)&&i===e.get(s,o);){if(Math.abs(a-s)>r.CODEWORD_SKEW_SIZE)return a;s+=u}u=-u,i=!i}return s},r.checkCodewordSkew=function(e,t,n){return t-r.CODEWORD_SKEW_SIZE<=e&&e<=n+r.CODEWORD_SKEW_SIZE},r.decodeCodewords=function(e,t,n){if(e.length===0)throw N.getFormatInstance();var i=1<<t+1,a=r.correctErrors(e,n,i);r.verifyCodewordCount(e,i);var o=ps.decode(e,""+t);return o.setErrorsCorrected(a),o.setErasures(n.length),o},r.correctErrors=function(e,t,n){if(t!=null&&t.length>n/2+r.MAX_ERRORS||n<0||n>r.MAX_EC_CODEWORDS)throw _e.getChecksumInstance();return r.errorCorrection.decode(e,n,t)},r.verifyCodewordCount=function(e,t){if(e.length<4)throw N.getFormatInstance();var n=e[0];if(n>e.length)throw N.getFormatInstance();if(n===0)if(t<e.length)e[0]=e.length-t;else throw N.getFormatInstance()},r.getBitCountForCodeword=function(e){for(var t=new Int32Array(8),n=0,i=t.length-1;!((e&1)!==n&&(n=e&1,i--,i<0));)t[i]++,e>>=1;return t},r.getCodewordBucketNumber=function(e){return e instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(e):this.getCodewordBucketNumber_number(e)},r.getCodewordBucketNumber_number=function(e){return r.getCodewordBucketNumber(r.getBitCountForCodeword(e))},r.getCodewordBucketNumber_Int32Array=function(e){return(e[0]-e[2]+e[4]-e[6]+9)%9},r.toString=function(e){for(var t=new Mr,n=0;n<e.length;n++){t.format("Row %2d: ",n);for(var i=0;i<e[n].length;i++){var a=e[n][i];a.getValue().length===0?t.format("        ",null):t.format("%4d(%2d)",a.getValue()[0],a.getConfidence(a.getValue()[0]))}t.format("%n")}return t.toString()},r.CODEWORD_SKEW_SIZE=2,r.MAX_ERRORS=3,r.MAX_EC_CODEWORDS=512,r.errorCorrection=new Ko,r}(),xs=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rr=function(){function r(){}return r.prototype.decode=function(e,t){t===void 0&&(t=null);var n=r.decode(e,t,!1);if(n==null||n.length===0||n[0]==null)throw I.getNotFoundInstance();return n[0]},r.prototype.decodeMultiple=function(e,t){t===void 0&&(t=null);try{return r.decode(e,t,!0)}catch(n){throw n instanceof N||n instanceof _e?I.getNotFoundInstance():n}},r.decode=function(e,t,n){var i,a,o=new Array,s=Wo.detectMultiple(e,t,n);try{for(var u=xs(s.getPoints()),f=u.next();!f.done;f=u.next()){var c=f.value,l=gs.decode(s.getBits(),c[4],c[5],c[6],c[7],r.getMinCodewordWidth(c),r.getMaxCodewordWidth(c)),h=new ke(l.getText(),l.getRawBytes(),void 0,c,L.PDF_417);h.putMetadata(Me.ERROR_CORRECTION_LEVEL,l.getECLevel());var d=l.getOther();d!=null&&h.putMetadata(Me.PDF417_EXTRA_METADATA,d),o.push(h)}}catch(v){i={error:v}}finally{try{f&&!f.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}return o.map(function(v){return v})},r.getMaxWidth=function(e,t){return e==null||t==null?0:Math.trunc(Math.abs(e.getX()-t.getX()))},r.getMinWidth=function(e,t){return e==null||t==null?z.MAX_VALUE:Math.trunc(Math.abs(e.getX()-t.getX()))},r.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(r.getMaxWidth(e[0],e[4]),r.getMaxWidth(e[6],e[2])*j.MODULES_IN_CODEWORD/j.MODULES_IN_STOP_PATTERN),Math.max(r.getMaxWidth(e[1],e[5]),r.getMaxWidth(e[7],e[3])*j.MODULES_IN_CODEWORD/j.MODULES_IN_STOP_PATTERN)))},r.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(r.getMinWidth(e[0],e[4]),r.getMinWidth(e[6],e[2])*j.MODULES_IN_CODEWORD/j.MODULES_IN_STOP_PATTERN),Math.min(r.getMinWidth(e[1],e[5]),r.getMinWidth(e[7],e[3])*j.MODULES_IN_CODEWORD/j.MODULES_IN_STOP_PATTERN)))},r.prototype.reset=function(){},r}(),ys=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),gn=function(r){ys(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ReaderException",e}($e),xn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Hn=function(){function r(){}return r.prototype.decode=function(e,t){return this.setHints(t),this.decodeInternal(e)},r.prototype.decodeWithState=function(e){return(this.readers===null||this.readers===void 0)&&this.setHints(null),this.decodeInternal(e)},r.prototype.setHints=function(e){this.hints=e;var t=e!=null&&e.get(oe.TRY_HARDER)!==void 0,n=e==null?null:e.get(oe.POSSIBLE_FORMATS),i=new Array;if(n!=null){var a=n.some(function(o){return o===L.UPC_A||o===L.UPC_E||o===L.EAN_13||o===L.EAN_8||o===L.CODABAR||o===L.CODE_39||o===L.CODE_93||o===L.CODE_128||o===L.ITF||o===L.RSS_14||o===L.RSS_EXPANDED});a&&!t&&i.push(new St(e)),n.includes(L.QR_CODE)&&i.push(new tr),n.includes(L.DATA_MATRIX)&&i.push(new er),n.includes(L.AZTEC)&&i.push(new Qt),n.includes(L.PDF_417)&&i.push(new rr),a&&t&&i.push(new St(e))}i.length===0&&(t||i.push(new St(e)),i.push(new tr),i.push(new er),i.push(new Qt),i.push(new rr),t&&i.push(new St(e))),this.readers=i},r.prototype.reset=function(){var e,t;if(this.readers!==null)try{for(var n=xn(this.readers),i=n.next();!i.done;i=n.next()){var a=i.value;a.reset()}}catch(o){e={error:o}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}},r.prototype.decodeInternal=function(e){var t,n;if(this.readers===null)throw new gn("No readers where selected, nothing can be read.");try{for(var i=xn(this.readers),a=i.next();!a.done;a=i.next()){var o=a.value;try{return o.decode(e,this.hints)}catch(s){if(s instanceof gn)continue}}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}throw new I("No MultiFormat Readers were able to detect the code.")},r}(),_s=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){_s(e,r);function e(t,n){t===void 0&&(t=null),n===void 0&&(n=500);var i=this,a=new Hn;return a.setHints(t),i=r.call(this,a,n)||this,i}return e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e})(Dt);var ws=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){ws(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new rr,t)||this}return e})(Dt);var As=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){As(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new tr,t)||this}return e})(Dt);var Sr;(function(r){r[r.ERROR_CORRECTION=0]="ERROR_CORRECTION",r[r.CHARACTER_SET=1]="CHARACTER_SET",r[r.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",r[r.DATA_MATRIX_COMPACT=3]="DATA_MATRIX_COMPACT",r[r.MIN_SIZE=4]="MIN_SIZE",r[r.MAX_SIZE=5]="MAX_SIZE",r[r.MARGIN=6]="MARGIN",r[r.PDF417_COMPACT=7]="PDF417_COMPACT",r[r.PDF417_COMPACTION=8]="PDF417_COMPACTION",r[r.PDF417_DIMENSIONS=9]="PDF417_DIMENSIONS",r[r.AZTEC_LAYERS=10]="AZTEC_LAYERS",r[r.QR_VERSION=11]="QR_VERSION",r[r.GS1_FORMAT=12]="GS1_FORMAT",r[r.FORCE_C40=13]="FORCE_C40"})(Sr||(Sr={}));const jt=Sr;var Gn=function(){function r(e){this.field=e,this.cachedGenerators=[],this.cachedGenerators.push(new ft(e,Int32Array.from([1])))}return r.prototype.buildGenerator=function(e){var t=this.cachedGenerators;if(e>=t.length)for(var n=t[t.length-1],i=this.field,a=t.length;a<=e;a++){var o=n.multiply(new ft(i,Int32Array.from([1,i.exp(a-1+i.getGeneratorBase())])));t.push(o),n=o}return t[e]},r.prototype.encode=function(e,t){if(t===0)throw new k("No error correction bytes");var n=e.length-t;if(n<=0)throw new k("No data bytes provided");var i=this.buildGenerator(t),a=new Int32Array(n);ue.arraycopy(e,0,a,0,n);var o=new ft(this.field,a);o=o.multiplyByMonomial(t,1);for(var s=o.divide(i)[1],u=s.getCoefficients(),f=t-u.length,c=0;c<f;c++)e[n+c]=0;ue.arraycopy(u,0,e,n+f,u.length)},r}(),Vt=function(){function r(){}return r.applyMaskPenaltyRule1=function(e){return r.applyMaskPenaltyRule1Internal(e,!0)+r.applyMaskPenaltyRule1Internal(e,!1)},r.applyMaskPenaltyRule2=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a-1;o++)for(var s=n[o],u=0;u<i-1;u++){var f=s[u];f===s[u+1]&&f===n[o+1][u]&&f===n[o+1][u+1]&&t++}return r.N2*t},r.applyMaskPenaltyRule3=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a;o++)for(var s=0;s<i;s++){var u=n[o];s+6<i&&u[s]===1&&u[s+1]===0&&u[s+2]===1&&u[s+3]===1&&u[s+4]===1&&u[s+5]===0&&u[s+6]===1&&(r.isWhiteHorizontal(u,s-4,s)||r.isWhiteHorizontal(u,s+7,s+11))&&t++,o+6<a&&n[o][s]===1&&n[o+1][s]===0&&n[o+2][s]===1&&n[o+3][s]===1&&n[o+4][s]===1&&n[o+5][s]===0&&n[o+6][s]===1&&(r.isWhiteVertical(n,s,o-4,o)||r.isWhiteVertical(n,s,o+7,o+11))&&t++}return t*r.N3},r.isWhiteHorizontal=function(e,t,n){t=Math.max(t,0),n=Math.min(n,e.length);for(var i=t;i<n;i++)if(e[i]===1)return!1;return!0},r.isWhiteVertical=function(e,t,n,i){n=Math.max(n,0),i=Math.min(i,e.length);for(var a=n;a<i;a++)if(e[a][t]===1)return!1;return!0},r.applyMaskPenaltyRule4=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a;o++)for(var s=n[o],u=0;u<i;u++)s[u]===1&&t++;var f=e.getHeight()*e.getWidth(),c=Math.floor(Math.abs(t*2-f)*10/f);return c*r.N4},r.getDataMaskBit=function(e,t,n){var i,a;switch(e){case 0:i=n+t&1;break;case 1:i=n&1;break;case 2:i=t%3;break;case 3:i=(n+t)%3;break;case 4:i=Math.floor(n/2)+Math.floor(t/3)&1;break;case 5:a=n*t,i=(a&1)+a%3;break;case 6:a=n*t,i=(a&1)+a%3&1;break;case 7:a=n*t,i=a%3+(n+t&1)&1;break;default:throw new k("Invalid mask pattern: "+e)}return i===0},r.applyMaskPenaltyRule1Internal=function(e,t){for(var n=0,i=t?e.getHeight():e.getWidth(),a=t?e.getWidth():e.getHeight(),o=e.getArray(),s=0;s<i;s++){for(var u=0,f=-1,c=0;c<a;c++){var l=t?o[s][c]:o[c][s];l===f?u++:(u>=5&&(n+=r.N1+(u-5)),u=1,f=l)}u>=5&&(n+=r.N1+(u-5))}return n},r.N1=3,r.N2=3,r.N3=40,r.N4=10,r}(),Cs=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Es=function(){function r(e,t){this.width=e,this.height=t;for(var n=new Array(t),i=0;i!==t;i++)n[i]=new Uint8Array(e);this.bytes=n}return r.prototype.getHeight=function(){return this.height},r.prototype.getWidth=function(){return this.width},r.prototype.get=function(e,t){return this.bytes[t][e]},r.prototype.getArray=function(){return this.bytes},r.prototype.setNumber=function(e,t,n){this.bytes[t][e]=n},r.prototype.setBoolean=function(e,t,n){this.bytes[t][e]=n?1:0},r.prototype.clear=function(e){var t,n;try{for(var i=Cs(this.bytes),a=i.next();!a.done;a=i.next()){var o=a.value;Se.fill(o,e)}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;if(this.width!==t.width||this.height!==t.height)return!1;for(var n=0,i=this.height;n<i;++n)for(var a=this.bytes[n],o=t.bytes[n],s=0,u=this.width;s<u;++s)if(a[s]!==o[s])return!1;return!0},r.prototype.toString=function(){for(var e=new X,t=0,n=this.height;t<n;++t){for(var i=this.bytes[t],a=0,o=this.width;a<o;++a)switch(i[a]){case 0:e.append(" 0");break;case 1:e.append(" 1");break;default:e.append("  ");break}e.append(`
`)}return e.toString()},r}(),Ir=function(){function r(){this.maskPattern=-1}return r.prototype.getMode=function(){return this.mode},r.prototype.getECLevel=function(){return this.ecLevel},r.prototype.getVersion=function(){return this.version},r.prototype.getMaskPattern=function(){return this.maskPattern},r.prototype.getMatrix=function(){return this.matrix},r.prototype.toString=function(){var e=new X;return e.append(`<<
`),e.append(" mode: "),e.append(this.mode?this.mode.toString():"null"),e.append(`
 ecLevel: `),e.append(this.ecLevel?this.ecLevel.toString():"null"),e.append(`
 version: `),e.append(this.version?this.version.toString():"null"),e.append(`
 maskPattern: `),e.append(this.maskPattern.toString()),this.matrix?(e.append(`
 matrix:
`),e.append(this.matrix.toString())):e.append(`
 matrix: null
`),e.append(`>>
`),e.toString()},r.prototype.setMode=function(e){this.mode=e},r.prototype.setECLevel=function(e){this.ecLevel=e},r.prototype.setVersion=function(e){this.version=e},r.prototype.setMaskPattern=function(e){this.maskPattern=e},r.prototype.setMatrix=function(e){this.matrix=e},r.isValidMaskPattern=function(e){return e>=0&&e<r.NUM_MASK_PATTERNS},r.NUM_MASK_PATTERNS=8,r}(),ms=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),ce=function(r){ms(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="WriterException",e}($e),yn=function(){function r(){}return r.clearMatrix=function(e){e.clear(255)},r.buildMatrix=function(e,t,n,i,a){r.clearMatrix(a),r.embedBasicPatterns(n,a),r.embedTypeInfo(t,i,a),r.maybeEmbedVersionInfo(n,a),r.embedDataBits(e,i,a)},r.embedBasicPatterns=function(e,t){r.embedPositionDetectionPatternsAndSeparators(t),r.embedDarkDotAtLeftBottomCorner(t),r.maybeEmbedPositionAdjustmentPatterns(e,t),r.embedTimingPatterns(t)},r.embedTypeInfo=function(e,t,n){var i=new be;r.makeTypeInfoBits(e,t,i);for(var a=0,o=i.getSize();a<o;++a){var s=i.get(i.getSize()-1-a),u=r.TYPE_INFO_COORDINATES[a],f=u[0],c=u[1];if(n.setBoolean(f,c,s),a<8){var l=n.getWidth()-a-1,h=8;n.setBoolean(l,h,s)}else{var l=8,h=n.getHeight()-7+(a-8);n.setBoolean(l,h,s)}}},r.maybeEmbedVersionInfo=function(e,t){if(!(e.getVersionNumber()<7)){var n=new be;r.makeVersionInfoBits(e,n);for(var i=6*3-1,a=0;a<6;++a)for(var o=0;o<3;++o){var s=n.get(i);i--,t.setBoolean(a,t.getHeight()-11+o,s),t.setBoolean(t.getHeight()-11+o,a,s)}}},r.embedDataBits=function(e,t,n){for(var i=0,a=-1,o=n.getWidth()-1,s=n.getHeight()-1;o>0;){for(o===6&&(o-=1);s>=0&&s<n.getHeight();){for(var u=0;u<2;++u){var f=o-u;if(r.isEmpty(n.get(f,s))){var c=void 0;i<e.getSize()?(c=e.get(i),++i):c=!1,t!==255&&Vt.getDataMaskBit(t,f,s)&&(c=!c),n.setBoolean(f,s,c)}}s+=a}a=-a,s+=a,o-=2}if(i!==e.getSize())throw new ce("Not all bits consumed: "+i+"/"+e.getSize())},r.findMSBSet=function(e){return 32-z.numberOfLeadingZeros(e)},r.calculateBCHCode=function(e,t){if(t===0)throw new k("0 polynomial");var n=r.findMSBSet(t);for(e<<=n-1;r.findMSBSet(e)>=n;)e^=t<<r.findMSBSet(e)-n;return e},r.makeTypeInfoBits=function(e,t,n){if(!Ir.isValidMaskPattern(t))throw new ce("Invalid mask pattern");var i=e.getBits()<<3|t;n.appendBits(i,5);var a=r.calculateBCHCode(i,r.TYPE_INFO_POLY);n.appendBits(a,10);var o=new be;if(o.appendBits(r.TYPE_INFO_MASK_PATTERN,15),n.xor(o),n.getSize()!==15)throw new ce("should not happen but we got: "+n.getSize())},r.makeVersionInfoBits=function(e,t){t.appendBits(e.getVersionNumber(),6);var n=r.calculateBCHCode(e.getVersionNumber(),r.VERSION_INFO_POLY);if(t.appendBits(n,12),t.getSize()!==18)throw new ce("should not happen but we got: "+t.getSize())},r.isEmpty=function(e){return e===255},r.embedTimingPatterns=function(e){for(var t=8;t<e.getWidth()-8;++t){var n=(t+1)%2;r.isEmpty(e.get(t,6))&&e.setNumber(t,6,n),r.isEmpty(e.get(6,t))&&e.setNumber(6,t,n)}},r.embedDarkDotAtLeftBottomCorner=function(e){if(e.get(8,e.getHeight()-8)===0)throw new ce;e.setNumber(8,e.getHeight()-8,1)},r.embedHorizontalSeparationPattern=function(e,t,n){for(var i=0;i<8;++i){if(!r.isEmpty(n.get(e+i,t)))throw new ce;n.setNumber(e+i,t,0)}},r.embedVerticalSeparationPattern=function(e,t,n){for(var i=0;i<7;++i){if(!r.isEmpty(n.get(e,t+i)))throw new ce;n.setNumber(e,t+i,0)}},r.embedPositionAdjustmentPattern=function(e,t,n){for(var i=0;i<5;++i)for(var a=r.POSITION_ADJUSTMENT_PATTERN[i],o=0;o<5;++o)n.setNumber(e+o,t+i,a[o])},r.embedPositionDetectionPattern=function(e,t,n){for(var i=0;i<7;++i)for(var a=r.POSITION_DETECTION_PATTERN[i],o=0;o<7;++o)n.setNumber(e+o,t+i,a[o])},r.embedPositionDetectionPatternsAndSeparators=function(e){var t=r.POSITION_DETECTION_PATTERN[0].length;r.embedPositionDetectionPattern(0,0,e),r.embedPositionDetectionPattern(e.getWidth()-t,0,e),r.embedPositionDetectionPattern(0,e.getWidth()-t,e);var n=8;r.embedHorizontalSeparationPattern(0,n-1,e),r.embedHorizontalSeparationPattern(e.getWidth()-n,n-1,e),r.embedHorizontalSeparationPattern(0,e.getWidth()-n,e);var i=7;r.embedVerticalSeparationPattern(i,0,e),r.embedVerticalSeparationPattern(e.getHeight()-i-1,0,e),r.embedVerticalSeparationPattern(i,e.getHeight()-i,e)},r.maybeEmbedPositionAdjustmentPatterns=function(e,t){if(!(e.getVersionNumber()<2))for(var n=e.getVersionNumber()-1,i=r.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],a=0,o=i.length;a!==o;a++){var s=i[a];if(s>=0)for(var u=0;u!==o;u++){var f=i[u];f>=0&&r.isEmpty(t.get(f,s))&&r.embedPositionAdjustmentPattern(f-2,s-2,t)}}},r.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),r.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),r.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),r.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),r.VERSION_INFO_POLY=7973,r.TYPE_INFO_POLY=1335,r.TYPE_INFO_MASK_PATTERN=21522,r}(),Ss=function(){function r(e,t){this.dataBytes=e,this.errorCorrectionBytes=t}return r.prototype.getDataBytes=function(){return this.dataBytes},r.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},r}(),_n=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};(function(){function r(){}return r.calculateMaskPenalty=function(e){return Vt.applyMaskPenaltyRule1(e)+Vt.applyMaskPenaltyRule2(e)+Vt.applyMaskPenaltyRule3(e)+Vt.applyMaskPenaltyRule4(e)},r.encode=function(e,t,n){n===void 0&&(n=null);var i=r.DEFAULT_BYTE_MODE_ENCODING,a=n!==null&&n.get(jt.CHARACTER_SET)!==void 0;a&&(i=n.get(jt.CHARACTER_SET).toString());var o=this.chooseMode(e,i),s=new be;if(o===le.BYTE&&(a||r.DEFAULT_BYTE_MODE_ENCODING!==i)){var u=me.getCharacterSetECIByName(i);u!==void 0&&this.appendECI(u,s)}this.appendModeInfo(o,s);var f=new be;this.appendBytes(e,o,f,i);var c;if(n!==null&&n.get(jt.QR_VERSION)!==void 0){var l=Number.parseInt(n.get(jt.QR_VERSION).toString(),10);c=xt.getVersionForNumber(l);var h=this.calculateBitsNeeded(o,s,f,c);if(!this.willFit(h,c,t))throw new ce("Data too big for requested version")}else c=this.recommendVersion(t,o,s,f);var d=new be;d.appendBitArray(s);var v=o===le.BYTE?f.getSizeInBytes():e.length;this.appendLengthInfo(v,c,o,d),d.appendBitArray(f);var g=c.getECBlocksForLevel(t),y=c.getTotalCodewords()-g.getTotalECCodewords();this.terminateBits(y,d);var _=this.interleaveWithECBytes(d,c.getTotalCodewords(),y,g.getNumBlocks()),x=new Ir;x.setECLevel(t),x.setMode(o),x.setVersion(c);var A=c.getDimensionForVersion(),m=new Es(A,A),S=this.chooseMaskPattern(_,t,c,m);return x.setMaskPattern(S),yn.buildMatrix(_,t,c,S,m),x.setMatrix(m),x},r.recommendVersion=function(e,t,n,i){var a=this.calculateBitsNeeded(t,n,i,xt.getVersionForNumber(1)),o=this.chooseVersion(a,e),s=this.calculateBitsNeeded(t,n,i,o);return this.chooseVersion(s,e)},r.calculateBitsNeeded=function(e,t,n,i){return t.getSize()+e.getCharacterCountBits(i)+n.getSize()},r.getAlphanumericCode=function(e){return e<r.ALPHANUMERIC_TABLE.length?r.ALPHANUMERIC_TABLE[e]:-1},r.chooseMode=function(e,t){if(t===void 0&&(t=null),me.SJIS.getName()===t&&this.isOnlyDoubleByteKanji(e))return le.KANJI;for(var n=!1,i=!1,a=0,o=e.length;a<o;++a){var s=e.charAt(a);if(r.isDigit(s))n=!0;else if(this.getAlphanumericCode(s.charCodeAt(0))!==-1)i=!0;else return le.BYTE}return i?le.ALPHANUMERIC:n?le.NUMERIC:le.BYTE},r.isOnlyDoubleByteKanji=function(e){var t;try{t=qe.encode(e,me.SJIS)}catch{return!1}var n=t.length;if(n%2!==0)return!1;for(var i=0;i<n;i+=2){var a=t[i]&255;if((a<129||a>159)&&(a<224||a>235))return!1}return!0},r.chooseMaskPattern=function(e,t,n,i){for(var a=Number.MAX_SAFE_INTEGER,o=-1,s=0;s<Ir.NUM_MASK_PATTERNS;s++){yn.buildMatrix(e,t,n,s,i);var u=this.calculateMaskPenalty(i);u<a&&(a=u,o=s)}return o},r.chooseVersion=function(e,t){for(var n=1;n<=40;n++){var i=xt.getVersionForNumber(n);if(r.willFit(e,i,t))return i}throw new ce("Data too big")},r.willFit=function(e,t,n){var i=t.getTotalCodewords(),a=t.getECBlocksForLevel(n),o=a.getTotalECCodewords(),s=i-o,u=(e+7)/8;return s>=u},r.terminateBits=function(e,t){var n=e*8;if(t.getSize()>n)throw new ce("data bits cannot fit in the QR Code"+t.getSize()+" > "+n);for(var i=0;i<4&&t.getSize()<n;++i)t.appendBit(!1);var a=t.getSize()&7;if(a>0)for(var i=a;i<8;i++)t.appendBit(!1);for(var o=e-t.getSizeInBytes(),i=0;i<o;++i)t.appendBits(i&1?17:236,8);if(t.getSize()!==n)throw new ce("Bits size does not equal capacity")},r.getNumDataBytesAndNumECBytesForBlockID=function(e,t,n,i,a,o){if(i>=n)throw new ce("Block ID too large");var s=e%n,u=n-s,f=Math.floor(e/n),c=f+1,l=Math.floor(t/n),h=l+1,d=f-l,v=c-h;if(d!==v)throw new ce("EC bytes mismatch");if(n!==u+s)throw new ce("RS blocks mismatch");if(e!==(l+d)*u+(h+v)*s)throw new ce("Total bytes mismatch");i<u?(a[0]=l,o[0]=d):(a[0]=h,o[0]=v)},r.interleaveWithECBytes=function(e,t,n,i){var a,o,s,u;if(e.getSizeInBytes()!==n)throw new ce("Number of bits and data bytes does not match");for(var f=0,c=0,l=0,h=new Array,d=0;d<i;++d){var v=new Int32Array(1),g=new Int32Array(1);r.getNumDataBytesAndNumECBytesForBlockID(t,n,i,d,v,g);var y=v[0],_=new Uint8Array(y);e.toBytes(8*f,_,0,y);var x=r.generateECBytes(_,g[0]);h.push(new Ss(_,x)),c=Math.max(c,y),l=Math.max(l,x.length),f+=v[0]}if(n!==f)throw new ce("Data bytes does not match offset");for(var A=new be,d=0;d<c;++d)try{for(var m=(a=void 0,_n(h)),S=m.next();!S.done;S=m.next()){var O=S.value,_=O.getDataBytes();d<_.length&&A.appendBits(_[d],8)}}catch(P){a={error:P}}finally{try{S&&!S.done&&(o=m.return)&&o.call(m)}finally{if(a)throw a.error}}for(var d=0;d<l;++d)try{for(var b=(s=void 0,_n(h)),T=b.next();!T.done;T=b.next()){var O=T.value,x=O.getErrorCorrectionBytes();d<x.length&&A.appendBits(x[d],8)}}catch(P){s={error:P}}finally{try{T&&!T.done&&(u=b.return)&&u.call(b)}finally{if(s)throw s.error}}if(t!==A.getSizeInBytes())throw new ce("Interleaving error: "+t+" and "+A.getSizeInBytes()+" differ.");return A},r.generateECBytes=function(e,t){for(var n=e.length,i=new Int32Array(n+t),a=0;a<n;a++)i[a]=e[a]&255;new Gn(ze.QR_CODE_FIELD_256).encode(i,t);for(var o=new Uint8Array(t),a=0;a<t;a++)o[a]=i[n+a];return o},r.appendModeInfo=function(e,t){t.appendBits(e.getBits(),4)},r.appendLengthInfo=function(e,t,n,i){var a=n.getCharacterCountBits(t);if(e>=1<<a)throw new ce(e+" is bigger than "+((1<<a)-1));i.appendBits(e,a)},r.appendBytes=function(e,t,n,i){switch(t){case le.NUMERIC:r.appendNumericBytes(e,n);break;case le.ALPHANUMERIC:r.appendAlphanumericBytes(e,n);break;case le.BYTE:r.append8BitBytes(e,n,i);break;case le.KANJI:r.appendKanjiBytes(e,n);break;default:throw new ce("Invalid mode: "+t)}},r.getDigit=function(e){return e.charCodeAt(0)-48},r.isDigit=function(e){var t=r.getDigit(e);return t>=0&&t<=9},r.appendNumericBytes=function(e,t){for(var n=e.length,i=0;i<n;){var a=r.getDigit(e.charAt(i));if(i+2<n){var o=r.getDigit(e.charAt(i+1)),s=r.getDigit(e.charAt(i+2));t.appendBits(a*100+o*10+s,10),i+=3}else if(i+1<n){var o=r.getDigit(e.charAt(i+1));t.appendBits(a*10+o,7),i+=2}else t.appendBits(a,4),i++}},r.appendAlphanumericBytes=function(e,t){for(var n=e.length,i=0;i<n;){var a=r.getAlphanumericCode(e.charCodeAt(i));if(a===-1)throw new ce;if(i+1<n){var o=r.getAlphanumericCode(e.charCodeAt(i+1));if(o===-1)throw new ce;t.appendBits(a*45+o,11),i+=2}else t.appendBits(a,6),i++}},r.append8BitBytes=function(e,t,n){var i;try{i=qe.encode(e,n)}catch(u){throw new ce(u)}for(var a=0,o=i.length;a!==o;a++){var s=i[a];t.appendBits(s,8)}},r.appendKanjiBytes=function(e,t){var n;try{n=qe.encode(e,me.SJIS)}catch(l){throw new ce(l)}for(var i=n.length,a=0;a<i;a+=2){var o=n[a]&255,s=n[a+1]&255,u=o<<8&4294967295|s,f=-1;if(u>=33088&&u<=40956?f=u-33088:u>=57408&&u<=60351&&(f=u-49472),f===-1)throw new ce("Invalid byte sequence");var c=(f>>8)*192+(f&255);t.appendBits(c,13)}},r.appendECI=function(e,t){t.appendBits(le.ECI.getBits(),4),t.appendBits(e.getValue(),8)},r.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),r.DEFAULT_BYTE_MODE_ENCODING=me.UTF8.getName(),r})();var Is=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){Is(e,r);function e(t,n,i,a,o,s,u,f){var c=r.call(this,s,u)||this;if(c.yuvData=t,c.dataWidth=n,c.dataHeight=i,c.left=a,c.top=o,a+s>n||o+u>i)throw new k("Crop rectangle does not fit within image data.");return f&&c.reverseHorizontal(s,u),c}return e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new k("Requested row is outside the image: "+t);var i=this.getWidth();(n==null||n.length<i)&&(n=new Uint8ClampedArray(i));var a=(t+this.top)*this.dataWidth+this.left;return ue.arraycopy(this.yuvData,a,n,0,i),n},e.prototype.getMatrix=function(){var t=this.getWidth(),n=this.getHeight();if(t===this.dataWidth&&n===this.dataHeight)return this.yuvData;var i=t*n,a=new Uint8ClampedArray(i),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return ue.arraycopy(this.yuvData,o,a,0,i),a;for(var s=0;s<n;s++){var u=s*t;ue.arraycopy(this.yuvData,o,a,u,t),o+=this.dataWidth}return a},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return new e(this.yuvData,this.dataWidth,this.dataHeight,this.left+t,this.top+n,i,a,!1)},e.prototype.renderThumbnail=function(){for(var t=this.getWidth()/e.THUMBNAIL_SCALE_FACTOR,n=this.getHeight()/e.THUMBNAIL_SCALE_FACTOR,i=new Int32Array(t*n),a=this.yuvData,o=this.top*this.dataWidth+this.left,s=0;s<n;s++){for(var u=s*t,f=0;f<t;f++){var c=a[o+f*e.THUMBNAIL_SCALE_FACTOR]&255;i[u+f]=4278190080|c*65793}o+=this.dataWidth*e.THUMBNAIL_SCALE_FACTOR}return i},e.prototype.getThumbnailWidth=function(){return this.getWidth()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.getThumbnailHeight=function(){return this.getHeight()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.reverseHorizontal=function(t,n){for(var i=this.yuvData,a=0,o=this.top*this.dataWidth+this.left;a<n;a++,o+=this.dataWidth)for(var s=o+t/2,u=o,f=o+t-1;u<s;u++,f--){var c=i[u];i[u]=i[f],i[f]=c}},e.prototype.invert=function(){return new ir(this)},e.THUMBNAIL_SCALE_FACTOR=2,e})(Ht);var Os=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){Os(e,r);function e(t,n,i,a,o,s,u){var f=r.call(this,n,i)||this;if(f.dataWidth=a,f.dataHeight=o,f.left=s,f.top=u,t.BYTES_PER_ELEMENT===4){for(var c=n*i,l=new Uint8ClampedArray(c),h=0;h<c;h++){var d=t[h],v=d>>16&255,g=d>>7&510,y=d&255;l[h]=(v+g+y)/4&255}f.luminances=l}else f.luminances=t;if(a===void 0&&(f.dataWidth=n),o===void 0&&(f.dataHeight=i),s===void 0&&(f.left=0),u===void 0&&(f.top=0),f.left+n>f.dataWidth||f.top+i>f.dataHeight)throw new k("Crop rectangle does not fit within image data.");return f}return e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new k("Requested row is outside the image: "+t);var i=this.getWidth();(n==null||n.length<i)&&(n=new Uint8ClampedArray(i));var a=(t+this.top)*this.dataWidth+this.left;return ue.arraycopy(this.luminances,a,n,0,i),n},e.prototype.getMatrix=function(){var t=this.getWidth(),n=this.getHeight();if(t===this.dataWidth&&n===this.dataHeight)return this.luminances;var i=t*n,a=new Uint8ClampedArray(i),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return ue.arraycopy(this.luminances,o,a,0,i),a;for(var s=0;s<n;s++){var u=s*t;ue.arraycopy(this.luminances,o,a,u,t),o+=this.dataWidth}return a},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return new e(this.luminances,i,a,this.dataWidth,this.dataHeight,this.left+t,this.top+n)},e.prototype.invert=function(){return new ir(this)},e})(Ht);var Ts=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),bs=function(r){Ts(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.forName=function(t){return this.getCharacterSetECIByName(t)},e}(me),Ds=function(){function r(){}return r.ISO_8859_1=me.ISO8859_1,r}(),Or,Rs=301,Ns=function(r,e){for(var t=1,n=0;n<255;n++)e[n]=t,r[t]=n,t*=2,t>=256&&(t^=Rs);return{LOG:r,ALOG:e}};Or=Ns([],[]),Or.LOG;Or.ALOG;var wn;(function(r){r[r.FORCE_NONE=0]="FORCE_NONE",r[r.FORCE_SQUARE=1]="FORCE_SQUARE",r[r.FORCE_RECTANGLE=2]="FORCE_RECTANGLE"})(wn||(wn={}));var An=129,Xn=230,Ps=231,Ms=235,Bs=236,Fs=237,Ls=238,ks=239,Us=240,hr=254,Vs=254,Cn="[)>05",En="[)>06",mn="",te=0,Ne=1,He=2,Ae=3,Te=4,Le=5,Hs=function(){function r(){}return r.prototype.getEncodingMode=function(){return te},r.prototype.encode=function(e){var t=et.determineConsecutiveDigitCount(e.getMessage(),e.pos);if(t>=2)e.writeCodeword(this.encodeASCIIDigits(e.getMessage().charCodeAt(e.pos),e.getMessage().charCodeAt(e.pos+1))),e.pos+=2;else{var n=e.getCurrentChar(),i=et.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(i!==this.getEncodingMode())switch(i){case Le:e.writeCodeword(Ps),e.signalEncoderChange(Le);return;case Ne:e.writeCodeword(Xn),e.signalEncoderChange(Ne);return;case Ae:e.writeCodeword(Ls),e.signalEncoderChange(Ae);break;case He:e.writeCodeword(ks),e.signalEncoderChange(He);break;case Te:e.writeCodeword(Us),e.signalEncoderChange(Te);break;default:throw new Error("Illegal mode: "+i)}else et.isExtendedASCII(n)?(e.writeCodeword(Ms),e.writeCodeword(n-128+1),e.pos++):(e.writeCodeword(n+1),e.pos++)}},r.prototype.encodeASCIIDigits=function(e,t){if(et.isDigit(e)&&et.isDigit(t)){var n=(e-48)*10+(t-48);return n+130}throw new Error("not digits: "+e+t)},r}(),Gs=function(){function r(){}return r.prototype.getEncodingMode=function(){return Le},r.prototype.encode=function(e){var t=new X;for(t.append(0);e.hasMoreCharacters();){var n=e.getCurrentChar();t.append(n),e.pos++;var i=et.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(i!==this.getEncodingMode()){e.signalEncoderChange(te);break}}var a=t.length()-1,o=1,s=e.getCodewordCount()+a+o;e.updateSymbolInfo(s);var u=e.getSymbolInfo().getDataCapacity()-s>0;if(e.hasMoreCharacters()||u)if(a<=249)t.setCharAt(0,$.getCharAt(a));else if(a<=1555)t.setCharAt(0,$.getCharAt(Math.floor(a/250)+249)),t.insert(1,$.getCharAt(a%250));else throw new Error("Message length not in valid ranges: "+a);for(var f=0,n=t.length();f<n;f++)e.writeCodeword(this.randomize255State(t.charAt(f).charCodeAt(0),e.getCodewordCount()+1))},r.prototype.randomize255State=function(e,t){var n=149*t%255+1,i=e+n;return i<=255?i:i-256},r}(),Br=function(){function r(){}return r.prototype.getEncodingMode=function(){return Ne},r.prototype.encodeMaximal=function(e){for(var t=new X,n=0,i=e.pos,a=0;e.hasMoreCharacters();){var o=e.getCurrentChar();e.pos++,n=this.encodeChar(o,t),t.length()%3===0&&(i=e.pos,a=t.length())}if(a!==t.length()){var s=Math.floor(t.length()/3*2),u=Math.floor(e.getCodewordCount()+s+1);e.updateSymbolInfo(u);var f=e.getSymbolInfo().getDataCapacity()-u,c=Math.floor(t.length()%3);(c===2&&f!==2||c===1&&(n>3||f!==1))&&(e.pos=i)}t.length()>0&&e.writeCodeword(Xn),this.handleEOD(e,t)},r.prototype.encode=function(e){for(var t=new X;e.hasMoreCharacters();){var n=e.getCurrentChar();e.pos++;var i=this.encodeChar(n,t),a=Math.floor(t.length()/3)*2,o=e.getCodewordCount()+a;e.updateSymbolInfo(o);var s=e.getSymbolInfo().getDataCapacity()-o;if(!e.hasMoreCharacters()){var u=new X;for(t.length()%3===2&&s!==2&&(i=this.backtrackOneCharacter(e,t,u,i));t.length()%3===1&&(i>3||s!==1);)i=this.backtrackOneCharacter(e,t,u,i);break}var f=t.length();if(f%3===0){var c=et.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(c!==this.getEncodingMode()){e.signalEncoderChange(te);break}}}this.handleEOD(e,t)},r.prototype.backtrackOneCharacter=function(e,t,n,i){var a=t.length(),o=t.toString().substring(0,a-i);t.setLengthToZero(),t.append(o),e.pos--;var s=e.getCurrentChar();return i=this.encodeChar(s,n),e.resetSymbolInfo(),i},r.prototype.writeNextTriplet=function(e,t){e.writeCodewords(this.encodeToCodewords(t.toString()));var n=t.toString().substring(3);t.setLengthToZero(),t.append(n)},r.prototype.handleEOD=function(e,t){var n=Math.floor(t.length()/3*2),i=t.length()%3,a=e.getCodewordCount()+n;e.updateSymbolInfo(a);var o=e.getSymbolInfo().getDataCapacity()-a;if(i===2){for(t.append("\0");t.length()>=3;)this.writeNextTriplet(e,t);e.hasMoreCharacters()&&e.writeCodeword(hr)}else if(o===1&&i===1){for(;t.length()>=3;)this.writeNextTriplet(e,t);e.hasMoreCharacters()&&e.writeCodeword(hr),e.pos--}else if(i===0){for(;t.length()>=3;)this.writeNextTriplet(e,t);(o>0||e.hasMoreCharacters())&&e.writeCodeword(hr)}else throw new Error("Unexpected case. Please report!");e.signalEncoderChange(te)},r.prototype.encodeChar=function(e,t){if(e===32)return t.append(3),1;if(e>=48&&e<=57)return t.append(e-48+4),1;if(e>=65&&e<=90)return t.append(e-65+14),1;if(e<32)return t.append(0),t.append(e),2;if(e<=47)return t.append(1),t.append(e-33),2;if(e<=64)return t.append(1),t.append(e-58+15),2;if(e<=95)return t.append(1),t.append(e-91+22),2;if(e<=127)return t.append(2),t.append(e-96),2;t.append("1");var n=2;return n+=this.encodeChar(e-128,t),n},r.prototype.encodeToCodewords=function(e){var t=1600*e.charCodeAt(0)+40*e.charCodeAt(1)+e.charCodeAt(2)+1,n=t/256,i=t%256,a=new X;return a.append(n),a.append(i),a.toString()},r}(),Xs=function(){function r(){}return r.prototype.getEncodingMode=function(){return Te},r.prototype.encode=function(e){for(var t=new X;e.hasMoreCharacters();){var n=e.getCurrentChar();this.encodeChar(n,t),e.pos++;var i=t.length();if(i>=4){e.writeCodewords(this.encodeToCodewords(t.toString()));var a=t.toString().substring(4);t.setLengthToZero(),t.append(a);var o=et.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(o!==this.getEncodingMode()){e.signalEncoderChange(te);break}}}t.append($.getCharAt(31)),this.handleEOD(e,t)},r.prototype.handleEOD=function(e,t){try{var n=t.length();if(n===0)return;if(n===1){e.updateSymbolInfo();var i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount(),a=e.getRemainingCharacters();if(a>i&&(e.updateSymbolInfo(e.getCodewordCount()+1),i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount()),a<=i&&i<=2)return}if(n>4)throw new Error("Count must not exceed 4");var o=n-1,s=this.encodeToCodewords(t.toString()),u=!e.hasMoreCharacters(),f=u&&o<=2;if(o<=2){e.updateSymbolInfo(e.getCodewordCount()+o);var i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount();i>=3&&(f=!1,e.updateSymbolInfo(e.getCodewordCount()+s.length))}f?(e.resetSymbolInfo(),e.pos-=o):e.writeCodewords(s)}finally{e.signalEncoderChange(te)}},r.prototype.encodeChar=function(e,t){e>=32&&e<=63?t.append(e):e>=64&&e<=94?t.append($.getCharAt(e-64)):et.illegalCharacter($.getCharAt(e))},r.prototype.encodeToCodewords=function(e){var t=e.length;if(t===0)throw new Error("StringBuilder must not be empty");var n=e.charAt(0).charCodeAt(0),i=t>=2?e.charAt(1).charCodeAt(0):0,a=t>=3?e.charAt(2).charCodeAt(0):0,o=t>=4?e.charAt(3).charCodeAt(0):0,s=(n<<18)+(i<<12)+(a<<6)+o,u=s>>16&255,f=s>>8&255,c=s&255,l=new X;return l.append(u),t>=2&&l.append(f),t>=3&&l.append(c),l.toString()},r}(),Ws=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),zs=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},q=function(){function r(e,t,n,i,a,o,s,u){s===void 0&&(s=0),u===void 0&&(u=0),this.rectangular=e,this.dataCapacity=t,this.errorCodewords=n,this.matrixWidth=i,this.matrixHeight=a,this.dataRegions=o,this.rsBlockData=s,this.rsBlockError=u}return r.lookup=function(e,t,n,i,a){var o,s;t===void 0&&(t=0),n===void 0&&(n=null),i===void 0&&(i=null),a===void 0&&(a=!0);try{for(var u=zs(js),f=u.next();!f.done;f=u.next()){var c=f.value;if(!(t===1&&c.rectangular)&&!(t===2&&!c.rectangular)&&!(n!=null&&(c.getSymbolWidth()<n.getWidth()||c.getSymbolHeight()<n.getHeight()))&&!(i!=null&&(c.getSymbolWidth()>i.getWidth()||c.getSymbolHeight()>i.getHeight()))&&e<=c.dataCapacity)return c}}catch(l){o={error:l}}finally{try{f&&!f.done&&(s=u.return)&&s.call(u)}finally{if(o)throw o.error}}if(a)throw new Error("Can't find a symbol arrangement that matches the message. Data codewords: "+e);return null},r.prototype.getHorizontalDataRegions=function(){switch(this.dataRegions){case 1:return 1;case 2:case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},r.prototype.getVerticalDataRegions=function(){switch(this.dataRegions){case 1:case 2:return 1;case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},r.prototype.getSymbolDataWidth=function(){return this.getHorizontalDataRegions()*this.matrixWidth},r.prototype.getSymbolDataHeight=function(){return this.getVerticalDataRegions()*this.matrixHeight},r.prototype.getSymbolWidth=function(){return this.getSymbolDataWidth()+this.getHorizontalDataRegions()*2},r.prototype.getSymbolHeight=function(){return this.getSymbolDataHeight()+this.getVerticalDataRegions()*2},r.prototype.getCodewordCount=function(){return this.dataCapacity+this.errorCodewords},r.prototype.getInterleavedBlockCount=function(){return this.rsBlockData?this.dataCapacity/this.rsBlockData:1},r.prototype.getDataCapacity=function(){return this.dataCapacity},r.prototype.getErrorCodewords=function(){return this.errorCodewords},r.prototype.getDataLengthForInterleavedBlock=function(e){return this.rsBlockData},r.prototype.getErrorLengthForInterleavedBlock=function(e){return this.rsBlockError},r}(),$s=function(r){Ws(e,r);function e(){return r.call(this,!1,1558,620,22,22,36,-1,62)||this}return e.prototype.getInterleavedBlockCount=function(){return 10},e.prototype.getDataLengthForInterleavedBlock=function(t){return t<=8?156:155},e}(q),js=[new q(!1,3,5,8,8,1),new q(!1,5,7,10,10,1),new q(!0,5,7,16,6,1),new q(!1,8,10,12,12,1),new q(!0,10,11,14,6,2),new q(!1,12,12,14,14,1),new q(!0,16,14,24,10,1),new q(!1,18,14,16,16,1),new q(!1,22,18,18,18,1),new q(!0,22,18,16,10,2),new q(!1,30,20,20,20,1),new q(!0,32,24,16,14,2),new q(!1,36,24,22,22,1),new q(!1,44,28,24,24,1),new q(!0,49,28,22,14,2),new q(!1,62,36,14,14,4),new q(!1,86,42,16,16,4),new q(!1,114,48,18,18,4),new q(!1,144,56,20,20,4),new q(!1,174,68,22,22,4),new q(!1,204,84,24,24,4,102,42),new q(!1,280,112,14,14,16,140,56),new q(!1,368,144,16,16,16,92,36),new q(!1,456,192,18,18,16,114,48),new q(!1,576,224,20,20,16,144,56),new q(!1,696,272,22,22,16,174,68),new q(!1,816,336,24,24,16,136,56),new q(!1,1050,408,18,18,36,175,68),new q(!1,1304,496,20,20,36,163,62),new $s],Ys=function(){function r(e){this.msg=e,this.pos=0,this.skipAtEnd=0;for(var t=e.split("").map(function(s){return s.charCodeAt(0)}),n=new X,i=0,a=t.length;i<a;i++){var o=String.fromCharCode(t[i]&255);if(o==="?"&&e.charAt(i)!=="?")throw new Error("Message contains characters outside ISO-8859-1 encoding.");n.append(o)}this.msg=n.toString(),this.shape=0,this.codewords=new X,this.newEncoding=-1}return r.prototype.setSymbolShape=function(e){this.shape=e},r.prototype.setSizeConstraints=function(e,t){this.minSize=e,this.maxSize=t},r.prototype.getMessage=function(){return this.msg},r.prototype.setSkipAtEnd=function(e){this.skipAtEnd=e},r.prototype.getCurrentChar=function(){return this.msg.charCodeAt(this.pos)},r.prototype.getCurrent=function(){return this.msg.charCodeAt(this.pos)},r.prototype.getCodewords=function(){return this.codewords},r.prototype.writeCodewords=function(e){this.codewords.append(e)},r.prototype.writeCodeword=function(e){this.codewords.append(e)},r.prototype.getCodewordCount=function(){return this.codewords.length()},r.prototype.getNewEncoding=function(){return this.newEncoding},r.prototype.signalEncoderChange=function(e){this.newEncoding=e},r.prototype.resetEncoderSignal=function(){this.newEncoding=-1},r.prototype.hasMoreCharacters=function(){return this.pos<this.getTotalMessageCharCount()},r.prototype.getTotalMessageCharCount=function(){return this.msg.length-this.skipAtEnd},r.prototype.getRemainingCharacters=function(){return this.getTotalMessageCharCount()-this.pos},r.prototype.getSymbolInfo=function(){return this.symbolInfo},r.prototype.updateSymbolInfo=function(e){e===void 0&&(e=this.getCodewordCount()),(this.symbolInfo==null||e>this.symbolInfo.getDataCapacity())&&(this.symbolInfo=q.lookup(e,this.shape,this.minSize,this.maxSize,!0))},r.prototype.resetSymbolInfo=function(){this.symbolInfo=null},r}(),Zs=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ks=function(r){Zs(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getEncodingMode=function(){return Ae},e.prototype.encode=function(t){for(var n=new X;t.hasMoreCharacters();){var i=t.getCurrentChar();t.pos++,this.encodeChar(i,n);var a=n.length();if(a%3===0){this.writeNextTriplet(t,n);var o=et.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode());if(o!==this.getEncodingMode()){t.signalEncoderChange(te);break}}}this.handleEOD(t,n)},e.prototype.encodeChar=function(t,n){switch(t){case 13:n.append(0);break;case 42:n.append(1);break;case 62:n.append(2);break;case 32:n.append(3);break;default:t>=48&&t<=57?n.append(t-48+4):t>=65&&t<=90?n.append(t-65+14):et.illegalCharacter($.getCharAt(t));break}return 1},e.prototype.handleEOD=function(t,n){t.updateSymbolInfo();var i=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),a=n.length();t.pos-=a,(t.getRemainingCharacters()>1||i>1||t.getRemainingCharacters()!==i)&&t.writeCodeword(Vs),t.getNewEncoding()<0&&t.signalEncoderChange(te)},e}(Br),qs=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Qs=function(r){qs(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getEncodingMode=function(){return He},e.prototype.encodeChar=function(t,n){if(t===32)return n.append(3),1;if(t>=48&&t<=57)return n.append(t-48+4),1;if(t>=97&&t<=122)return n.append(t-97+14),1;if(t<32)return n.append(0),n.append(t),2;if(t<=47)return n.append(1),n.append(t-33),2;if(t<=64)return n.append(1),n.append(t-58+15),2;if(t>=91&&t<=95)return n.append(1),n.append(t-91+22),2;if(t===96)return n.append(2),n.append(0),2;if(t<=90)return n.append(2),n.append(t-65+1),2;if(t<=127)return n.append(2),n.append(t-123+27),2;n.append("1");var i=2;return i+=this.encodeChar(t-128,n),i},e}(Br),et=function(){function r(){}return r.randomize253State=function(e){var t=149*e%253+1,n=An+t;return n<=254?n:n-254},r.encodeHighLevel=function(e,t,n,i,a){t===void 0&&(t=0),n===void 0&&(n=null),i===void 0&&(i=null),a===void 0&&(a=!1);var o=new Br,s=[new Hs,o,new Qs,new Ks,new Xs,new Gs],u=new Ys(e);u.setSymbolShape(t),u.setSizeConstraints(n,i),e.startsWith(Cn)&&e.endsWith(mn)?(u.writeCodeword(Bs),u.setSkipAtEnd(2),u.pos+=Cn.length):e.startsWith(En)&&e.endsWith(mn)&&(u.writeCodeword(Fs),u.setSkipAtEnd(2),u.pos+=En.length);var f=te;for(a&&(o.encodeMaximal(u),f=u.getNewEncoding(),u.resetEncoderSignal());u.hasMoreCharacters();)s[f].encode(u),u.getNewEncoding()>=0&&(f=u.getNewEncoding(),u.resetEncoderSignal());var c=u.getCodewordCount();u.updateSymbolInfo();var l=u.getSymbolInfo().getDataCapacity();c<l&&f!==te&&f!==Le&&f!==Te&&u.writeCodeword("þ");var h=u.getCodewords();for(h.length()<l&&h.append(An);h.length()<l;)h.append(this.randomize253State(h.length()+1));return u.getCodewords().toString()},r.lookAheadTest=function(e,t,n){var i=this.lookAheadTestIntern(e,t,n);if(n===Ae&&i===Ae){for(var a=Math.min(t+3,e.length),o=t;o<a;o++)if(!this.isNativeX12(e.charCodeAt(o)))return te}else if(n===Te&&i===Te){for(var a=Math.min(t+4,e.length),o=t;o<a;o++)if(!this.isNativeEDIFACT(e.charCodeAt(o)))return te}return i},r.lookAheadTestIntern=function(e,t,n){if(t>=e.length)return n;var i;n===te?i=[0,1,1,1,1,1.25]:(i=[1,2,2,2,2,2.25],i[n]=0);for(var a=0,o=new Uint8Array(6),s=[];;){if(t+a===e.length){Se.fill(o,0),Se.fill(s,0);var u=this.findMinimums(i,s,z.MAX_VALUE,o),f=this.getMinimumCount(o);if(s[te]===u)return te;if(f===1){if(o[Le]>0)return Le;if(o[Te]>0)return Te;if(o[He]>0)return He;if(o[Ae]>0)return Ae}return Ne}var c=e.charCodeAt(t+a);if(a++,this.isDigit(c)?i[te]+=.5:this.isExtendedASCII(c)?(i[te]=Math.ceil(i[te]),i[te]+=2):(i[te]=Math.ceil(i[te]),i[te]++),this.isNativeC40(c)?i[Ne]+=2/3:this.isExtendedASCII(c)?i[Ne]+=8/3:i[Ne]+=4/3,this.isNativeText(c)?i[He]+=2/3:this.isExtendedASCII(c)?i[He]+=8/3:i[He]+=4/3,this.isNativeX12(c)?i[Ae]+=2/3:this.isExtendedASCII(c)?i[Ae]+=13/3:i[Ae]+=10/3,this.isNativeEDIFACT(c)?i[Te]+=3/4:this.isExtendedASCII(c)?i[Te]+=17/4:i[Te]+=13/4,this.isSpecialB256(c)?i[Le]+=4:i[Le]++,a>=4){if(Se.fill(o,0),Se.fill(s,0),this.findMinimums(i,s,z.MAX_VALUE,o),s[te]<this.min(s[Le],s[Ne],s[He],s[Ae],s[Te]))return te;if(s[Le]<s[te]||s[Le]+1<this.min(s[Ne],s[He],s[Ae],s[Te]))return Le;if(s[Te]+1<this.min(s[Le],s[Ne],s[He],s[Ae],s[te]))return Te;if(s[He]+1<this.min(s[Le],s[Ne],s[Te],s[Ae],s[te]))return He;if(s[Ae]+1<this.min(s[Le],s[Ne],s[Te],s[He],s[te]))return Ae;if(s[Ne]+1<this.min(s[te],s[Le],s[Te],s[He])){if(s[Ne]<s[Ae])return Ne;if(s[Ne]===s[Ae]){for(var l=t+a+1;l<e.length;){var h=e.charCodeAt(l);if(this.isX12TermSep(h))return Ae;if(!this.isNativeX12(h))break;l++}return Ne}}}}},r.min=function(e,t,n,i,a){var o=Math.min(e,Math.min(t,Math.min(n,i)));return a===void 0?o:Math.min(o,a)},r.findMinimums=function(e,t,n,i){for(var a=0;a<6;a++){var o=t[a]=Math.ceil(e[a]);n>o&&(n=o,Se.fill(i,0)),n===o&&(i[a]=i[a]+1)}return n},r.getMinimumCount=function(e){for(var t=0,n=0;n<6;n++)t+=e[n];return t||0},r.isDigit=function(e){return e>=48&&e<=57},r.isExtendedASCII=function(e){return e>=128&&e<=255},r.isNativeC40=function(e){return e===32||e>=48&&e<=57||e>=65&&e<=90},r.isNativeText=function(e){return e===32||e>=48&&e<=57||e>=97&&e<=122},r.isNativeX12=function(e){return this.isX12TermSep(e)||e===32||e>=48&&e<=57||e>=65&&e<=90},r.isX12TermSep=function(e){return e===13||e===42||e===62},r.isNativeEDIFACT=function(e){return e>=32&&e<=94},r.isSpecialB256=function(e){return!1},r.determineConsecutiveDigitCount=function(e,t){t===void 0&&(t=0);for(var n=e.length,i=t;i<n&&this.isDigit(e.charCodeAt(i));)i++;return i-t},r.illegalCharacter=function(e){var t=z.toHexString(e.charCodeAt(0));throw t="0000".substring(0,4-t.length)+t,new Error("Illegal character: "+e+" (0x"+t+")")},r}(),vr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Sn=function(){function r(e){this.charset=e,this.name=e.name}return r.prototype.canEncode=function(e){try{return qe.encode(e,this.charset)!=null}catch{return!1}},r}(),Js=function(){function r(e,t,n){var i,a,o,s,u,f;this.ENCODERS=["IBM437","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-9","ISO-8859-10","ISO-8859-11","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","windows-1250","windows-1251","windows-1252","windows-1256","Shift_JIS"].map(function(D){return new Sn(bs.forName(D))}),this.encoders=[];var c=[];c.push(new Sn(Ds.ISO_8859_1));for(var l=t!=null&&t.name.startsWith("UTF"),h=0;h<e.length;h++){var d=!1;try{for(var v=(i=void 0,vr(c)),g=v.next();!g.done;g=v.next()){var y=g.value,_=e.charAt(h),x=_.charCodeAt(0);if(x===n||y.canEncode(_)){d=!0;break}}}catch(D){i={error:D}}finally{try{g&&!g.done&&(a=v.return)&&a.call(v)}finally{if(i)throw i.error}}if(!d)try{for(var A=(o=void 0,vr(this.ENCODERS)),m=A.next();!m.done;m=A.next()){var y=m.value;if(y.canEncode(e.charAt(h))){c.push(y),d=!0;break}}}catch(D){o={error:D}}finally{try{m&&!m.done&&(s=A.return)&&s.call(A)}finally{if(o)throw o.error}}d||(l=!0)}if(c.length===1&&!l)this.encoders=[c[0]];else{this.encoders=[];var S=0;try{for(var O=vr(c),b=O.next();!b.done;b=O.next()){var y=b.value;this.encoders[S++]=y}}catch(D){u={error:D}}finally{try{b&&!b.done&&(f=O.return)&&f.call(O)}finally{if(u)throw u.error}}}var T=-1;if(t!=null){for(var h=0;h<this.encoders.length;h++)if(this.encoders[h]!=null&&t.name===this.encoders[h].name){T=h;break}}this.priorityEncoderIndex=T}return r.prototype.length=function(){return this.encoders.length},r.prototype.getCharsetName=function(e){if(!(e<this.length()))throw new Error("index must be less than length");return this.encoders[e].name},r.prototype.getCharset=function(e){if(!(e<this.length()))throw new Error("index must be less than length");return this.encoders[e].charset},r.prototype.getECIValue=function(e){return this.encoders[e].charset.getValueIdentifier()},r.prototype.getPriorityEncoderIndex=function(){return this.priorityEncoderIndex},r.prototype.canEncode=function(e,t){if(!(t<this.length()))throw new Error("index must be less than length");return!0},r.prototype.encode=function(e,t){if(!(t<this.length()))throw new Error("index must be less than length");return qe.encode($.getCharAt(e),this.encoders[t].name)},r}(),e1=3,t1=function(){function r(e,t,n){this.fnc1=n;var i=new Js(e,t,n);if(i.length()===1)for(var a=0;a<this.bytes.length;a++){var o=e.charAt(a).charCodeAt(0);this.bytes[a]=o===n?1e3:o}else this.bytes=this.encodeMinimally(e,i,n)}return r.prototype.getFNC1Character=function(){return this.fnc1},r.prototype.length=function(){return this.bytes.length},r.prototype.haveNCharacters=function(e,t){if(e+t-1>=this.bytes.length)return!1;for(var n=0;n<t;n++)if(this.isECI(e+n))return!1;return!0},r.prototype.charAt=function(e){if(e<0||e>=this.length())throw new Error(""+e);if(this.isECI(e))throw new Error("value at "+e+" is not a character but an ECI");return this.isFNC1(e)?this.fnc1:this.bytes[e]},r.prototype.subSequence=function(e,t){if(e<0||e>t||t>this.length())throw new Error(""+e);for(var n=new X,i=e;i<t;i++){if(this.isECI(i))throw new Error("value at "+i+" is not a character but an ECI");n.append(this.charAt(i))}return n.toString()},r.prototype.isECI=function(e){if(e<0||e>=this.length())throw new Error(""+e);return this.bytes[e]>255&&this.bytes[e]<=999},r.prototype.isFNC1=function(e){if(e<0||e>=this.length())throw new Error(""+e);return this.bytes[e]===1e3},r.prototype.getECIValue=function(e){if(e<0||e>=this.length())throw new Error(""+e);if(!this.isECI(e))throw new Error("value at "+e+" is not an ECI but a character");return this.bytes[e]-256},r.prototype.addEdge=function(e,t,n){(e[t][n.encoderIndex]==null||e[t][n.encoderIndex].cachedTotalSize>n.cachedTotalSize)&&(e[t][n.encoderIndex]=n)},r.prototype.addEdges=function(e,t,n,i,a,o){var s=e.charAt(i).charCodeAt(0),u=0,f=t.length();t.getPriorityEncoderIndex()>=0&&(s===o||t.canEncode(s,t.getPriorityEncoderIndex()))&&(u=t.getPriorityEncoderIndex(),f=u+1);for(var c=u;c<f;c++)(s===o||t.canEncode(s,c))&&this.addEdge(n,i+1,new In(s,t,c,a,o))},r.prototype.encodeMinimally=function(e,t,n){var i=e.length,a=new In[i+1][t.length()];this.addEdges(e,t,a,0,null,n);for(var o=1;o<=i;o++){for(var s=0;s<t.length();s++)a[o][s]!=null&&o<i&&this.addEdges(e,t,a,o,a[o][s],n);for(var s=0;s<t.length();s++)a[o-1][s]=null}for(var u=-1,f=z.MAX_VALUE,s=0;s<t.length();s++)if(a[i][s]!=null){var c=a[i][s];c.cachedTotalSize<f&&(f=c.cachedTotalSize,u=s)}if(u<0)throw new Error('Failed to encode "'+e+'"');for(var l=[],h=a[i][u];h!=null;){if(h.isFNC1())l.unshift(1e3);else for(var d=t.encode(h.c,h.encoderIndex),o=d.length-1;o>=0;o--)l.unshift(d[o]&255);var v=h.previous===null?0:h.previous.encoderIndex;v!==h.encoderIndex&&l.unshift(256+t.getECIValue(h.encoderIndex)),h=h.previous}for(var g=[],o=0;o<g.length;o++)g[o]=l[o];return g},r}(),In=function(){function r(e,t,n,i,a){this.c=e,this.encoderSet=t,this.encoderIndex=n,this.previous=i,this.fnc1=a,this.c=e===a?1e3:e;var o=this.isFNC1()?1:t.encode(e,n).length,s=i===null?0:i.encoderIndex;s!==n&&(o+=e1),i!=null&&(o+=i.cachedTotalSize),this.cachedTotalSize=o}return r.prototype.isFNC1=function(){return this.c===1e3},r}(),r1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();var On;(function(r){r[r.ASCII=0]="ASCII",r[r.C40=1]="C40",r[r.TEXT=2]="TEXT",r[r.X12=3]="X12",r[r.EDF=4]="EDF",r[r.B256=5]="B256"})(On||(On={}));(function(r){r1(e,r);function e(t,n,i,a,o){var s=r.call(this,t,n,i)||this;return s.shape=a,s.macroId=o,s}return e.prototype.getMacroId=function(){return this.macroId},e.prototype.getShapeHint=function(){return this.shape},e})(t1);var n1=function(){function r(){}return r.prototype.isCompact=function(){return this.compact},r.prototype.setCompact=function(e){this.compact=e},r.prototype.getSize=function(){return this.size},r.prototype.setSize=function(e){this.size=e},r.prototype.getLayers=function(){return this.layers},r.prototype.setLayers=function(e){this.layers=e},r.prototype.getCodeWords=function(){return this.codeWords},r.prototype.setCodeWords=function(e){this.codeWords=e},r.prototype.getMatrix=function(){return this.matrix},r.prototype.setMatrix=function(e){this.matrix=e},r}(),Tn=function(){function r(){}return r.singletonList=function(e){return[e]},r.min=function(e,t){return e.sort(t)[0]},r}(),i1=function(){function r(e){this.previous=e}return r.prototype.getPrevious=function(){return this.previous},r}(),a1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Fr=function(r){a1(e,r);function e(t,n,i){var a=r.call(this,t)||this;return a.value=n,a.bitCount=i,a}return e.prototype.appendTo=function(t,n){t.appendBits(this.value,this.bitCount)},e.prototype.add=function(t,n){return new e(this,t,n)},e.prototype.addBinaryShift=function(t,n){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new e(this,t,n)},e.prototype.toString=function(){var t=this.value&(1<<this.bitCount)-1;return t|=1<<this.bitCount,"<"+z.toBinaryString(t|1<<this.bitCount).substring(1)+">"},e}(i1),o1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),s1=function(r){o1(e,r);function e(t,n,i){var a=r.call(this,t,0,0)||this;return a.binaryShiftStart=n,a.binaryShiftByteCount=i,a}return e.prototype.appendTo=function(t,n){for(var i=0;i<this.binaryShiftByteCount;i++)(i===0||i===31&&this.binaryShiftByteCount<=62)&&(t.appendBits(31,5),this.binaryShiftByteCount>62?t.appendBits(this.binaryShiftByteCount-31,16):i===0?t.appendBits(Math.min(this.binaryShiftByteCount,31),5):t.appendBits(this.binaryShiftByteCount-31,5)),t.appendBits(n[this.binaryShiftStart+i],8)},e.prototype.addBinaryShift=function(t,n){return new e(this,t,n)},e.prototype.toString=function(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"},e}(Fr);function u1(r,e,t){return new s1(r,e,t)}function Lt(r,e,t){return new Fr(r,e,t)}var f1=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],ct=0,nr=1,Ke=2,Wn=3,nt=4,c1=new Fr(null,0,0),pr=[Int32Array.from([0,(5<<16)+28,(5<<16)+30,(5<<16)+29,656318]),Int32Array.from([(9<<16)+480+14,0,(5<<16)+30,(5<<16)+29,656318]),Int32Array.from([(4<<16)+14,(9<<16)+448+28,0,(9<<16)+448+29,932798]),Int32Array.from([(5<<16)+29,(5<<16)+28,656318,0,(5<<16)+30]),Int32Array.from([(5<<16)+31,656380,656382,656381,0])],l1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};function d1(r){var e,t;try{for(var n=l1(r),i=n.next();!i.done;i=n.next()){var a=i.value;Se.fill(a,-1)}}catch(o){e={error:o}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return r[ct][nt]=0,r[nr][nt]=0,r[nr][ct]=28,r[Wn][nt]=0,r[Ke][nt]=0,r[Ke][ct]=15,r}var zn=d1(Se.createInt32Array(6,6)),h1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},v1=function(){function r(e,t,n,i){this.token=e,this.mode=t,this.binaryShiftByteCount=n,this.bitCount=i}return r.prototype.getMode=function(){return this.mode},r.prototype.getToken=function(){return this.token},r.prototype.getBinaryShiftByteCount=function(){return this.binaryShiftByteCount},r.prototype.getBitCount=function(){return this.bitCount},r.prototype.latchAndAppend=function(e,t){var n=this.bitCount,i=this.token;if(e!==this.mode){var a=pr[this.mode][e];i=Lt(i,a&65535,a>>16),n+=a>>16}var o=e===Ke?4:5;return i=Lt(i,t,o),new r(i,e,0,n+o)},r.prototype.shiftAndAppend=function(e,t){var n=this.token,i=this.mode===Ke?4:5;return n=Lt(n,zn[this.mode][e],i),n=Lt(n,t,5),new r(n,this.mode,0,this.bitCount+i+5)},r.prototype.addBinaryShiftChar=function(e){var t=this.token,n=this.mode,i=this.bitCount;if(this.mode===nt||this.mode===Ke){var a=pr[n][ct];t=Lt(t,a&65535,a>>16),i+=a>>16,n=ct}var o=this.binaryShiftByteCount===0||this.binaryShiftByteCount===31?18:this.binaryShiftByteCount===62?9:8,s=new r(t,n,this.binaryShiftByteCount+1,i+o);return s.binaryShiftByteCount===2078&&(s=s.endBinaryShift(e+1)),s},r.prototype.endBinaryShift=function(e){if(this.binaryShiftByteCount===0)return this;var t=this.token;return t=u1(t,e-this.binaryShiftByteCount,this.binaryShiftByteCount),new r(t,this.mode,0,this.bitCount)},r.prototype.isBetterThanOrEqualTo=function(e){var t=this.bitCount+(pr[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?t+=r.calculateBinaryShiftCost(e)-r.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(t+=10),t<=e.bitCount},r.prototype.toBitArray=function(e){for(var t,n,i=[],a=this.endBinaryShift(e.length).token;a!==null;a=a.getPrevious())i.unshift(a);var o=new be;try{for(var s=h1(i),u=s.next();!u.done;u=s.next()){var f=u.value;f.appendTo(o,e)}}catch(c){t={error:c}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return o},r.prototype.toString=function(){return $.format("%s bits=%d bytes=%d",f1[this.mode],this.bitCount,this.binaryShiftByteCount)},r.calculateBinaryShiftCost=function(e){return e.binaryShiftByteCount>62?21:e.binaryShiftByteCount>31?20:e.binaryShiftByteCount>0?10:0},r.INITIAL_STATE=new r(c1,ct,0,0),r}();function p1(r){var e=$.getCharCode(" "),t=$.getCharCode("."),n=$.getCharCode(",");r[ct][e]=1;for(var i=$.getCharCode("Z"),a=$.getCharCode("A"),o=a;o<=i;o++)r[ct][o]=o-a+2;r[nr][e]=1;for(var s=$.getCharCode("z"),u=$.getCharCode("a"),o=u;o<=s;o++)r[nr][o]=o-u+2;r[Ke][e]=1;for(var f=$.getCharCode("9"),c=$.getCharCode("0"),o=c;o<=f;o++)r[Ke][o]=o-c+2;r[Ke][n]=12,r[Ke][t]=13;for(var l=["\0"," ","","","","","","","\x07","\b","	",`
`,"\v","\f","\r","\x1B","","","","","@","\\","^","_","`","|","~",""],h=0;h<l.length;h++)r[Wn][$.getCharCode(l[h])]=h;for(var d=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"],h=0;h<d.length;h++)$.getCharCode(d[h])>0&&(r[nt][$.getCharCode(d[h])]=h);return r}var gr=p1(Se.createInt32Array(5,256)),Yt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g1=function(){function r(e){this.text=e}return r.prototype.encode=function(){for(var e=$.getCharCode(" "),t=$.getCharCode(`
`),n=Tn.singletonList(v1.INITIAL_STATE),i=0;i<this.text.length;i++){var a=void 0,o=i+1<this.text.length?this.text[i+1]:0;switch(this.text[i]){case $.getCharCode("\r"):a=o===t?2:0;break;case $.getCharCode("."):a=o===e?3:0;break;case $.getCharCode(","):a=o===e?4:0;break;case $.getCharCode(":"):a=o===e?5:0;break;default:a=0}a>0?(n=r.updateStateListForPair(n,i,a),i++):n=this.updateStateListForChar(n,i)}var s=Tn.min(n,function(u,f){return u.getBitCount()-f.getBitCount()});return s.toBitArray(this.text)},r.prototype.updateStateListForChar=function(e,t){var n,i,a=[];try{for(var o=Yt(e),s=o.next();!s.done;s=o.next()){var u=s.value;this.updateStateForChar(u,t,a)}}catch(f){n={error:f}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}return r.simplifyStates(a)},r.prototype.updateStateForChar=function(e,t,n){for(var i=this.text[t]&255,a=gr[e.getMode()][i]>0,o=null,s=0;s<=nt;s++){var u=gr[s][i];if(u>0){if(o==null&&(o=e.endBinaryShift(t)),!a||s===e.getMode()||s===Ke){var f=o.latchAndAppend(s,u);n.push(f)}if(!a&&zn[e.getMode()][s]>=0){var c=o.shiftAndAppend(s,u);n.push(c)}}}if(e.getBinaryShiftByteCount()>0||gr[e.getMode()][i]===0){var l=e.addBinaryShiftChar(t);n.push(l)}},r.updateStateListForPair=function(e,t,n){var i,a,o=[];try{for(var s=Yt(e),u=s.next();!u.done;u=s.next()){var f=u.value;this.updateStateForPair(f,t,n,o)}}catch(c){i={error:c}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}return this.simplifyStates(o)},r.updateStateForPair=function(e,t,n,i){var a=e.endBinaryShift(t);if(i.push(a.latchAndAppend(nt,n)),e.getMode()!==nt&&i.push(a.shiftAndAppend(nt,n)),n===3||n===4){var o=a.latchAndAppend(Ke,16-n).latchAndAppend(Ke,1);i.push(o)}if(e.getBinaryShiftByteCount()>0){var s=e.addBinaryShiftChar(t).addBinaryShiftChar(t+1);i.push(s)}},r.simplifyStates=function(e){var t,n,i,a,o=[];try{for(var s=Yt(e),u=s.next();!u.done;u=s.next()){var f=u.value,c=!0,l=function(y){if(y.isBetterThanOrEqualTo(f))return c=!1,"break";f.isBetterThanOrEqualTo(y)&&(o=o.filter(function(_){return _!==y}))};try{for(var h=(i=void 0,Yt(o)),d=h.next();!d.done;d=h.next()){var v=d.value,g=l(v);if(g==="break")break}}catch(y){i={error:y}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}c&&o.push(f)}}catch(y){t={error:y}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return o},r}(),x1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};(function(){function r(){}return r.encodeBytes=function(e){return r.encode(e,r.DEFAULT_EC_PERCENT,r.DEFAULT_AZTEC_LAYERS)},r.encode=function(e,t,n){var i=new g1(e).encode(),a=z.truncDivision(i.getSize()*t,100)+11,o=i.getSize()+a,s,u,f,c,l;if(n!==r.DEFAULT_AZTEC_LAYERS){if(s=n<0,u=Math.abs(n),u>(s?r.MAX_NB_BITS_COMPACT:r.MAX_NB_BITS))throw new k($.format("Illegal value %s for layers",n));f=r.totalBitsInLayer(u,s),c=r.WORD_SIZE[u];var h=f-f%c;if(l=r.stuffBits(i,c),l.getSize()+a>h)throw new k("Data to large for user specified layer");if(s&&l.getSize()>c*64)throw new k("Data to large for user specified layer")}else{c=0,l=null;for(var d=0;;d++){if(d>r.MAX_NB_BITS)throw new k("Data too large for an Aztec code");if(s=d<=3,u=s?d+1:d,f=r.totalBitsInLayer(u,s),!(o>f)){(l==null||c!==r.WORD_SIZE[u])&&(c=r.WORD_SIZE[u],l=r.stuffBits(i,c));var h=f-f%c;if(!(s&&l.getSize()>c*64)&&l.getSize()+a<=h)break}}}var v=r.generateCheckWords(l,f,c),g=l.getSize()/c,y=r.generateModeMessage(s,u,g),_=(s?11:14)+u*4,x=new Int32Array(_),A;if(s){A=_;for(var d=0;d<x.length;d++)x[d]=d}else{A=_+1+2*z.truncDivision(z.truncDivision(_,2)-1,15);for(var m=z.truncDivision(_,2),S=z.truncDivision(A,2),d=0;d<m;d++){var O=d+z.truncDivision(d,15);x[m-d-1]=S-O-1,x[m+d]=S+O+1}}for(var b=new it(A),d=0,T=0;d<u;d++){for(var D=(u-d)*4+(s?9:12),P=0;P<D;P++)for(var G=P*2,H=0;H<2;H++)v.get(T+G+H)&&b.set(x[d*2+H],x[d*2+P]),v.get(T+D*2+G+H)&&b.set(x[d*2+P],x[_-1-d*2-H]),v.get(T+D*4+G+H)&&b.set(x[_-1-d*2-H],x[_-1-d*2-P]),v.get(T+D*6+G+H)&&b.set(x[_-1-d*2-P],x[d*2+H]);T+=D*8}if(r.drawModeMessage(b,s,A,y),s)r.drawBullsEye(b,z.truncDivision(A,2),5);else{r.drawBullsEye(b,z.truncDivision(A,2),7);for(var d=0,P=0;d<z.truncDivision(_,2)-1;d+=15,P+=16)for(var H=z.truncDivision(A,2)&1;H<A;H+=2)b.set(z.truncDivision(A,2)-P,H),b.set(z.truncDivision(A,2)+P,H),b.set(H,z.truncDivision(A,2)-P),b.set(H,z.truncDivision(A,2)+P)}var se=new n1;return se.setCompact(s),se.setSize(A),se.setLayers(u),se.setCodeWords(g),se.setMatrix(b),se},r.drawBullsEye=function(e,t,n){for(var i=0;i<n;i+=2)for(var a=t-i;a<=t+i;a++)e.set(a,t-i),e.set(a,t+i),e.set(t-i,a),e.set(t+i,a);e.set(t-n,t-n),e.set(t-n+1,t-n),e.set(t-n,t-n+1),e.set(t+n,t-n),e.set(t+n,t-n+1),e.set(t+n,t+n-1)},r.generateModeMessage=function(e,t,n){var i=new be;return e?(i.appendBits(t-1,2),i.appendBits(n-1,6),i=r.generateCheckWords(i,28,4)):(i.appendBits(t-1,5),i.appendBits(n-1,11),i=r.generateCheckWords(i,40,4)),i},r.drawModeMessage=function(e,t,n,i){var a=z.truncDivision(n,2);if(t)for(var o=0;o<7;o++){var s=a-3+o;i.get(o)&&e.set(s,a-5),i.get(o+7)&&e.set(a+5,s),i.get(20-o)&&e.set(s,a+5),i.get(27-o)&&e.set(a-5,s)}else for(var o=0;o<10;o++){var s=a-5+o+z.truncDivision(o,5);i.get(o)&&e.set(s,a-7),i.get(o+10)&&e.set(a+7,s),i.get(29-o)&&e.set(s,a+7),i.get(39-o)&&e.set(a-7,s)}},r.generateCheckWords=function(e,t,n){var i,a,o=e.getSize()/n,s=new Gn(r.getGF(n)),u=z.truncDivision(t,n),f=r.bitsToWords(e,n,u);s.encode(f,u-o);var c=t%n,l=new be;l.appendBits(0,c);try{for(var h=x1(Array.from(f)),d=h.next();!d.done;d=h.next()){var v=d.value;l.appendBits(v,n)}}catch(g){i={error:g}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}return l},r.bitsToWords=function(e,t,n){var i=new Int32Array(n),a,o;for(a=0,o=e.getSize()/t;a<o;a++){for(var s=0,u=0;u<t;u++)s|=e.get(a*t+u)?1<<t-u-1:0;i[a]=s}return i},r.getGF=function(e){switch(e){case 4:return ze.AZTEC_PARAM;case 6:return ze.AZTEC_DATA_6;case 8:return ze.AZTEC_DATA_8;case 10:return ze.AZTEC_DATA_10;case 12:return ze.AZTEC_DATA_12;default:throw new k("Unsupported word size "+e)}},r.stuffBits=function(e,t){for(var n=new be,i=e.getSize(),a=(1<<t)-2,o=0;o<i;o+=t){for(var s=0,u=0;u<t;u++)(o+u>=i||e.get(o+u))&&(s|=1<<t-1-u);(s&a)===a?(n.appendBits(s&a,t),o--):s&a?n.appendBits(s,t):(n.appendBits(s|1,t),o--)}return n},r.totalBitsInLayer=function(e,t){return((t?88:112)+16*e)*e},r.DEFAULT_EC_PERCENT=33,r.DEFAULT_AZTEC_LAYERS=0,r.MAX_NB_BITS=32,r.MAX_NB_BITS_COMPACT=4,r.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]),r})();var y1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),_1=function(r){y1(e,r);function e(t){var n=r.call(this,t.width,t.height)||this;return n.canvas=t,n.tempCanvasElement=null,n.buffer=e.makeBufferFromCanvasImageData(t),n}return e.makeBufferFromCanvasImageData=function(t){var n;try{n=t.getContext("2d",{willReadFrequently:!0})}catch{n=t.getContext("2d")}if(!n)throw new Error("Couldn't get canvas context.");var i=n.getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(i.data,t.width,t.height)},e.toGrayscaleBuffer=function(t,n,i){for(var a=new Uint8ClampedArray(n*i),o=0,s=0,u=t.length;o<u;o+=4,s++){var f=void 0,c=t[o+3];if(c===0)f=255;else{var l=t[o],h=t[o+1],d=t[o+2];f=306*l+601*h+117*d+512>>10}a[s]=f}return a},e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new k("Requested row is outside the image: "+t);var i=this.getWidth(),a=t*i;return n===null?n=this.buffer.slice(a,a+i):(n.length<i&&(n=new Uint8ClampedArray(i)),n.set(this.buffer.slice(a,a+i))),n},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return r.prototype.crop.call(this,t,n,i,a),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.invert=function(){return new ir(this)},e.prototype.getTempCanvasElement=function(){if(this.tempCanvasElement===null){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement();if(!n)throw new Error("Could not create a Canvas element.");var i=t*e.DEGREE_TO_RADIANS,a=this.canvas.width,o=this.canvas.height,s=Math.ceil(Math.abs(Math.cos(i))*a+Math.abs(Math.sin(i))*o),u=Math.ceil(Math.abs(Math.sin(i))*a+Math.abs(Math.cos(i))*o);n.width=s,n.height=u;var f=n.getContext("2d");if(!f)throw new Error("Could not create a Canvas Context element.");return f.translate(s/2,u/2),f.rotate(i),f.drawImage(this.canvas,a/-2,o/-2),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.DEGREE_TO_RADIANS=Math.PI/180,e}(Ht);function $n(){return typeof navigator<"u"}function w1(){return $n()&&!!navigator.mediaDevices}function A1(){return!!(w1()&&navigator.mediaDevices.enumerateDevices)}var Ot=function(){return Ot=Object.assign||function(r){for(var e,t=1,n=arguments.length;t<n;t++){e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])}return r},Ot.apply(this,arguments)},he=function(r,e,t,n){function i(a){return a instanceof t?a:new t(function(o){o(a)})}return new(t||(t=Promise))(function(a,o){function s(c){try{f(n.next(c))}catch(l){o(l)}}function u(c){try{f(n.throw(c))}catch(l){o(l)}}function f(c){c.done?a(c.value):i(c.value).then(s,u)}f((n=n.apply(r,e||[])).next())})},ve=function(r,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(f){return function(c){return u([f,c])}}function u(f){if(n)throw new TypeError("Generator is already executing.");for(;t;)try{if(n=1,i&&(a=f[0]&2?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[f[0]&2,a.value]),f[0]){case 0:case 1:a=f;break;case 4:return t.label++,{value:f[1],done:!1};case 5:t.label++,i=f[1],f=[0];continue;case 7:f=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(f[0]===6||f[0]===2)){t=0;continue}if(f[0]===3&&(!a||f[1]>a[0]&&f[1]<a[3])){t.label=f[1];break}if(f[0]===6&&t.label<a[1]){t.label=a[1],a=f;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(f);break}a[2]&&t.ops.pop(),t.trys.pop();continue}f=e.call(r,t)}catch(c){f=[6,c],i=0}finally{n=a=0}if(f[0]&5)throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}},xr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},C1={delayBetweenScanAttempts:500,delayBetweenScanSuccess:500,tryPlayVideoTimeout:5e3},Rt=function(){function r(e,t,n){t===void 0&&(t=new Map),n===void 0&&(n={}),this.reader=e,this.hints=t,this.options=Ot(Ot({},C1),n)}return Object.defineProperty(r.prototype,"possibleFormats",{set:function(e){this.hints.set(oe.POSSIBLE_FORMATS,e)},enumerable:!1,configurable:!0}),r.addVideoSource=function(e,t){try{e.srcObject=t}catch{console.error("got interrupted by new loading request")}},r.mediaStreamSetTorch=function(e,t){return he(this,void 0,void 0,function(){return ve(this,function(n){switch(n.label){case 0:return[4,e.applyConstraints({advanced:[{fillLightMode:t?"flash":"off",torch:!!t}]})];case 1:return n.sent(),[2]}})})},r.mediaStreamIsTorchCompatible=function(e){var t,n,i=e.getVideoTracks();try{for(var a=xr(i),o=a.next();!o.done;o=a.next()){var s=o.value;if(r.mediaStreamIsTorchCompatibleTrack(s))return!0}}catch(u){t={error:u}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return!1},r.mediaStreamIsTorchCompatibleTrack=function(e){try{var t=e.getCapabilities();return"torch"in t}catch(n){return console.error(n),console.warn("Your browser may be not fully compatible with WebRTC and/or ImageCapture specs. Torch will not be available."),!1}},r.isVideoPlaying=function(e){return e.currentTime>0&&!e.paused&&e.readyState>2},r.getMediaElement=function(e,t){var n=document.getElementById(e);if(!n)throw new ye("element with id '".concat(e,"' not found"));if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new ye("element with id '".concat(e,"' must be an ").concat(t," element"));return n},r.createVideoElement=function(e){if(e instanceof HTMLVideoElement)return e;if(typeof e=="string")return r.getMediaElement(e,"video");if(!e&&typeof document<"u"){var t=document.createElement("video");return t.width=200,t.height=200,t}throw new Error("Couldn't get videoElement from videoSource!")},r.prepareImageElement=function(e){if(e instanceof HTMLImageElement)return e;if(typeof e=="string")return r.getMediaElement(e,"img");if(typeof e>"u"){var t=document.createElement("img");return t.width=200,t.height=200,t}throw new Error("Couldn't get imageElement from imageSource!")},r.prepareVideoElement=function(e){var t=r.createVideoElement(e);return t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t},r.isImageLoaded=function(e){return!(!e.complete||e.naturalWidth===0)},r.createBinaryBitmapFromCanvas=function(e){var t=new _1(e),n=new Dn(t);return new bn(n)},r.drawImageOnCanvas=function(e,t){e.drawImage(t,0,0)},r.getMediaElementDimensions=function(e){if(e instanceof HTMLVideoElement)return{height:e.videoHeight,width:e.videoWidth};if(e instanceof HTMLImageElement)return{height:e.naturalHeight||e.height,width:e.naturalWidth||e.width};throw new Error("Couldn't find the Source's dimensions!")},r.createCaptureCanvas=function(e){if(!e)throw new ye("Cannot create a capture canvas without a media element.");if(typeof document>"u")throw new Error(`The page "Document" is undefined, make sure you're running in a browser.`);var t=document.createElement("canvas"),n=r.getMediaElementDimensions(e),i=n.width,a=n.height;return t.style.width=i+"px",t.style.height=a+"px",t.width=i,t.height=a,t},r.tryPlayVideo=function(e){return he(this,void 0,void 0,function(){var t;return ve(this,function(n){switch(n.label){case 0:if(e!=null&&e.ended)return console.error("Trying to play video that has ended."),[2,!1];if(r.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2,!0];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,e.play()];case 2:return n.sent(),[2,!0];case 3:return t=n.sent(),console.warn("It was not possible to play the video.",t),[2,!1];case 4:return[2]}})})},r.createCanvasFromMediaElement=function(e){var t=r.createCaptureCanvas(e),n=t.getContext("2d");if(!n)throw new Error("Couldn't find Canvas 2D Context.");return r.drawImageOnCanvas(n,e),t},r.createBinaryBitmapFromMediaElem=function(e){var t=r.createCanvasFromMediaElement(e);return r.createBinaryBitmapFromCanvas(t)},r.destroyImageElement=function(e){e.src="",e.removeAttribute("src"),e=void 0},r.listVideoInputDevices=function(){return he(this,void 0,void 0,function(){var e,t,n,i,a,o,s,u,f,c,l,h;return ve(this,function(d){switch(d.label){case 0:if(!$n())throw new Error("Can't enumerate devices, navigator is not present.");if(!A1())throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:e=d.sent(),t=[];try{for(n=xr(e),i=n.next();!i.done;i=n.next())a=i.value,o=a.kind==="video"?"videoinput":a.kind,o==="videoinput"&&(s=a.deviceId||a.id,u=a.label||"Video device ".concat(t.length+1),f=a.groupId,c={deviceId:s,label:u,kind:o,groupId:f},t.push(c))}catch(v){l={error:v}}finally{try{i&&!i.done&&(h=n.return)&&h.call(n)}finally{if(l)throw l.error}}return[2,t]}})})},r.findDeviceById=function(e){return he(this,void 0,void 0,function(){var t;return ve(this,function(n){switch(n.label){case 0:return[4,r.listVideoInputDevices()];case 1:return t=n.sent(),t?[2,t.find(function(i){return i.deviceId===e})]:[2]}})})},r.cleanVideoSource=function(e){if(e){try{e.srcObject=null}catch{e.src=""}e&&e.removeAttribute("src")}},r.releaseAllStreams=function(){r.streamTracker.length!==0&&r.streamTracker.forEach(function(e){e.getTracks().forEach(function(t){return t.stop()})}),r.streamTracker=[]},r.playVideoOnLoadAsync=function(e,t){return he(this,void 0,void 0,function(){var n;return ve(this,function(i){switch(i.label){case 0:return[4,r.tryPlayVideo(e)];case 1:return n=i.sent(),n?[2,!0]:[2,new Promise(function(a,o){var s=setTimeout(function(){r.isVideoPlaying(e)||(o(!1),e.removeEventListener("canplay",u))},t),u=function(){r.tryPlayVideo(e).then(function(f){clearTimeout(s),e.removeEventListener("canplay",u),a(f)})};e.addEventListener("canplay",u)})]}})})},r.attachStreamToVideo=function(e,t,n){return n===void 0&&(n=5e3),he(this,void 0,void 0,function(){var i;return ve(this,function(a){switch(a.label){case 0:return i=r.prepareVideoElement(t),r.addVideoSource(i,e),[4,r.playVideoOnLoadAsync(i,n)];case 1:return a.sent(),[2,i]}})})},r._waitImageLoad=function(e){return new Promise(function(t,n){var i=1e4,a=setTimeout(function(){r.isImageLoaded(e)||(e.removeEventListener("load",o),n())},i),o=function(){clearTimeout(a),e.removeEventListener("load",o),t()};e.addEventListener("load",o)})},r.checkCallbackFnOrThrow=function(e){if(!e)throw new ye("`callbackFn` is a required parameter, you cannot capture results without it.")},r.disposeMediaStream=function(e){e.getVideoTracks().forEach(function(t){return t.stop()}),e=void 0},r.prototype.decode=function(e){var t=r.createCanvasFromMediaElement(e);return this.decodeFromCanvas(t)},r.prototype.decodeBitmap=function(e){return this.reader.decode(e,this.hints)},r.prototype.decodeFromCanvas=function(e){var t=r.createBinaryBitmapFromCanvas(e);return this.decodeBitmap(t)},r.prototype.decodeFromImageElement=function(e){return he(this,void 0,void 0,function(){var t;return ve(this,function(n){switch(n.label){case 0:if(!e)throw new ye("An image element must be provided.");return t=r.prepareImageElement(e),[4,this._decodeOnLoadImage(t)];case 1:return[2,n.sent()]}})})},r.prototype.decodeFromImageUrl=function(e){return he(this,void 0,void 0,function(){var t;return ve(this,function(n){switch(n.label){case 0:if(!e)throw new ye("An URL must be provided.");t=r.prepareImageElement(),t.src=e,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.decodeFromImageElement(t)];case 2:return[2,n.sent()];case 3:return r.destroyImageElement(t),[7];case 4:return[2]}})})},r.prototype.decodeFromConstraints=function(e,t,n){return he(this,void 0,void 0,function(){var i,a;return ve(this,function(o){switch(o.label){case 0:return r.checkCallbackFnOrThrow(n),[4,this.getUserMedia(e)];case 1:i=o.sent(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,this.decodeFromStream(i,t,n)];case 3:return[2,o.sent()];case 4:throw a=o.sent(),r.disposeMediaStream(i),a;case 5:return[2]}})})},r.prototype.decodeFromStream=function(e,t,n){return he(this,void 0,void 0,function(){var i,a,o,s,u,f,c,l,h,d=this;return ve(this,function(v){switch(v.label){case 0:return r.checkCallbackFnOrThrow(n),i=this.options.tryPlayVideoTimeout,[4,r.attachStreamToVideo(e,t,i)];case 1:return a=v.sent(),o=function(){r.disposeMediaStream(e),r.cleanVideoSource(a)},s=this.scan(a,n,o),u=e.getVideoTracks(),f=Ot(Ot({},s),{stop:function(){s.stop()},streamVideoConstraintsApply:function(g,y){return he(this,void 0,void 0,function(){var _,x,A,m,S,O,b;return ve(this,function(T){switch(T.label){case 0:_=y?u.filter(y):u,T.label=1;case 1:T.trys.push([1,6,7,8]),x=xr(_),A=x.next(),T.label=2;case 2:return A.done?[3,5]:(m=A.value,[4,m.applyConstraints(g)]);case 3:T.sent(),T.label=4;case 4:return A=x.next(),[3,2];case 5:return[3,8];case 6:return S=T.sent(),O={error:S},[3,8];case 7:try{A&&!A.done&&(b=x.return)&&b.call(x)}finally{if(O)throw O.error}return[7];case 8:return[2]}})})},streamVideoConstraintsGet:function(g){return u.find(g).getConstraints()},streamVideoSettingsGet:function(g){return u.find(g).getSettings()},streamVideoCapabilitiesGet:function(g){return u.find(g).getCapabilities()}}),c=r.mediaStreamIsTorchCompatible(e),c&&(l=u==null?void 0:u.find(function(g){return r.mediaStreamIsTorchCompatibleTrack(g)}),h=function(g){return he(d,void 0,void 0,function(){return ve(this,function(y){switch(y.label){case 0:return[4,r.mediaStreamSetTorch(l,g)];case 1:return y.sent(),[2]}})})},f.switchTorch=h,f.stop=function(){return he(d,void 0,void 0,function(){return ve(this,function(g){switch(g.label){case 0:return s.stop(),[4,h(!1)];case 1:return g.sent(),[2]}})})}),[2,f]}})})},r.prototype.decodeFromVideoDevice=function(e,t,n){return he(this,void 0,void 0,function(){var i,a;return ve(this,function(o){switch(o.label){case 0:return r.checkCallbackFnOrThrow(n),e?i={deviceId:{exact:e}}:i={facingMode:"environment"},a={video:i},[4,this.decodeFromConstraints(a,t,n)];case 1:return[2,o.sent()]}})})},r.prototype.decodeFromVideoElement=function(e,t){return he(this,void 0,void 0,function(){var n,i;return ve(this,function(a){switch(a.label){case 0:if(r.checkCallbackFnOrThrow(t),!e)throw new ye("A video element must be provided.");return n=r.prepareVideoElement(e),i=this.options.tryPlayVideoTimeout,[4,r.playVideoOnLoadAsync(n,i)];case 1:return a.sent(),[2,this.scan(n,t)]}})})},r.prototype.decodeFromVideoUrl=function(e,t){return he(this,void 0,void 0,function(){var n,i,a,o;return ve(this,function(s){switch(s.label){case 0:if(r.checkCallbackFnOrThrow(t),!e)throw new ye("An URL must be provided.");return n=r.prepareVideoElement(),n.src=e,i=function(){r.cleanVideoSource(n)},a=this.options.tryPlayVideoTimeout,[4,r.playVideoOnLoadAsync(n,a)];case 1:return s.sent(),o=this.scan(n,t,i),[2,o]}})})},r.prototype.decodeOnceFromConstraints=function(e,t){return he(this,void 0,void 0,function(){var n;return ve(this,function(i){switch(i.label){case 0:return[4,this.getUserMedia(e)];case 1:return n=i.sent(),[4,this.decodeOnceFromStream(n,t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromStream=function(e,t){return he(this,void 0,void 0,function(){var n,i,a;return ve(this,function(o){switch(o.label){case 0:return n=!!t,[4,r.attachStreamToVideo(e,t)];case 1:i=o.sent(),o.label=2;case 2:return o.trys.push([2,,4,5]),[4,this.scanOneResult(i)];case 3:return a=o.sent(),[2,a];case 4:return n||r.cleanVideoSource(i),[7];case 5:return[2]}})})},r.prototype.decodeOnceFromVideoDevice=function(e,t){return he(this,void 0,void 0,function(){var n,i;return ve(this,function(a){switch(a.label){case 0:return e?n={deviceId:{exact:e}}:n={facingMode:"environment"},i={video:n},[4,this.decodeOnceFromConstraints(i,t)];case 1:return[2,a.sent()]}})})},r.prototype.decodeOnceFromVideoElement=function(e){return he(this,void 0,void 0,function(){var t,n;return ve(this,function(i){switch(i.label){case 0:if(!e)throw new ye("A video element must be provided.");return t=r.prepareVideoElement(e),n=this.options.tryPlayVideoTimeout,[4,r.playVideoOnLoadAsync(t,n)];case 1:return i.sent(),[4,this.scanOneResult(t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromVideoUrl=function(e){return he(this,void 0,void 0,function(){var t,n;return ve(this,function(i){switch(i.label){case 0:if(!e)throw new ye("An URL must be provided.");t=r.prepareVideoElement(),t.src=e,n=this.decodeOnceFromVideoElement(t),i.label=1;case 1:return i.trys.push([1,,3,4]),[4,n];case 2:return[2,i.sent()];case 3:return r.cleanVideoSource(t),[7];case 4:return[2]}})})},r.prototype.scanOneResult=function(e,t,n,i){var a=this;return t===void 0&&(t=!0),n===void 0&&(n=!0),i===void 0&&(i=!0),new Promise(function(o,s){a.scan(e,function(u,f,c){if(u){o(u),c.stop();return}if(f){if(f instanceof I&&t||f instanceof _e&&n||f instanceof N&&i)return;c.stop(),s(f)}})})},r.prototype.scan=function(e,t,n){var i=this;r.checkCallbackFnOrThrow(t);var a=r.createCaptureCanvas(e),o;try{o=a.getContext("2d",{willReadFrequently:!0})}catch{o=a.getContext("2d")}if(!o)throw new Error("Couldn't create canvas for visual element scan.");var s=function(){o=void 0,a=void 0},u=!1,f,c=function(){u=!0,clearTimeout(f),s(),n&&n()},l={stop:c},h=function(){if(!u)try{r.drawImageOnCanvas(o,e);var d=i.decodeFromCanvas(a);t(d,void 0,l),f=setTimeout(h,i.options.delayBetweenScanSuccess)}catch(_){t(void 0,_,l);var v=_ instanceof _e,g=_ instanceof N,y=_ instanceof I;if(v||g||y){f=setTimeout(h,i.options.delayBetweenScanAttempts);return}s(),n&&n(_)}};return h(),l},r.prototype._decodeOnLoadImage=function(e){return he(this,void 0,void 0,function(){var t;return ve(this,function(n){switch(n.label){case 0:return t=r.isImageLoaded(e),t?[3,2]:[4,r._waitImageLoad(e)];case 1:n.sent(),n.label=2;case 2:return[2,this.decode(e)]}})})},r.prototype.getUserMedia=function(e){return he(this,void 0,void 0,function(){var t;return ve(this,function(n){switch(n.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return t=n.sent(),r.streamTracker.push(t),[2,t]}})})},r.streamTracker=[],r}(),E1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){E1(e,r);function e(t,n){return r.call(this,new Qt,t,n)||this}return e})(Rt);var m1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){m1(e,r);function e(t,n){return r.call(this,new St(t),t,n)||this}return e})(Rt);var S1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){S1(e,r);function e(t,n){return r.call(this,new er,t,n)||this}return e})(Rt);var I1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){I1(e,r);function e(t,n){var i=this,a=new Hn;return a.setHints(t),i=r.call(this,a,t,n)||this,i.reader=a,i}return Object.defineProperty(e.prototype,"possibleFormats",{set:function(t){this.hints.set(oe.POSSIBLE_FORMATS,t),this.reader.setHints(this.hints)},enumerable:!1,configurable:!0}),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e.prototype.setHints=function(t){this.hints=t,this.reader.setHints(this.hints)},e})(Rt);var O1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();(function(r){O1(e,r);function e(t,n){return r.call(this,new rr,t,n)||this}return e})(Rt);var T1=function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(e,t)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),b1=function(r){T1(e,r);function e(t,n){return r.call(this,new tr,t,n)||this}return e}(Rt);const D1={class:"qr-scanner"},R1={class:"scanner-container"},N1={class:"camera-container"},P1={class:"scan-frame"},M1={class:"loading-state"},B1={class:"error-state"},F1={class:"scanner-actions"},L1={__name:"QRScanner",props:{show:{type:Boolean,default:!1}},emits:["update:show","scan-success","scan-error","switch-manual"],setup(r,{emit:e}){const t=r,n=e,i=ge(!1),a=ge(null),o=ge(!1),s=ge(""),u=ge(null),f=ge(null);Tr(()=>t.show,x=>{i.value=x,x?c():y()});const c=async()=>{try{if(s.value="",o.value=!1,!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw new Error("当前浏览器不支持摄像头功能");u.value=new b1,f.value=await navigator.mediaDevices.getUserMedia({video:{facingMode:"environment",width:{ideal:1920,min:640},height:{ideal:1080,min:480},frameRate:{ideal:30,min:15},focusMode:"continuous",exposureMode:"continuous",whiteBalanceMode:"continuous"}}),a.value&&(a.value.srcObject=f.value,await a.value.play(),o.value=!0,l())}catch(x){console.error("启动摄像头失败:",x),d(x)}},l=async()=>{try{if(!u.value||!a.value)return;let x=0;const A=3,m=await u.value.decodeFromVideoDevice(void 0,a.value,(S,O)=>{S?(console.log("扫码成功，尝试次数:",x+1),h(S.getText())):O&&(x++,x%30===0&&console.log(`扫码尝试中... (${x}次)`),x>150&&x%60===0&&Ce({type:"warning",message:"请调整二维码角度和距离",duration:1e3}))})}catch(x){console.error("扫码解码失败:",x),Ce({type:"fail",message:"扫码功能启动失败，请重试"})}},h=x=>{var A,m;try{console.log("扫码原始内容:",x);let S="",O="",b="";const T=x.trim().replace(/[\s\n\r\t]/g,"");if(console.log("清理后的内容:",T),T.includes("roomNumber=")||T.includes("meetingRoom="))try{const D=new URL(T),P=new URLSearchParams(D.hash.split("?")[1]||D.search);S=P.get("roomNumber")||P.get("meetingRoom"),O=P.get("building"),b=P.get("floor")}catch{const D=T.match(/(?:roomNumber|meetingRoom)=([A-Z]+\d+)/i),P=T.match(/building=([A-Z]+)/i),G=T.match(/floor=(\d+)/i);D&&(S=D[1]),P&&(O=P[1]),G&&(b=G[1])}else if(T.match(/^[A-Z]+\d+$/))S=T,O=((A=S.match(/^([A-Z]+)/))==null?void 0:A[1])||"";else{const D=[/([A-Z]+\d{3,4})/g,/([A-Z]+\d{2,3})/g,/会议室[：:]?([A-Z]+\d+)/g,/房间[：:]?([A-Z]+\d+)/g,/([A-Z]\d+)/g];for(const P of D){const G=[...T.matchAll(P)];if(G.length>0){S=G[0][1],O=((m=S.match(/^([A-Z]+)/))==null?void 0:m[1])||"";break}}}if(S){const D=S.match(/^([A-Z])\1+(.*)$/);if(D&&(S=D[1]+D[2],O=D[1]),S=S.toUpperCase(),!b&&S.length>=4){let P=null;P=S.match(/^[A-Z](\d{2})\d{2}$/),P?b=parseInt(P[1]).toString():(P=S.match(/^[A-Z]+(\d{1,2})\d{2}$/),P&&(b=parseInt(P[1]).toString()))}console.log("解析后的会议室编号:",S),console.log("楼栋:",O,"楼层:",b),Ce({type:"success",message:`扫码成功！获取到会议室：${S}`}),n("scan-success",{roomNumber:S,building:O,floor:b,url:x}),_()}else throw new Error("无法识别会议室编号")}catch(S){console.error("解析二维码失败:",S),console.error("原始内容:",x);let O="二维码格式不正确";x.length>200?O="二维码内容过长，请确认是会议室二维码":x.length<3?O="二维码内容过短，请重新扫描":x.match(/[A-Z]/)||(O="未找到会议室编号，请扫描正确的会议室二维码"),Ce({type:"fail",message:O,duration:2e3}),setTimeout(()=>{o.value&&Ce({type:"info",message:"请重新对准二维码扫描",duration:1500})},2500)}},d=x=>{let A="启动摄像头失败";x.name==="NotAllowedError"?A="请允许访问摄像头权限":x.name==="NotFoundError"?A="未找到摄像头设备":x.name==="NotSupportedError"?A="当前浏览器不支持摄像头功能":x.message&&(A=x.message),s.value=A,n("scan-error",A)},v=()=>{c()},g=()=>{n("switch-manual"),_()},y=()=>{try{u.value&&(u.value.reset(),u.value=null),f.value&&(f.value.getTracks().forEach(x=>x.stop()),f.value=null),a.value&&(a.value.srcObject=null),o.value=!1,s.value=""}catch(x){console.error("停止扫码失败:",x)}},_=()=>{y(),n("update:show",!1)};return wi(()=>{y()}),(x,A)=>{const m=xe("van-loading"),S=xe("van-icon"),O=xe("van-button"),b=xe("van-popup");return re(),pe("div",D1,[U(b,{show:i.value,"onUpdate:show":A[0]||(A[0]=T=>i.value=T),position:"center",style:{width:"90%",height:"70%"},round:"",closeable:"",onClose:_},{default:J(()=>[F("div",R1,[A[6]||(A[6]=F("div",{class:"scanner-header"},[F("h3",null,"扫描二维码"),F("p",null,"请将二维码放入扫描框内")],-1)),F("div",N1,[wt(F("video",{ref_key:"videoRef",ref:a,class:"camera-video",autoplay:"",muted:"",playsinline:""},null,512),[[At,o.value]]),wt(F("div",P1,A[1]||(A[1]=[F("div",{class:"scan-line"},null,-1)]),512),[[At,o.value]]),wt(F("div",M1,[U(m,{size:"24px"}),A[2]||(A[2]=F("p",null,"正在启动摄像头...",-1))],512),[[At,!o.value&&!s.value]]),wt(F("div",B1,[U(S,{name:"warning-o",size:"48",color:"#ee0a24"}),F("p",null,Ge(s.value),1),U(O,{type:"primary",size:"small",onClick:v},{default:J(()=>A[3]||(A[3]=[Pe("重试")])),_:1,__:[3]})],512),[[At,s.value]])]),F("div",F1,[U(O,{type:"default",size:"large",onClick:_,block:""},{default:J(()=>A[4]||(A[4]=[Pe(" 取消扫码 ")])),_:1,__:[4]}),U(O,{type:"primary",size:"large",onClick:g,block:"",style:{"margin-top":"12px"}},{default:J(()=>A[5]||(A[5]=[Pe(" 手动选择会议室 ")])),_:1,__:[5]})])])]),_:1},8,["show"])])}}},k1=br(L1,[["__scopeId","data-v-3082f17f"]]),U1={class:"room-selector"},V1={class:"selector-header"},H1={class:"search-section"},G1={class:"filter-section"},X1={class:"filter-tabs"},W1=["onClick"],z1={class:"room-list"},$1={class:"room-grid"},j1=["onClick"],Y1={class:"room-number"},Z1={class:"room-info"},K1={class:"building"},q1={key:0,class:"floor"},Q1={key:0,class:"load-more"},J1={key:1,class:"no-result"},eu={class:"quick-input"},tu={__name:"RoomSelector",props:{show:{type:Boolean,default:!1},rooms:{type:Array,default:()=>[]}},emits:["update:show","select"],setup(r,{emit:e}){const t=r,n=e,i=ge(!1),a=ge(""),o=ge(""),s=ge(20),u=ge(!1),f=ge(!1),c=ge("");Tr(()=>t.show,T=>{i.value=T,T&&m()});const l=rt(()=>{const T=new Set;return t.rooms.forEach(D=>{var P;if(D.building)T.add(D.building);else{const G=(P=D.number)==null?void 0:P.match(/^([A-Z]+)/);G&&T.add(G[1])}}),Array.from(T).sort()}),h=rt(()=>{let T=t.rooms;if(o.value&&(T=T.filter(D=>{var P;return D.building?D.building===o.value:(P=D.number)==null?void 0:P.startsWith(o.value)})),a.value){const D=a.value.toUpperCase();T=T.filter(P=>{var G,H;return((G=P.number)==null?void 0:G.toUpperCase().includes(D))||((H=P.name)==null?void 0:H.toUpperCase().includes(D))})}return T.sort((D,P)=>D.number&&P.number?D.number.localeCompare(P.number):0)}),d=rt(()=>h.value.slice(0,s.value)),v=rt(()=>h.value.length>s.value),g=T=>{a.value=T,s.value=20},y=T=>{o.value=T,s.value=20},_=()=>{u.value=!0,setTimeout(()=>{s.value+=20,u.value=!1},300)},x=T=>{n("select",T),b(),Ce({type:"success",message:`已选择会议室：${T.number}`})},A=()=>{a.value="",o.value="",s.value=20},m=()=>{a.value="",o.value="",s.value=20,c.value=""},S=T=>{c.value=T.toUpperCase().replace(/[^A-Z0-9]/g,"")},O=()=>{var D;if(!c.value){Ce("请输入会议室编号");return}const T={number:c.value,building:((D=c.value.match(/^([A-Z]+)/))==null?void 0:D[1])||"",name:c.value};n("select",T),b(),Ce({type:"success",message:`已输入会议室：${c.value}`})},b=()=>{n("update:show",!1)};return(T,D)=>{const P=xe("van-field"),G=xe("van-button"),H=xe("van-icon"),se=xe("van-dialog"),Be=xe("van-popup");return re(),_t(Be,{show:i.value,"onUpdate:show":D[5]||(D[5]=W=>i.value=W),position:"bottom",style:{height:"70%"},round:"",closeable:"",onClose:b},{default:J(()=>[F("div",U1,[F("div",V1,[D[6]||(D[6]=F("h3",null,"选择会议室",-1)),F("p",null,"共"+Ge(h.value.length)+"个会议室",1)]),F("div",H1,[U(P,{modelValue:a.value,"onUpdate:modelValue":D[0]||(D[0]=W=>a.value=W),placeholder:"搜索会议室编号，如A01","left-icon":"search",clearable:"",onInput:g},null,8,["modelValue"])]),F("div",G1,[D[7]||(D[7]=F("div",{class:"filter-title"},"按楼栋筛选",-1)),F("div",X1,[F("div",{class:yr(["filter-tab",{active:o.value===""}]),onClick:D[1]||(D[1]=W=>y(""))}," 全部 ",2),(re(!0),pe(Et,null,Ct(l.value,W=>(re(),pe("div",{key:W,class:yr(["filter-tab",{active:o.value===W}]),onClick:lt=>y(W)},Ge(W)+"栋 ",11,W1))),128))])]),F("div",z1,[F("div",$1,[(re(!0),pe(Et,null,Ct(d.value,W=>(re(),pe("div",{key:W.number,class:"room-item",onClick:lt=>x(W)},[F("div",Y1,Ge(W.number),1),F("div",Z1,[F("span",K1,Ge(W.building)+"栋",1),W.floor?(re(),pe("span",q1,Ge(W.floor)+"楼",1)):We("",!0)])],8,j1))),128))]),v.value?(re(),pe("div",Q1,[U(G,{type:"default",size:"small",onClick:_,loading:u.value},{default:J(()=>[Pe(" 加载更多 ("+Ge(h.value.length-s.value)+"个) ",1)]),_:1},8,["loading"])])):We("",!0),h.value.length===0?(re(),pe("div",J1,[U(H,{name:"search",size:"48",color:"#c8c9cc"}),D[9]||(D[9]=F("p",null,"未找到匹配的会议室",-1)),U(G,{type:"primary",size:"small",onClick:A},{default:J(()=>D[8]||(D[8]=[Pe(" 清除搜索 ")])),_:1,__:[8]})])):We("",!0)]),F("div",eu,[U(G,{type:"primary",size:"large",block:"",onClick:D[2]||(D[2]=W=>f.value=!0)},{default:J(()=>D[10]||(D[10]=[Pe(" 直接输入会议室编号 ")])),_:1,__:[10]})])]),U(se,{show:f.value,"onUpdate:show":D[4]||(D[4]=W=>f.value=W),title:"输入会议室编号","show-cancel-button":"",onConfirm:O},{default:J(()=>[U(P,{modelValue:c.value,"onUpdate:modelValue":D[3]||(D[3]=W=>c.value=W),placeholder:"请输入会议室编号，如A0101",onInput:S},null,8,["modelValue"])]),_:1},8,["show"])]),_:1},8,["show"])}}},ru=br(tu,[["__scopeId","data-v-46678677"]]),nu={class:"multi-service-form"},iu={class:"content"},au={class:"meeting-room-section"},ou={key:0,class:"room-tip"},su={class:"service-checklist-section"},uu={class:"checklist-header"},fu={class:"section-title"},cu={class:"selected-count"},lu={key:0,class:"quick-select-section"},du={class:"quick-select-header"},hu={class:"quick-select-items"},vu=["onClick"],pu={class:"service-categories"},gu=["onClick"],xu={class:"category-info"},yu={class:"category-name"},_u={key:0,class:"category-count"},wu={class:"category-actions"},Au={key:0,class:"service-items"},Cu=["onClick"],Eu={class:"item-content"},mu={class:"item-text"},Su={class:"item-controls"},Iu={key:0,class:"quantity-selector"};const Ou={class:"contact-info-section"},Tu={class:"contact-category enterprise-card haptic-feedback"},bu={class:"category-info"},Du={key:0,class:"category-count"},Ru={class:"category-actions"},Nu={class:"contact-items"},Pu={class:"submit-section"},Mu={__name:"MultiServiceForm",setup(r){const e=(C,w)=>{let R;return function(...ne){const ie=()=>{clearTimeout(R),C(...ne)};clearTimeout(R),R=setTimeout(ie,w)}},t=Ei(),n=Ci(),i=ge(),a=ge(!1),o={phone:C=>!C||!C.trim()?!0:/^1[3-9]\d{9}$/.test(C.trim()),meetingRoom:C=>!C||!C.trim()?!1:/^[A-Z]+\d{2,4}$/.test(C.trim()),serviceSelection:C=>{let w=0;return C.forEach(R=>{w+=R.size}),w>0}},s={phone:"请输入正确的手机号码格式（如：13800138000）",meetingRoom:"请输入正确的会议室编号格式（如：A0815、A1101、B1234）",serviceSelection:"请至少选择一项服务"},u={getDetailedErrorMessage:C=>{const w={NETWORK_ERROR:"网络连接失败，请检查网络设置后重试",VALIDATION_ERROR:"表单数据验证失败，请检查输入内容",SERVER_ERROR:"服务器暂时无法处理请求，请稍后重试",AUTH_ERROR:"身份验证失败，请刷新页面后重试",TIMEOUT_ERROR:"请求超时，请检查网络连接后重试",DUPLICATE_ERROR:"检测到重复提交，请勿重复操作",ROOM_NOT_FOUND:"会议室不存在，请检查会议室编号",SERVICE_UNAVAILABLE:"服务暂时不可用，请稍后重试"};if(C.code&&w[C.code])return w[C.code];const R=C.message||C.toString();return R.includes("网络")?w.NETWORK_ERROR:R.includes("超时")?w.TIMEOUT_ERROR:R.includes("重复")?w.DUPLICATE_ERROR:R.includes("会议室")?w.ROOM_NOT_FOUND:R.includes("服务")?w.SERVICE_UNAVAILABLE:R||"操作失败，请重试"},showError:(C,w={})=>{const R=u.getDetailedErrorMessage(C),V={type:"fail",message:R,duration:4e3,...w};Ce(V),console.error("操作失败:",{error:C,message:R,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href})},showWarning:(C,w={})=>{const R={type:"warning",message:C,duration:3e3,...w};Ce(R)},showSuccess:(C,w={})=>{const R={type:"success",message:C,duration:2e3,...w};Ce(R)}},c=(()=>{const C=Gr({selectedServices:new Map,serviceQuantities:new Map,serviceStatuses:new Map}),w=(Z,de)=>`${Z}_${de}`;return{state:C,addService:(Z,de)=>{if(C.selectedServices.has(Z)||C.selectedServices.set(Z,new Set),C.selectedServices.get(Z).add(de),Z==="tea"){const M=w(Z,de);C.serviceQuantities.has(M)||C.serviceQuantities.set(M,1)}if(Z==="equipment"){const M=w(Z,de);C.serviceStatuses.has(M)||C.serviceStatuses.set(M,null)}},removeService:(Z,de)=>{const De=C.selectedServices.get(Z);De&&(De.delete(de),De.size===0&&C.selectedServices.delete(Z));const M=w(Z,de);C.serviceQuantities.delete(M),C.serviceStatuses.delete(M)},isServiceSelected:(Z,de)=>{const De=C.selectedServices.get(Z);return De?De.has(de):!1},getServiceQuantity:(Z,de)=>{const De=w(Z,de);return C.serviceQuantities.get(De)||1},setServiceQuantity:(Z,de,De)=>{const M=w(Z,de);C.serviceQuantities.set(M,De)},getServiceStatus:(Z,de)=>{const De=w(Z,de),M=C.serviceStatuses.get(De);return M||null},setServiceStatus:(Z,de,De)=>{const M=w(Z,de);C.serviceStatuses.set(M,De)},getTotalCount:()=>{let Z=0;return C.selectedServices.forEach(de=>{Z+=de.size}),Z},clearAll:()=>{C.selectedServices.clear(),C.serviceQuantities.clear(),C.serviceStatuses.clear()}}})(),l={types:{light:30,medium:50,heavy:100,success:[30,50],error:[100,50,100]},provide:(C="light")=>{if(!navigator.vibrate)return;const w=l.types[C];Array.isArray(w)?navigator.vibrate(w):navigator.vibrate(w||l.types.light)},isSupported:()=>"vibrate"in navigator},h={DRAFT_KEY:"serviceFormDraft",saveDraft:()=>{try{const C={meetingRoom:d.meetingRoom,selectedServices:Array.from(c.state.selectedServices.entries()).map(([w,R])=>[w,Array.from(R)]),serviceQuantities:Array.from(c.state.serviceQuantities.entries()),serviceStatuses:Array.from(c.state.serviceStatuses.entries()),contactName:d.contactName,contactPhone:d.contactPhone,description:d.description,urgency:d.urgency,timestamp:Date.now()};localStorage.setItem(h.DRAFT_KEY,JSON.stringify(C)),console.log("草稿已保存")}catch(C){console.error("保存草稿失败:",C)}},loadDraft:()=>{try{const C=localStorage.getItem(h.DRAFT_KEY);if(!C)return null;const w=JSON.parse(C);return Date.now()-w.timestamp>24*60*60*1e3?(h.clearDraft(),null):w}catch(C){return console.error("加载草稿失败:",C),h.clearDraft(),null}},restoreDraft:C=>{try{return d.meetingRoom=C.meetingRoom||"",d.contactName=C.contactName||"",d.contactPhone=C.contactPhone||"",d.description=C.description||"",d.urgency=C.urgency||"normal",C.selectedServices&&(c.state.selectedServices.clear(),C.selectedServices.forEach(([w,R])=>{c.state.selectedServices.set(w,new Set(R))})),C.serviceQuantities&&(c.state.serviceQuantities.clear(),C.serviceQuantities.forEach(([w,R])=>{c.state.serviceQuantities.set(w,R)})),C.serviceStatuses&&(c.state.serviceStatuses.clear(),C.serviceStatuses.forEach(([w,R])=>{c.state.serviceStatuses.set(w,R)})),console.log("草稿已恢复"),!0}catch(w){return console.error("恢复草稿失败:",w),!1}},clearDraft:()=>{try{localStorage.removeItem(h.DRAFT_KEY),console.log("草稿已清除")}catch(C){console.error("清除草稿失败:",C)}},hasDraft:()=>h.loadDraft()!==null},d=Gr({meetingRoom:"",contactName:"",contactPhone:"",description:"",urgency:"normal"}),v=ge(!1),g=ge(!1),y=ge(!1),_=ge([]),x=ge([]),A=ge([{text:"茶水服务",value:"tea",icon:"hot-o",expanded:!1,popular:!0,items:[{text:"更换茶水",value:"replace_tea",popular:!0},{text:"增加茶水",value:"add_tea",popular:!0},{text:"清洁茶具",value:"clean_tea_set"}]},{text:"空调服务",value:"air_conditioning",icon:"fire-o",expanded:!1,popular:!0,items:[{text:"降温",value:"cool_down",popular:!0},{text:"关闭",value:"turn_off"},{text:"调节风速",value:"adjust_fan"}]},{text:"设备服务",value:"equipment",icon:"desktop-o",expanded:!1,items:[{text:"投影设备问题",value:"projector_issue",hasStatus:!0,popular:!0,statusOptions:[{text:"投影设备无法启动",value:"no_start"},{text:"投影画面异常",value:"display_abnormal"},{text:"投影设备故障",value:"malfunction"}]},{text:"音响设备问题",value:"audio_issue",hasStatus:!0,popular:!0,statusOptions:[{text:"音响无声音",value:"no_sound"},{text:"音响杂音/噪音",value:"noise"},{text:"音响设备故障",value:"malfunction"}]},{text:"麦克风故障",value:"microphone_issue",hasStatus:!0,statusOptions:[{text:"麦克风无声音",value:"no_sound"},{text:"麦克风杂音",value:"noise"},{text:"麦克风损坏",value:"damaged"}]},{text:"网络连接问题",value:"network_issue",hasStatus:!0,statusOptions:[{text:"网络无法连接",value:"no_connection"},{text:"网络连接不稳定",value:"unstable"},{text:"网络速度慢",value:"slow_speed"}]},{text:"电脑设备问题",value:"computer_issue",hasStatus:!0,statusOptions:[{text:"电脑无法启动",value:"no_start"},{text:"电脑运行缓慢",value:"slow_running"},{text:"电脑系统故障",value:"system_error"}]},{text:"显示屏问题",value:"display_issue",hasStatus:!0,statusOptions:[{text:"显示屏无显示",value:"no_display"},{text:"显示屏花屏/闪烁",value:"screen_flicker"},{text:"显示屏故障",value:"malfunction"}]}]}]),m=async()=>{try{const C=await Oi("roomManagement",{action:"getRooms"});C.code===0&&C.data&&C.data.length>0?(_.value=C.data.map(w=>{var R,V;return{number:w.roomNumber||w.number,name:w.name||w.roomNumber||w.number,building:w.building||((V=(R=w.roomNumber||w.number)==null?void 0:R.match(/^([A-Z]+)/))==null?void 0:V[1])||"",floor:w.floor||""}}),x.value=_.value.slice(0,8).map(w=>w.number)):(Ce("请先在管理后台添加会议室数据"),_.value=[],x.value=[])}catch(C){console.error("加载会议室列表失败:",C),u.showError(C,{message:"加载会议室列表失败，请检查网络连接"}),_.value=[],x.value=[]}},S=(C,w)=>{w&&(w.preventDefault(),w.stopPropagation()),C.expanded=!C.expanded},O=(C,w)=>c.isServiceSelected(C,w),b=(C,w,R)=>{R?(c.addService(C,w),l.provide("light")):(c.removeService(C,w),l.provide("light"))},T=C=>{const w=c.state.selectedServices.get(C);return w&&w.size>0},D=C=>{const w=c.state.selectedServices.get(C);return w?w.size:0},P=()=>lt.value,G=C=>{const w=A.value.find(R=>R.value===C);return w?w.icon:"service-o"},H=C=>{const w=A.value.find(R=>R.value===C.categoryValue);w&&(w.expanded=!0),b(C.categoryValue,C.value,!0),l.provide("medium"),u.showSuccess(`已选择：${C.text}`,{duration:1e3})},se=(C,w)=>{const R=A.value.find(V=>V.value===C);R&&(w?R.items.forEach(V=>{c.addService(C,V.value)}):R.items.forEach(V=>{c.removeService(C,V.value)}))},Be=rt(()=>c.getTotalCount()),W=()=>Be.value,lt=rt(()=>{const C=[];return A.value.forEach(w=>{w.items.forEach(R=>{R.popular&&C.push({...R,categoryValue:w.value,categoryText:w.text})})}),C.slice(0,6)}),Nt=rt(()=>{const C=[];return A.value.forEach(w=>{const R=c.state.selectedServices.get(w.value);if(R&&R.size>0){const V=w.items.filter(ne=>R.has(ne.value));C.push({...w,selectedItems:V})}}),C}),Pt=rt(()=>h.hasDraft()),jn=rt(()=>{const C=new Map;return A.value.forEach(w=>{w.value==="equipment"&&w.items.forEach(R=>{if(R.hasStatus&&O(w.value,R.value)){const V=`${w.value}_${R.value}`,ne=c.getServiceStatus(w.value,R.value);if(ne)C.set(V,ne);else if(R.statusOptions&&R.statusOptions.length>0){const ie=R.statusOptions[0].value;c.setServiceStatus(w.value,R.value,ie),C.set(V,ie)}}})}),C}),Yn=(C,w)=>Xt.getDisplayText(C,w),Xt={getQuantity:(C,w)=>c.getServiceQuantity(C,w),setQuantity:(C,w,R)=>{c.setServiceQuantity(C,w,R),l.provide("light")},getStatus:(C,w)=>c.getServiceStatus(C,w),setStatus:(C,w,R)=>{c.setServiceStatus(C,w,R),l.provide("light")},getDisplayText:(C,w)=>{if(C==="tea"&&O(C,w.value)){const R=Xt.getQuantity(C,w.value);return`${w.text} x ${R}人`}return w.text}},Lr=(C,w)=>{const R=c.getServiceStatus(C,w);if(R)return R;if(C==="equipment"){const V=`${C}_${w}`,ne=jn.value.get(V);if(ne)return ne;const ie=A.value.find(Fe=>Fe.value===C);if(ie){const Fe=ie.items.find(je=>je.value===w);if(Fe&&Fe.statusOptions&&Fe.statusOptions.length>0){const je=Fe.statusOptions[0].value;return c.setServiceStatus(C,w,je),je}}}return"startup"},kr=Xt.getQuantity,Zn=Xt.setQuantity,Ur=(C,w,R)=>{console.log("updateItemStatus called:",{categoryValue:C,itemValue:w,status:R});try{c.setServiceStatus(C,w,R),l.provide("light");const V=c.getServiceStatus(C,w);console.log("Status verification:",{expected:R,actual:V}),V!==R&&console.warn("Status update failed - mismatch detected")}catch(V){console.error("Error updating item status:",V)}},Vr=(C,w,R,V)=>{console.log("handleRadioClick called:",{categoryValue:C,itemValue:w,status:R}),V.stopPropagation(),V.preventDefault(),Ur(C,w,R),Si(()=>{console.log("Force update completed")})},Kn=()=>Nt.value,qn=()=>{g.value=!0},Qn=C=>{const{roomNumber:w,building:R,floor:V}=C;w&&(d.meetingRoom=w,$r(R&&V?{type:"success",message:`扫码成功！获取到会议室：${w}（${R}栋${V}楼）`}:{type:"success",message:`扫码成功！获取到会议室：${w}`}))},Jn=C=>{Ce({type:"fail",message:C})},ei=()=>{v.value=!0},ti=()=>{window.history.length>1?t.back():t.push("/")},ri=C=>{d.meetingRoom=C.number,v.value=!1},ni=e(C=>{d.meetingRoom=C.toUpperCase().replace(/[^A-Z0-9]/g,"")},300),ii=C=>{ni(C)},ai=async()=>{if(!o.serviceSelection(c.state.selectedServices)){Ce(s.serviceSelection);return}if(!o.phone(d.contactPhone)){Ce(s.phone);return}try{a.value=!0,Ii({message:"提交中...",forbidClick:!0,duration:0});const C=Kn(),w=[],R=Date.now().toString();C.forEach(ne=>{ne.selectedItems.forEach(ie=>{let Fe=ie.text,je={};if(ne.value==="tea"){const tt=kr(ne.value,ie.value);Fe=`${ie.text} x ${tt}人`,je.quantity=tt}if(ne.value==="equipment"&&ie.hasStatus){const tt=Lr(ne.value,ie.value);let Mt=tt;if(ie.statusOptions){const dt=ie.statusOptions.find(Z=>Z.value===tt);dt&&(Mt=dt.text)}Fe=`${ie.text} - ${Mt}`,je.equipmentStatus=tt}w.push({meetingName:d.meetingRoom,meetingRoom:d.meetingRoom,serviceType:ne.text,serviceDetail:Fe,contactName:d.contactName,contactPhone:d.contactPhone,description:d.description,urgency:d.urgency,location:d.meetingRoom,createTime:new Date().toISOString(),batchId:R,...je})})});const V=await Ti(w,R);Wr(),l.provide("success"),u.showSuccess(`成功提交${w.length}项服务需求！`),h.clearDraft(),t.push({name:"MultiServiceSuccess",query:{meetingRoom:d.meetingRoom,serviceCount:w.length,batchId:R,services:JSON.stringify(C.map(ne=>({category:ne.text,items:ne.selectedItems.map(ie=>ie.text)})))}})}catch(C){Wr(),u.showError(C,{message:C.message||"提交失败，请重试"})}finally{a.value=!1}},oi=()=>{const C=n.query.room||n.query.roomNumber||n.query.meetingRoom||n.query.roomId||n.params.room||n.params.roomNumber;if(C){const w=C.toString().toUpperCase().trim();/^[A-Z]+\d{2,4}$/.test(w)?(d.meetingRoom=w,Ce({type:"success",message:`已自动选择会议室：${w}`,duration:2e3})):(d.meetingRoom=w,Ce({type:"warning",message:"会议室编号格式可能不正确，请检查",duration:3e3}))}if(n.query.service||n.query.serviceType){const w=n.query.service||n.query.serviceType;si(w)}},si=C=>{const R={tea:"tea",water:"tea",air:"air_conditioning",ac:"air_conditioning",equipment:"equipment",device:"equipment"}[C.toLowerCase()];if(R){const V=A.value.find(ne=>ne.value===R);V&&(V.expanded=!0,Ce({type:"success",message:`已为您展开${V.text}类别`,duration:2e3}))}};Ai(async()=>{try{console.log("MultiServiceForm mounted"),console.log("serviceCategories:",A.value),await m(),oi(),h.hasDraft()&&Ce({type:"warning",message:"检测到未完成的表单，点击恢复按钮可恢复之前的填写内容",duration:5e3})}catch(C){console.error("页面初始化失败:",C),u.showError(C,{message:"页面初始化失败，请刷新页面重试"})}}),Tr([()=>d.meetingRoom,()=>d.contactName,()=>d.contactPhone,()=>d.description,()=>d.urgency,()=>c.state.selectedServices,()=>c.state.serviceQuantities,()=>c.state.serviceStatuses],()=>{clearTimeout(Hr.value),Hr.value=setTimeout(()=>{(W()>0||d.meetingRoom)&&h.saveDraft()},2e3)},{deep:!0});const Hr=ge(null),ui=()=>{try{const C=h.loadDraft();C?h.restoreDraft(C)?(u.showSuccess("草稿已恢复，请检查填写内容"),l.provide("success")):u.showError(new Error("恢复草稿失败")):u.showWarning("没有找到可恢复的草稿")}catch(C){console.error("恢复草稿失败:",C),u.showError(C,{message:"恢复草稿失败，请重试"})}},fi=()=>{y.value=!y.value,l.provide("light")},ci=rt(()=>{var C,w;return!!((C=d.contactName)!=null&&C.trim()||(w=d.contactPhone)!=null&&w.trim())}),li=()=>{var w,R;let C=0;return(w=d.contactName)!=null&&w.trim()&&C++,(R=d.contactPhone)!=null&&R.trim()&&C++,C},di=()=>{try{h.clearDraft(),u.showSuccess("草稿已清除"),l.provide("light")}catch(C){console.error("清除草稿失败:",C),u.showError(C,{message:"清除草稿失败，请重试"})}};return(C,w)=>{const R=xe("van-nav-bar"),V=xe("van-button"),ne=xe("van-field"),ie=xe("van-icon"),Fe=xe("van-cell-group"),je=xe("van-tag"),tt=xe("van-checkbox"),Mt=xe("van-stepper"),dt=xe("van-radio"),Z=xe("van-radio-group"),de=xe("van-cell"),De=xe("van-form");return re(),pe("div",nu,[U(R,{"left-text":"返回","left-arrow":"",onClickLeft:ti,fixed:""},{title:J(()=>[U(bi,{size:"small"})]),_:1}),F("div",iu,[U(De,{onSubmit:ai,ref_key:"formRef",ref:i},{default:J(()=>[U(Fe,{inset:""},{default:J(()=>[F("div",au,[U(ne,{modelValue:d.meetingRoom,"onUpdate:modelValue":w[1]||(w[1]=M=>d.meetingRoom=M),name:"meetingRoom",label:"会议室编号",placeholder:"请输入会议室编号，如A0101",clearable:"",onInput:ii,rules:[{required:!0,message:"请输入会议室编号"}]},{button:J(()=>[U(V,{size:"small",type:"default",onClick:w[0]||(w[0]=at(M=>v.value=!0,["stop"])),style:{"margin-right":"8px"}},{default:J(()=>w[13]||(w[13]=[Pe(" 选择 ")])),_:1,__:[13]}),U(V,{size:"small",type:"primary",onClick:at(qn,["stop"]),icon:"scan"},{default:J(()=>w[14]||(w[14]=[Pe(" 扫码 ")])),_:1,__:[14]})]),_:1},8,["modelValue"]),d.meetingRoom?We("",!0):(re(),pe("div",ou,[U(ie,{name:"info-o",size:"14",color:"#969799"}),w[15]||(w[15]=F("span",null,'可点击"选择"或"扫码"快速获取会议室编号',-1))]))])]),_:1}),U(Fe,{inset:""},{default:J(()=>[F("div",su,[F("div",uu,[F("div",fu,[U(ie,{name:"todo-list-o",size:"16"}),w[16]||(w[16]=Pe(" 请选择需要的服务 "))]),F("div",cu," 已选择 "+Ge(W())+" 项服务 ",1)]),W()===0?(re(),pe("div",lu,[F("div",du,[U(ie,{name:"fire-o",size:"16",color:"#ff976a"}),w[17]||(w[17]=F("span",null,"常用服务",-1))]),F("div",hu,[(re(!0),pe(Et,null,Ct(P(),M=>(re(),pe("div",{key:`quick-${M.categoryValue}-${M.value}`,class:"quick-select-item",onClick:fe=>H(M)},[U(ie,{name:G(M.categoryValue),size:"16"},null,8,["name"]),F("span",null,Ge(M.text),1)],8,vu))),128))])])):We("",!0),F("div",pu,[(re(!0),pe(Et,null,Ct(A.value,M=>(re(),pe("div",{key:M.value,class:"service-category enterprise-card haptic-feedback"},[F("div",{class:"category-header",onClick:fe=>S(M,fe)},[F("div",xu,[U(ie,{name:M.icon,size:"20"},null,8,["name"]),F("span",yu,Ge(M.text),1),D(M.value)>0?(re(),pe("span",_u,Ge(D(M.value)),1)):We("",!0),M.popular?(re(),_t(je,{key:1,type:"warning",size:"small",class:"popular-tag"},{default:J(()=>w[18]||(w[18]=[Pe(" 常用 ")])),_:1,__:[18]})):We("",!0)]),F("div",wu,[U(tt,{"model-value":T(M.value),"onUpdate:modelValue":fe=>se(M.value,fe),onClick:w[2]||(w[2]=at(()=>{},["stop"])),size:"18"},null,8,["model-value","onUpdate:modelValue"]),U(ie,{name:M.expanded?"arrow-up":"arrow-down",size:"16",color:"#969799",class:"expand-icon"},null,8,["name"])])],8,gu),M.expanded?(re(),pe("div",Au,[(re(!0),pe(Et,null,Ct(M.items,fe=>(re(),pe("div",{key:fe.value,class:"service-item-wrapper"},[F("div",{class:yr(["service-item",{selected:O(M.value,fe.value),popular:fe.popular}]),onClick:Ye=>b(M.value,fe.value,!O(M.value,fe.value))},[F("div",Eu,[U(tt,{"model-value":O(M.value,fe.value),"onUpdate:modelValue":Ye=>b(M.value,fe.value,Ye),onClick:w[3]||(w[3]=at(()=>{},["stop"])),size:"18"},null,8,["model-value","onUpdate:modelValue"]),F("span",mu,Ge(Yn(M.value,fe)),1),fe.popular?(re(),_t(je,{key:0,type:"primary",size:"small",class:"item-popular-tag"},{default:J(()=>w[19]||(w[19]=[Pe(" 热门 ")])),_:1,__:[19]})):We("",!0)]),F("div",Su,[M.value==="tea"&&O(M.value,fe.value)?(re(),pe("div",Iu,[U(Mt,{"model-value":Xr(kr)(M.value,fe.value),"onUpdate:modelValue":Ye=>Xr(Zn)(M.value,fe.value,Ye),min:"1",max:"30",integer:"","button-size":"22",onClick:w[4]||(w[4]=at(()=>{},["stop"]))},null,8,["model-value","onUpdate:modelValue"]),w[20]||(w[20]=F("span",{class:"quantity-unit"},"人",-1))])):We("",!0)])],10,Cu),wt((re(),pe("div",{class:"equipment-status",key:`status-${M.value}-${fe.value}`,onClick:w[6]||(w[6]=at(()=>{},["stop"]))},[We("",!0),U(Z,{"model-value":Lr(M.value,fe.value),"onUpdate:modelValue":Ye=>Ur(M.value,fe.value,Ye),direction:"horizontal",onClick:w[5]||(w[5]=at(()=>{},["stop"]))},{default:J(()=>[(re(!0),pe(Et,null,Ct(fe.statusOptions,Ye=>(re(),_t(dt,{key:Ye.value,name:Ye.value,onClick:at(sr=>Vr(M.value,fe.value,Ye.value,sr),["stop"]),onTouchstart:at(sr=>Vr(M.value,fe.value,Ye.value,sr),["stop"])},{default:J(()=>[Pe(Ge(Ye.text),1)]),_:2},1032,["name","onClick","onTouchstart"]))),128))]),_:2},1032,["model-value","onUpdate:modelValue"])])),[[At,M.value==="equipment"&&fe.hasStatus&&O(M.value,fe.value)]])]))),128))])):We("",!0)]))),128))])]),F("div",Ou,[F("div",Tu,[F("div",{class:"category-header",onClick:fi},[F("div",bu,[U(ie,{name:"contact",size:"20"}),w[21]||(w[21]=F("span",{class:"category-name"},"联系信息",-1)),ci.value?(re(),pe("span",Du,Ge(li()),1)):We("",!0)]),F("div",Ru,[U(ie,{name:y.value?"arrow-up":"arrow-down",size:"16",color:"#969799",class:"expand-icon",style:mi({transform:y.value?"rotate(180deg)":"rotate(0deg)"})},null,8,["name","style"])])]),wt(F("div",Nu,[U(ne,{modelValue:d.contactName,"onUpdate:modelValue":w[7]||(w[7]=M=>d.contactName=M),name:"contactName",label:"联系人",placeholder:"请输入联系人姓名（选填）",clearable:""},null,8,["modelValue"]),U(ne,{modelValue:d.contactPhone,"onUpdate:modelValue":w[8]||(w[8]=M=>d.contactPhone=M),name:"contactPhone",label:"联系电话",type:"tel",placeholder:"请输入联系电话（选填）",clearable:"",rules:[{validator:o.phone,message:s.phone}]},null,8,["modelValue","rules"])],512),[[At,y.value]])])]),U(ne,{modelValue:d.description,"onUpdate:modelValue":w[9]||(w[9]=M=>d.description=M),name:"description",label:"补充说明",type:"textarea",placeholder:"请详细描述您的需求（选填）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"]),U(ne,{name:"urgency",label:"紧急程度"},{input:J(()=>[U(Z,{modelValue:d.urgency,"onUpdate:modelValue":w[10]||(w[10]=M=>d.urgency=M),direction:"horizontal"},{default:J(()=>[U(dt,{name:"normal"},{default:J(()=>w[22]||(w[22]=[Pe("普通")])),_:1,__:[22]}),U(dt,{name:"urgent"},{default:J(()=>w[23]||(w[23]=[Pe("紧急")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),W()>0?(re(),_t(Fe,{key:0,inset:""},{default:J(()=>[U(de,{title:"已选服务",value:`${W()}项`},null,8,["value"])]),_:1})):We("",!0),Pt.value?(re(),_t(Fe,{key:1,inset:""},{default:J(()=>[U(de,{title:"草稿操作"},{"right-icon":J(()=>[U(V,{size:"small",type:"warning",onClick:ui,style:{"margin-right":"8px"}},{default:J(()=>w[24]||(w[24]=[Pe(" 恢复草稿 ")])),_:1,__:[24]}),U(V,{size:"small",type:"default",onClick:di},{default:J(()=>w[25]||(w[25]=[Pe(" 清除草稿 ")])),_:1,__:[25]})]),_:1})]),_:1})):We("",!0),F("div",Pu,[U(V,{type:"primary","native-type":"submit",block:"",round:"",loading:a.value,"loading-text":"提交中...",disabled:W()===0,class:"enterprise-button haptic-feedback",size:"large"},{default:J(()=>[Pe(" 提交需求 ("+Ge(W())+"项) ",1)]),_:1},8,["loading","disabled"])])]),_:1},512)]),U(ru,{show:v.value,"onUpdate:show":w[11]||(w[11]=M=>v.value=M),rooms:_.value,onSelect:ri},null,8,["show","rooms"]),U(k1,{show:g.value,"onUpdate:show":w[12]||(w[12]=M=>g.value=M),onScanSuccess:Qn,onScanError:Jn,onSwitchManual:ei},null,8,["show"]),U(Di,{variant:"minimal"})])}}},Vu=br(Mu,[["__scopeId","data-v-4ec165ee"]]);export{Vu as default};
