import{r as m,B as Qe,q as ee,w as de,C as Ge,c as k,o as p,b as n,n as He,a as o,l as s,d,v as We,s as y,u as Xe,t as f,x as T,A as w,p as _,F as te,z as ae,y as Ye,E as Ze,J as j,K as x}from"./index-DaPIm3IU.js";import{g as qe,c as et,u as ve}from"./api-CiHvhAh2.js";import{B as tt}from"./BrandFooter-EH36XjN5.js";import{_ as at}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as me}from"./function-call-T0USrQIg.js";const nt={class:"request-management"},st={class:"stats-section"},lt={class:"stat-number"},ot={class:"stat-number"},it={class:"stat-number"},ut={class:"stat-number"},ct={class:"filter-section"},rt={class:"request-list"},dt={class:"card-header"},vt={class:"card-status"},mt=["onClick"],pt={class:"main-info"},ft={class:"meeting-room"},gt={class:"service-info"},_t={class:"service-type"},yt={class:"service-detail"},ht={key:0,class:"contact-section"},wt={key:0,class:"contact-name"},kt={key:1,class:"contact-phone"},bt={class:"time-info"},Ct={class:"create-time"},Tt={key:0,class:"update-time"},xt={class:"card-actions"},St={key:0,class:"detail-popup"},Rt={class:"detail-content"},Dt={class:"contact-phone-detail"},Vt={class:"status-actions"},$t={class:"batch-status-popup"},Ut={class:"batch-content"},zt={class:"batch-actions-buttons"},Bt={__name:"RequestManagement",setup(Lt){Xe();const g=m([]),J=m(!1),K=m(!1),Q=m(!1),$=m(""),U=m(!1),i=m(null),z=m(""),G=m(!1),B=m(""),c=m([]),b=m(!1),H=m(!1),L=m(!1),S=m(""),N=m(""),W=m(!1),X=m("createTime"),M=Qe({page:1,limit:20}),P=new Map,O=ee(()=>{const t=g.value.filter(v=>v.status==="pending").length,e=g.value.filter(v=>v.status==="processing").length,l=g.value.filter(v=>v.status==="completed").length,u=g.value.length;return{pending:t,processing:e,completed:l,total:u}}),pe=[{text:"全部状态",value:""},{text:"待处理",value:"pending"},{text:"处理中",value:"processing"},{text:"已完成",value:"completed"},{text:"已取消",value:"cancelled"}],fe=[{text:"按创建时间",value:"createTime"},{text:"按更新时间",value:"updateTime"},{text:"按紧急程度",value:"urgency"},{text:"按状态",value:"status"}],ge=[{name:"批量状态更新",value:"updateStatus"},{name:"批量导出",value:"export"},{name:"批量删除",value:"delete",color:"#ee0a24"}],I=ee(()=>{let t=[...g.value];if(B.value){const e=B.value.toLowerCase();t=t.filter(l=>(l.meetingName||l.meetingRoom||"").toLowerCase().includes(e)||(l.serviceType||"").toLowerCase().includes(e)||(l.serviceDetail||"").toLowerCase().includes(e)||(l.contactName||"").toLowerCase().includes(e)||(l.contactPhone||"").includes(e))}return $.value&&(t=t.filter(e=>e.status===$.value)),t.sort((e,l)=>{switch(X.value){case"createTime":return new Date(l.createTime)-new Date(e.createTime);case"updateTime":return new Date(l.updateTime||l.createTime)-new Date(e.updateTime||e.createTime);case"urgency":return e.urgency==="urgent"&&l.urgency!=="urgent"?-1:e.urgency!=="urgent"&&l.urgency==="urgent"?1:new Date(l.createTime)-new Date(e.createTime);case"status":const u={pending:0,processing:1,completed:2,cancelled:3};return u[e.status]-u[l.status];default:return 0}}),t}),_e=ee(()=>c.value.length>0&&c.value.length<I.value.length);de(b,t=>{t?c.value=I.value.map(e=>e._id):c.value=[]}),de(c,t=>{t.length===0?b.value=!1:t.length===I.value.length&&(b.value=!0)},{deep:!0});const Y=async(t=!1)=>{try{t&&(M.page=1,g.value=[],K.value=!1,c.value=[]);const e={limit:M.limit,offset:(M.page-1)*M.limit},l=await qe(e);l.list&&l.list.length>0?(t?g.value=l.list:g.value.push(...l.list),M.page++,Be()):K.value=!0}catch(e){y({type:"fail",message:e.message||"加载失败"})}finally{J.value=!1,Q.value=!1}},ye=()=>{Y(!0)},he=()=>{Y()},we=()=>{},ke=()=>{B.value=""},A=t=>{$.value=t},be=()=>{},Ce=()=>{},Te=t=>{const e=c.value.indexOf(t);e>-1?c.value.splice(e,1):c.value.push(t)},xe=()=>{},Se=t=>{i.value=t,z.value="",U.value=!0},Re=t=>{const e=[];switch(t){case"pending":e.push({status:"processing",text:"开始处理",type:"primary"}),e.push({status:"completed",text:"直接完成",type:"success"});break;case"processing":e.push({status:"completed",text:"标记完成",type:"success"}),e.push({status:"pending",text:"退回待处理",type:"warning"});break;case"completed":e.push({status:"processing",text:"重新处理",type:"primary"});break}return e},De=()=>[{status:"pending",text:"待处理",type:"warning"},{status:"processing",text:"处理中",type:"primary"},{status:"completed",text:"已完成",type:"success"},{status:"cancelled",text:"已取消",type:"default"}],Ve=async(t,e)=>{await ne(t._id,e,"")},ne=async(t,e,l="")=>{try{j({message:"更新中...",forbidClick:!0}),await ve(t,e,l);const u=g.value.find(v=>v._id===t);u&&(u.status=e,u.updateTime=new Date().toISOString()),i.value&&i.value._id===t&&(i.value.status=e,i.value.updateTime=new Date().toISOString()),x(),y({type:"success",message:"状态更新成功"})}catch(u){x(),y({type:"fail",message:u.message||"更新失败"})}},D=t=>t?new Date(t).toLocaleString("zh-CN"):"",$e=t=>t?t.length===11?t.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):t:"",E=t=>({pending:"待处理",processing:"处理中",completed:"已完成",cancelled:"已取消"})[t]||"未知",Ue=t=>({pending:"clock-o",processing:"play-circle-o",completed:"checked",cancelled:"cross"})[t]||"clock-o",Z=t=>({pending:"#ff976a",processing:"#1989fa",completed:"#07c160",cancelled:"#969799"})[t]||"#969799",ze=t=>{if(t.status!=="pending")return!1;const e=new Date(t.createTime);return(new Date-e)/(1e3*60)>30},Be=()=>{P.forEach(t=>clearInterval(t)),P.clear(),g.value.forEach(t=>{if(t.status==="pending"){const e=new Date(t.createTime),u=(new Date-e)/(1e3*60);if(u>30)se(t);else{const v=(30-u)*60*1e3;setTimeout(()=>{t.status==="pending"&&se(t)},v)}}})},se=t=>{let e=0;const l=3,u=setInterval(async()=>{if(t.status!=="pending"||e>=l){clearInterval(u),P.delete(t._id);return}await Le(t),e++},30*60*1e3);P.set(t._id,u)},Le=async t=>{try{await et("serviceRequest",{action:"sendReminder",data:{requestId:t._id,meetingRoom:t.meetingName||t.meetingRoom,serviceType:t.serviceType,serviceDetail:t.serviceDetail}})}catch(e){console.error("发送提醒失败:",e)}},le=t=>{t&&(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)?window.location.href=`tel:${t}`:navigator.clipboard.writeText(t).then(()=>{y({type:"success",message:"电话号码已复制到剪贴板"})}).catch(()=>{y({message:`电话号码：${t}`})}))},Me=async t=>{!i.value||t===i.value.status||(await ne(i.value._id,t,z.value),U.value=!1,z.value="")},Ne=async t=>{if(c.value.length===0){y("请先选择需求");return}switch(t.value){case"updateStatus":L.value=!0;break;case"export":await Oe();break;case"delete":await Ie();break}},Pe=async()=>{if(!S.value){y("请选择目标状态");return}if(await me({title:"确认批量更新",message:`确定要将选中的 ${c.value.length} 个需求状态更新为"${E(S.value)}"吗？`}).catch(()=>!1))try{W.value=!0,j({message:"批量更新中...",forbidClick:!0});const e=c.value.map(l=>ve(l,S.value,N.value));await Promise.all(e),c.value.forEach(l=>{const u=g.value.find(v=>v._id===l);u&&(u.status=S.value,u.updateTime=new Date().toISOString())}),x(),y({type:"success",message:`成功更新 ${c.value.length} 个需求状态`}),c.value=[],b.value=!1,L.value=!1,S.value="",N.value=""}catch(e){x(),y({type:"fail",message:e.message||"批量更新失败"})}finally{W.value=!1}},Oe=async()=>{try{j({message:"导出中...",forbidClick:!0});const t=g.value.filter(r=>c.value.includes(r._id)),e={exportTime:new Date().toISOString(),totalCount:t.length,data:t.map(r=>({会议室:r.meetingName||r.meetingRoom,服务类型:r.serviceType,具体需求:r.serviceDetail,联系人:r.contactName||"",联系电话:r.contactPhone||"",状态:E(r.status),紧急程度:r.urgency==="urgent"?"紧急":"普通",提交时间:D(r.createTime),更新时间:r.updateTime?D(r.updateTime):"",补充说明:r.description||""}))},l=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),u=URL.createObjectURL(l),v=document.createElement("a");v.href=u,v.download=`需求管理-批量导出-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(v),v.click(),document.body.removeChild(v),URL.revokeObjectURL(u),x(),y({type:"success",message:"导出成功"}),c.value=[],b.value=!1}catch(t){x(),y({type:"fail",message:t.message||"导出失败"})}},Ie=async()=>{if(await me({title:"危险操作",message:`确定要删除选中的 ${c.value.length} 个需求吗？此操作不可撤销！`,confirmButtonText:"确认删除",confirmButtonColor:"#ee0a24"}).catch(()=>!1))try{j({message:"删除中...",forbidClick:!0}),g.value=g.value.filter(e=>!c.value.includes(e._id)),x(),y({type:"success",message:`成功删除 ${c.value.length} 个需求`}),c.value=[],b.value=!1}catch(e){x(),y({type:"fail",message:e.message||"删除失败"})}};return Ge(()=>{Y(!0)}),(t,e)=>{const l=d("van-icon"),u=d("van-nav-bar"),v=d("van-search"),r=d("van-col"),oe=d("van-row"),ie=d("van-dropdown-item"),Ae=d("van-dropdown-menu"),ue=d("van-checkbox"),V=d("van-button"),F=d("van-tag"),Ee=d("van-badge"),Fe=d("van-list"),je=d("van-pull-refresh"),h=d("van-cell"),R=d("van-cell-group"),ce=d("van-field"),re=d("van-popup"),Je=d("van-action-sheet"),q=d("van-radio"),Ke=d("van-radio-group");return p(),k("div",nt,[n(u,{title:"需求管理","left-text":"返回","left-arrow":"",onClickLeft:e[1]||(e[1]=a=>t.$router.back())},{right:s(()=>[n(l,{name:"search",size:"18",onClick:e[0]||(e[0]=a=>G.value=!G.value)})]),_:1}),He(n(v,{modelValue:B.value,"onUpdate:modelValue":e[2]||(e[2]=a=>B.value=a),placeholder:"搜索会议室、服务类型或联系人",onSearch:we,onClear:ke,background:"#f7f8fa"},null,8,["modelValue"]),[[We,G.value]]),o("div",st,[n(oe,{gutter:"12"},{default:s(()=>[n(r,{span:"6"},{default:s(()=>[o("div",{class:"stat-card pending",onClick:e[3]||(e[3]=a=>A("pending"))},[o("div",lt,f(O.value.pending),1),e[22]||(e[22]=o("div",{class:"stat-label"},"待处理",-1))])]),_:1}),n(r,{span:"6"},{default:s(()=>[o("div",{class:"stat-card processing",onClick:e[4]||(e[4]=a=>A("processing"))},[o("div",ot,f(O.value.processing),1),e[23]||(e[23]=o("div",{class:"stat-label"},"处理中",-1))])]),_:1}),n(r,{span:"6"},{default:s(()=>[o("div",{class:"stat-card completed",onClick:e[5]||(e[5]=a=>A("completed"))},[o("div",it,f(O.value.completed),1),e[24]||(e[24]=o("div",{class:"stat-label"},"已完成",-1))])]),_:1}),n(r,{span:"6"},{default:s(()=>[o("div",{class:"stat-card total",onClick:e[6]||(e[6]=a=>A(""))},[o("div",ut,f(O.value.total),1),e[25]||(e[25]=o("div",{class:"stat-label"},"总计",-1))])]),_:1})]),_:1})]),o("div",ct,[n(oe,{align:"center"},{default:s(()=>[n(r,{span:"12"},{default:s(()=>[n(Ae,null,{default:s(()=>[n(ie,{modelValue:$.value,"onUpdate:modelValue":e[7]||(e[7]=a=>$.value=a),options:pe,onChange:be},null,8,["modelValue"]),n(ie,{modelValue:X.value,"onUpdate:modelValue":e[8]||(e[8]=a=>X.value=a),options:fe,onChange:Ce},null,8,["modelValue"])]),_:1})]),_:1}),n(r,{span:"12",class:"batch-actions"},{default:s(()=>[n(ue,{modelValue:b.value,"onUpdate:modelValue":e[9]||(e[9]=a=>b.value=a),onChange:xe,indeterminate:_e.value},{default:s(()=>e[26]||(e[26]=[_(" 全选 ")])),_:1,__:[26]},8,["modelValue","indeterminate"]),c.value.length>0?(p(),T(V,{key:0,type:"primary",size:"mini",onClick:e[10]||(e[10]=a=>H.value=!0)},{default:s(()=>[_(" 批量操作("+f(c.value.length)+") ",1)]),_:1})):w("",!0)]),_:1})]),_:1})]),n(je,{modelValue:Q.value,"onUpdate:modelValue":e[12]||(e[12]=a=>Q.value=a),onRefresh:ye},{default:s(()=>[n(Fe,{loading:J.value,"onUpdate:loading":e[11]||(e[11]=a=>J.value=a),finished:K.value,"finished-text":"没有更多了",onLoad:he},{default:s(()=>[o("div",rt,[(p(!0),k(te,null,ae(I.value,a=>(p(),k("div",{key:a._id,class:Ye(["request-card",{selected:c.value.includes(a._id)}])},[o("div",dt,[n(ue,{"model-value":c.value.includes(a._id),"onUpdate:modelValue":C=>Te(a._id)},null,8,["model-value","onUpdate:modelValue"]),o("div",vt,[n(F,{color:Z(a.status),plain:"",size:"small"},{default:s(()=>[_(f(E(a.status)),1)]),_:2},1032,["color"]),a.status==="pending"&&ze(a)?(p(),T(Ee,{key:0,dot:"",color:"#ff4d4f"})):w("",!0)])]),o("div",{class:"card-content",onClick:C=>Se(a)},[o("div",pt,[o("div",ft,[n(l,{name:"location-o",size:"14",color:"#969799"}),o("span",null,f(a.meetingName||a.meetingRoom),1),a.urgency==="urgent"?(p(),T(F,{key:0,type:"danger",size:"small",class:"urgent-tag"},{default:s(()=>e[27]||(e[27]=[_(" 紧急 ")])),_:1,__:[27]})):w("",!0)]),o("div",gt,[o("span",_t,f(a.serviceType),1),o("span",yt,f(a.serviceDetail),1)])]),a.contactName||a.contactPhone?(p(),k("div",ht,[a.contactName?(p(),k("div",wt,[n(l,{name:"contact",size:"12",color:"#969799"}),o("span",null,f(a.contactName),1)])):w("",!0),a.contactPhone?(p(),k("div",kt,[n(l,{name:"phone-o",size:"12",color:"#1989fa"}),o("span",null,f($e(a.contactPhone)),1),n(V,{type:"primary",size:"mini",round:"",onClick:Ze(C=>le(a.contactPhone),["stop"])},{default:s(()=>e[28]||(e[28]=[_(" 拨号 ")])),_:2,__:[28]},1032,["onClick"])])):w("",!0)])):w("",!0),o("div",bt,[o("span",Ct,f(D(a.createTime)),1),a.updateTime&&a.updateTime!==a.createTime?(p(),k("span",Tt," 更新："+f(D(a.updateTime)),1)):w("",!0)])],8,mt),o("div",xt,[(p(!0),k(te,null,ae(Re(a.status),C=>(p(),T(V,{key:C.status,type:C.type,size:"mini",round:"",onClick:Mt=>Ve(a,C.status)},{default:s(()=>[_(f(C.text),1)]),_:2},1032,["type","onClick"]))),128))])],2))),128))])]),_:1},8,["loading","finished"])]),_:1},8,["modelValue"]),n(re,{show:U.value,"onUpdate:show":e[16]||(e[16]=a=>U.value=a),position:"bottom",style:{height:"80%"},round:"",closeable:""},{default:s(()=>[i.value?(p(),k("div",St,[n(u,{title:"需求详情","left-text":"关闭",onClickLeft:e[13]||(e[13]=a=>U.value=!1)}),o("div",Rt,[n(R,{inset:"",title:"基本信息"},{default:s(()=>[n(h,{title:"会议室",value:i.value.meetingName||i.value.meetingRoom},{icon:s(()=>[n(l,{name:"location-o",color:"#1989fa"})]),_:1},8,["value"]),n(h,{title:"服务类型",value:i.value.serviceType},{icon:s(()=>[n(l,{name:"service",color:"#07c160"})]),_:1},8,["value"]),n(h,{title:"具体需求",value:i.value.serviceDetail},{icon:s(()=>[n(l,{name:"description",color:"#ff976a"})]),_:1},8,["value"]),n(h,{title:"紧急程度"},{icon:s(()=>[n(l,{name:"warning-o",color:i.value.urgency==="urgent"?"#ee0a24":"#969799"},null,8,["color"])]),value:s(()=>[n(F,{type:i.value.urgency==="urgent"?"danger":"default",size:"small"},{default:s(()=>[_(f(i.value.urgency==="urgent"?"紧急":"普通"),1)]),_:1},8,["type"])]),_:1}),i.value.description?(p(),T(h,{key:0,title:"补充说明",value:i.value.description},{icon:s(()=>[n(l,{name:"notes-o",color:"#969799"})]),_:1},8,["value"])):w("",!0)]),_:1}),n(R,{inset:"",title:"联系信息"},{default:s(()=>[n(h,{title:"联系人",value:i.value.contactName||"未填写"},{icon:s(()=>[n(l,{name:"contact",color:"#1989fa"})]),_:1},8,["value"]),n(h,{title:"联系电话"},{icon:s(()=>[n(l,{name:"phone-o",color:"#1989fa"})]),value:s(()=>[o("div",Dt,[o("span",null,f(i.value.contactPhone||"未填写"),1),i.value.contactPhone?(p(),T(V,{key:0,type:"primary",size:"mini",round:"",onClick:e[14]||(e[14]=a=>le(i.value.contactPhone))},{default:s(()=>[n(l,{name:"phone-o",size:"12"}),e[29]||(e[29]=_(" 拨号 "))]),_:1,__:[29]})):w("",!0)])]),_:1})]),_:1}),n(R,{inset:"",title:"状态信息"},{default:s(()=>[n(h,{title:"当前状态"},{icon:s(()=>[n(l,{name:Ue(i.value.status),color:Z(i.value.status)},null,8,["name","color"])]),value:s(()=>[n(F,{color:Z(i.value.status),size:"small"},{default:s(()=>[_(f(E(i.value.status)),1)]),_:1},8,["color"])]),_:1}),n(h,{title:"提交时间",value:D(i.value.createTime)},{icon:s(()=>[n(l,{name:"clock-o",color:"#969799"})]),_:1},8,["value"]),i.value.updateTime&&i.value.updateTime!==i.value.createTime?(p(),T(h,{key:0,title:"更新时间",value:D(i.value.updateTime)},{icon:s(()=>[n(l,{name:"edit",color:"#969799"})]),_:1},8,["value"])):w("",!0)]),_:1}),n(R,{inset:"",title:"状态操作"},{default:s(()=>[o("div",Vt,[(p(!0),k(te,null,ae(De(),a=>(p(),T(V,{key:a.status,type:a.status===i.value.status?"default":a.type,disabled:a.status===i.value.status,size:"small",round:"",onClick:C=>Me(a.status)},{default:s(()=>[_(f(a.text),1)]),_:2},1032,["type","disabled","onClick"]))),128))])]),_:1}),n(R,{inset:"",title:"添加备注"},{default:s(()=>[n(ce,{modelValue:z.value,"onUpdate:modelValue":e[15]||(e[15]=a=>z.value=a),type:"textarea",placeholder:"请输入处理备注（可选）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})])])):w("",!0)]),_:1},8,["show"]),n(Je,{show:H.value,"onUpdate:show":e[17]||(e[17]=a=>H.value=a),actions:ge,"cancel-text":"取消",onSelect:Ne},null,8,["show"]),n(re,{show:L.value,"onUpdate:show":e[21]||(e[21]=a=>L.value=a),position:"bottom",round:"",closeable:""},{default:s(()=>[o("div",$t,[n(u,{title:"批量状态更新","left-text":"取消",onClickLeft:e[18]||(e[18]=a=>L.value=!1)}),o("div",Ut,[n(R,{inset:""},{default:s(()=>[n(h,{title:"选中项目",value:`${c.value.length} 个需求`},null,8,["value"]),n(h,{title:"目标状态"},{value:s(()=>[n(Ke,{modelValue:S.value,"onUpdate:modelValue":e[19]||(e[19]=a=>S.value=a),direction:"horizontal"},{default:s(()=>[n(q,{name:"pending"},{default:s(()=>e[30]||(e[30]=[_("待处理")])),_:1,__:[30]}),n(q,{name:"processing"},{default:s(()=>e[31]||(e[31]=[_("处理中")])),_:1,__:[31]}),n(q,{name:"completed"},{default:s(()=>e[32]||(e[32]=[_("已完成")])),_:1,__:[32]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(R,{inset:"",title:"批量备注"},{default:s(()=>[n(ce,{modelValue:N.value,"onUpdate:modelValue":e[20]||(e[20]=a=>N.value=a),type:"textarea",placeholder:"请输入批量处理备注（可选）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),o("div",zt,[n(V,{type:"primary",block:"",round:"",loading:W.value,onClick:Pe},{default:s(()=>e[33]||(e[33]=[_(" 确认更新 ")])),_:1,__:[33]},8,["loading"])])])])]),_:1},8,["show"]),n(tt,{variant:"minimal","light-background":!0})])}}},Et=at(Bt,[["__scopeId","data-v-abf40251"]]);export{Et as default};
