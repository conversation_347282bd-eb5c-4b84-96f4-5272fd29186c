import{i as a,e as n,m as r,f as i,b as c,g as u,L as f}from"./index-DaPIm3IU.js";let l;const m={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1};let p=n({},m);function d(){({instance:l}=r({setup(){const{state:o,toggle:t}=i();return()=>c(f,u(o,{"onUpdate:show":t}),null)}}))}function C(e){return a?new Promise((o,t)=>{l||d(),l.open(n({},p,e,{callback:s=>{(s==="confirm"?o:t)(s)}}))}):Promise.resolve(void 0)}const B=e=>C(n({showCancelButton:!0},e));export{B as a,C as s};
