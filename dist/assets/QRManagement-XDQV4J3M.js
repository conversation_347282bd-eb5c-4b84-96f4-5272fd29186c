import{r as d,q as ie,C as re,s as r,c as p,o as u,b as a,a as l,l as n,d as s,A as G,x as K,t as _,F as J,z as j,p as x,y as ce,E as h,J as H,_ as de,K as D}from"./index-DaPIm3IU.js";import{c as k}from"./api-CiHvhAh2.js";import{A as ue}from"./AppLogo-BC2bg_K7.js";import{B as me}from"./BrandFooter-EH36XjN5.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as W}from"./function-call-T0USrQIg.js";const pe={class:"qr-management"},_e={class:"content"},ge={class:"search-section"},be={class:"filter-row"},fe={class:"view-toggle"},he={class:"action-section"},Ce={class:"action-header"},we={class:"generate-button"},ye={class:"qr-list-section"},ke={class:"section-header"},$e={key:0,class:"batch-actions"},Ve={key:0,class:"qr-grid"},Ne=["onClick"],Re={class:"qr-header"},Qe={class:"room-number"},Be={class:"qr-actions"},Ue={class:"qr-preview"},Te=["src","alt"],xe={class:"qr-info"},De={class:"qr-location"},Fe={class:"qr-stats"},Le={class:"qr-time"},Ae={class:"list-actions"},ze={key:0,class:"empty-state"},Ge={class:"batch-form"},Me={class:"batch-tip"},Se={key:0,class:"preview-content"},Ee={class:"preview-qr"},qe=["src","alt"],Ie={class:"preview-info"},Oe={__name:"QRManagement",setup(Pe){const M=d([]),$=d(""),V=d("all"),N=d("grid"),c=d([]),F=d(!1),C=d(!1),L=d(!1),i=d(null),m=d({roomNumber:"",description:""}),g=d({building:"",floor:""}),R=ie(()=>{let t=M.value;if(V.value!=="all"&&(t=t.filter(e=>e.building===V.value)),$.value){const e=$.value.toLowerCase();t=t.filter(B=>B.roomNumber.toLowerCase().includes(e))}return t});re(()=>{w()});const w=async()=>{try{const t=await k("qrCodeGenerator",{action:"getQRCodeList",data:{limit:100}});M.value=t.data.list||[]}catch{r("加载二维码列表失败")}},X=async()=>{if(!m.value.roomNumber){r("请输入会议室编号");return}try{F.value=!0,H({message:"生成中...",forbidClick:!0});const e=`https://huiyi-7gudxwsnf4ca4b67-1256836374.tcloudbaseapp.com/dist/#/form?roomNumber=${encodeURIComponent(m.value.roomNumber)}`,z=await(await de(()=>import("./browser-BJzWpxwN.js").then(y=>y.b),[],import.meta.url)).toDataURL(e,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});await k("qrCodeGenerator",{action:"saveQRCode",data:{roomNumber:m.value.roomNumber,qrUrl:e,qrCodeBase64:z.split(",")[1],description:m.value.description}}),D(),r({type:"success",message:"生成成功！"}),m.value={roomNumber:"",description:""},w()}catch(t){D(),r({type:"fail",message:t.message||"生成失败"})}finally{F.value=!1}},Y=async()=>{try{H({message:"批量生成中...",forbidClick:!0});const t=await k("qrCodeGenerator",{action:"batchGenerateQRCodes",data:g.value});D(),r({type:"success",message:t.message}),C.value=!1,g.value={building:"",floor:""},w()}catch(t){D(),r({type:"fail",message:t.message||"批量生成失败"})}},S=async t=>{try{await W({title:"确认删除",message:`确定要删除 ${t.roomNumber} 的二维码吗？`}),await k("qrCodeGenerator",{action:"deleteQRCode",data:{id:t._id}}),r({type:"success",message:"删除成功"}),w()}catch(e){e!=="cancel"&&r({type:"fail",message:"删除失败"})}},Z=async()=>{try{await W({title:"确认删除",message:`确定要删除选中的 ${c.value.length} 个二维码吗？`}),await k("qrCodeGenerator",{action:"batchDeleteQRCodes",data:{ids:c.value}}),r({type:"success",message:"批量删除成功"}),c.value=[],w()}catch(t){t!=="cancel"&&r({type:"fail",message:"批量删除失败"})}},A=t=>{try{const e=document.createElement("a");e.download=`${t.roomNumber}-二维码-${new Date().getTime()}.png`,e.href=`data:image/png;base64,${t.qrCodeBase64}`,e.click(),r({type:"success",message:"下载成功"})}catch{r({type:"fail",message:"下载失败"})}},E=t=>{i.value=t,L.value=!0},ee=t=>{const e=c.value.indexOf(t);e>-1?c.value.splice(e,1):c.value.push(t)},q=()=>{},oe=()=>{},te=()=>{N.value=N.value==="grid"?"list":"grid"},Q=t=>t?new Date(t).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"";return(t,e)=>{var P;const B=s("van-nav-bar"),z=s("van-search"),y=s("van-tab"),ae=s("van-tabs"),b=s("van-icon"),U=s("van-button"),T=s("van-field"),I=s("van-cell-group"),le=s("van-checkbox"),f=s("van-cell"),ne=s("van-empty"),se=s("van-notice-bar"),O=s("van-dialog");return u(),p("div",pe,[a(B,{title:"二维码管理","left-arrow":"",onClickLeft:e[0]||(e[0]=o=>t.$router.back())},{right:n(()=>[a(ue,{size:"small"})]),_:1}),l("div",_e,[l("div",ge,[a(z,{modelValue:$.value,"onUpdate:modelValue":e[1]||(e[1]=o=>$.value=o),placeholder:"搜索会议室编号",onSearch:q,onClear:q},null,8,["modelValue"]),l("div",be,[a(ae,{active:V.value,"onUpdate:active":e[2]||(e[2]=o=>V.value=o),onChange:oe},{default:n(()=>[a(y,{title:"全部",name:"all"}),a(y,{title:"A栋",name:"A"}),a(y,{title:"B栋",name:"B"})]),_:1},8,["active"]),l("div",fe,[a(b,{name:N.value==="grid"?"apps-o":"bars",onClick:te,class:"toggle-icon"},null,8,["name"])])])]),l("div",he,[l("div",Ce,[e[15]||(e[15]=l("h3",null,"二维码生成",-1)),a(U,{type:"primary",size:"small",onClick:e[3]||(e[3]=o=>C.value=!0),icon:"plus"},{default:n(()=>e[14]||(e[14]=[x(" 批量生成 ")])),_:1,__:[14]})]),a(I,{inset:""},{default:n(()=>[a(T,{modelValue:m.value.roomNumber,"onUpdate:modelValue":e[4]||(e[4]=o=>m.value.roomNumber=o),label:"会议室编号",placeholder:"如: A0101",required:""},null,8,["modelValue"]),a(T,{modelValue:m.value.description,"onUpdate:modelValue":e[5]||(e[5]=o=>m.value.description=o),label:"描述",placeholder:"可选"},null,8,["modelValue"]),l("div",we,[a(U,{type:"primary",block:"",onClick:X,loading:F.value},{default:n(()=>e[16]||(e[16]=[x(" 生成二维码 ")])),_:1,__:[16]},8,["loading"])])]),_:1})]),l("div",ye,[l("div",ke,[l("h3",null,"已生成二维码 ("+_(R.value.length)+")",1),c.value.length>0?(u(),p("div",$e,[a(U,{type:"danger",size:"small",onClick:Z,icon:"delete-o"},{default:n(()=>[x(" 删除选中("+_(c.value.length)+") ",1)]),_:1})])):G("",!0)]),N.value==="grid"?(u(),p("div",Ve,[(u(!0),p(J,null,j(R.value,o=>(u(),p("div",{key:o._id,class:ce(["qr-card",{selected:c.value.includes(o._id)}]),onClick:v=>ee(o._id)},[l("div",Re,[l("span",Qe,_(o.roomNumber),1),l("div",Be,[a(b,{name:"eye-o",onClick:h(v=>E(o),["stop"]),class:"action-icon"},null,8,["onClick"]),a(b,{name:"download",onClick:h(v=>A(o),["stop"]),class:"action-icon"},null,8,["onClick"]),a(b,{name:"delete-o",onClick:h(v=>S(o),["stop"]),class:"action-icon delete"},null,8,["onClick"])])]),l("div",Ue,[l("img",{src:`data:image/png;base64,${o.qrCodeBase64}`,alt:o.roomNumber,class:"qr-image"},null,8,Te)]),l("div",xe,[l("div",De,_(o.building)+"栋 "+_(o.floor)+"楼",1),l("div",Fe,"扫描 "+_(o.scanCount||0)+" 次",1),l("div",Le,_(Q(o.updateTime)),1)])],10,Ne))),128))])):(u(),K(I,{key:1,inset:""},{default:n(()=>[(u(!0),p(J,null,j(R.value,o=>(u(),K(f,{key:o._id,title:o.roomNumber,label:`${o.building}栋 ${o.floor}楼 | 扫描${o.scanCount||0}次`,value:Q(o.updateTime),"is-link":"",onClick:v=>E(o)},{icon:n(()=>[a(le,{modelValue:c.value,"onUpdate:modelValue":e[6]||(e[6]=v=>c.value=v),name:o._id,onClick:e[7]||(e[7]=h(()=>{},["stop"]))},null,8,["modelValue","name"])]),"right-icon":n(()=>[l("div",Ae,[a(b,{name:"download",onClick:h(v=>A(o),["stop"])},null,8,["onClick"]),a(b,{name:"delete-o",onClick:h(v=>S(o),["stop"])},null,8,["onClick"])])]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1}))]),R.value.length===0?(u(),p("div",ze,[a(ne,{image:"search",description:"暂无二维码数据"},{default:n(()=>[a(U,{type:"primary",size:"small",onClick:e[8]||(e[8]=o=>C.value=!0)},{default:n(()=>e[17]||(e[17]=[x(" 立即生成 ")])),_:1,__:[17]})]),_:1})])):G("",!0)]),a(O,{show:C.value,"onUpdate:show":e[11]||(e[11]=o=>C.value=o),title:"批量生成二维码","show-cancel-button":"",onConfirm:Y},{default:n(()=>[l("div",Ge,[a(T,{modelValue:g.value.building,"onUpdate:modelValue":e[9]||(e[9]=o=>g.value.building=o),label:"楼栋",placeholder:"如: A (留空生成全部)"},null,8,["modelValue"]),a(T,{modelValue:g.value.floor,"onUpdate:modelValue":e[10]||(e[10]=o=>g.value.floor=o),label:"楼层",placeholder:"如: 8 (留空生成全部)",type:"number"},null,8,["modelValue"]),l("div",Me,[a(se,{"left-icon":"info-o",text:"将为符合条件的所有会议室生成二维码"})])])]),_:1},8,["show"]),a(O,{show:L.value,"onUpdate:show":e[12]||(e[12]=o=>L.value=o),title:`${(P=i.value)==null?void 0:P.roomNumber} 二维码`,"show-cancel-button":"","confirm-button-text":"下载",onConfirm:e[13]||(e[13]=o=>A(i.value))},{default:n(()=>[i.value?(u(),p("div",Se,[l("div",Ee,[l("img",{src:`data:image/png;base64,${i.value.qrCodeBase64}`,alt:i.value.roomNumber,class:"preview-image"},null,8,qe)]),l("div",Ie,[a(f,{title:"会议室",value:i.value.roomNumber},null,8,["value"]),a(f,{title:"位置",value:`${i.value.building}栋 ${i.value.floor}楼`},null,8,["value"]),a(f,{title:"扫描次数",value:`${i.value.scanCount||0} 次`},null,8,["value"]),a(f,{title:"生成时间",value:Q(i.value.createTime)},null,8,["value"]),a(f,{title:"更新时间",value:Q(i.value.updateTime)},null,8,["value"])])])):G("",!0)]),_:1},8,["show","title"]),a(me,{variant:"minimal","light-background":!0})])}}},Ye=ve(Oe,[["__scopeId","data-v-62123556"]]);export{Ye as default};
