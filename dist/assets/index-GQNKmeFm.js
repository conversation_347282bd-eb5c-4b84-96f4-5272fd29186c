const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Home-CN1eZbly.js","./AppLogo-BaVronrj.js","./_plugin-vue_export-helper-DlAUqK2U.js","./AppLogo-0J7b9OMs.css","./BrandFooter-CIZu14VB.js","./BrandFooter-1COmKiMA.css","./Home-CGzHvGvJ.css","./MultiServiceForm-DESGZTx4.js","./api-CiHvhAh2.js","./MultiServiceForm-DsO1iQOj.css","./MultiServiceSuccess-DobUl1Dw.js","./MultiServiceSuccess-DkvAx6Oi.css","./QRCode-DtF6vB9P.js","./browser-BJzWpxwN.js","./QRCode-BErpsBnD.css","./Admin-B3aH8-aY.js","./function-call-CabHCnJg.js","./Admin-CPaJu_J9.css","./RoomManagement-DlR6LsDb.js","./RoomManagement-4PWv8jee.css","./QRManagement-UOe5BI0g.js","./QRManagement-BK0oyu2Y.css","./RequestManagement-DUiR35py.js","./RequestManagement-B-ibm4ra.css","./Settings-CCWDUgDf.js","./Settings-DGG-R2an.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))o(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(a){if(a.ep)return;a.ep=!0;const i=n(a);fetch(a.href,i)}})();const Lh="modulepreload",Vh=function(e,t){return new URL(e,t).href},vs={},En=function(t,n,o){let a=Promise.resolve();if(n&&n.length>0){const l=document.getElementsByTagName("link"),r=document.querySelector("meta[property=csp-nonce]"),s=(r==null?void 0:r.nonce)||(r==null?void 0:r.getAttribute("nonce"));a=Promise.allSettled(n.map(c=>{if(c=Vh(c,o),c in vs)return;vs[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(!!o)for(let v=l.length-1;v>=0;v--){const p=l[v];if(p.href===c&&(!u||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${d}`))return;const m=document.createElement("link");if(m.rel=u?"stylesheet":Lh,u||(m.as="script"),m.crossOrigin="",m.href=c,s&&m.setAttribute("nonce",s),document.head.appendChild(m),u)return new Promise((v,p)=>{m.addEventListener("load",v),m.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(l){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=l,window.dispatchEvent(r),!r.defaultPrevented)throw l}return a.then(l=>{for(const r of l||[])r.status==="rejected"&&i(r.reason);return t().catch(i)})};/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function xr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Be={},Io=[],nn=()=>{},Fh=()=>!1,Ii=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Sr=e=>e.startsWith("onUpdate:"),Qe=Object.assign,Cr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Nh=Object.prototype.hasOwnProperty,Re=(e,t)=>Nh.call(e,t),be=Array.isArray,Ro=e=>Ri(e)==="[object Map]",Tu=e=>Ri(e)==="[object Set]",pe=e=>typeof e=="function",He=e=>typeof e=="string",qn=e=>typeof e=="symbol",Fe=e=>e!==null&&typeof e=="object",_u=e=>(Fe(e)||pe(e))&&pe(e.then)&&pe(e.catch),ku=Object.prototype.toString,Ri=e=>ku.call(e),zh=e=>Ri(e).slice(8,-1),Eu=e=>Ri(e)==="[object Object]",Tr=e=>He(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ua=xr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Di=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Hh=/-(\w)/g,$t=Di(e=>e.replace(Hh,(t,n)=>n?n.toUpperCase():"")),jh=/\B([A-Z])/g,Pn=Di(e=>e.replace(jh,"-$1").toLowerCase()),Bi=Di(e=>e.charAt(0).toUpperCase()+e.slice(1)),sl=Di(e=>e?`on${Bi(e)}`:""),Un=(e,t)=>!Object.is(e,t),cl=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Pu=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},Wh=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Uh=e=>{const t=He(e)?Number(e):NaN;return isNaN(t)?e:t};let gs;const Mi=()=>gs||(gs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Li(e){if(be(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],a=He(o)?Gh(o):Li(o);if(a)for(const i in a)t[i]=a[i]}return t}else if(He(e)||Fe(e))return e}const Yh=/;(?![^(]*\))/g,Kh=/:([^]+)/,qh=/\/\*[^]*?\*\//g;function Gh(e){const t={};return e.replace(qh,"").split(Yh).forEach(n=>{if(n){const o=n.split(Kh);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function Xh(e){if(!e)return"";if(He(e))return e;let t="";for(const n in e){const o=e[n];if(He(o)||typeof o=="number"){const a=n.startsWith("--")?n:Pn(n);t+=`${a}:${o};`}}return t}function Vi(e){let t="";if(He(e))t=e;else if(be(e))for(let n=0;n<e.length;n++){const o=Vi(e[n]);o&&(t+=o+" ")}else if(Fe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Zh="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Jh=xr(Zh);function $u(e){return!!e||e===""}const Au=e=>!!(e&&e.__v_isRef===!0),Qh=e=>He(e)?e:e==null?"":be(e)||Fe(e)&&(e.toString===ku||!pe(e.toString))?Au(e)?Qh(e.value):JSON.stringify(e,Ou,2):String(e),Ou=(e,t)=>Au(t)?Ou(e,t.value):Ro(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[o,a],i)=>(n[ul(o,i)+" =>"]=a,n),{})}:Tu(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ul(n))}:qn(t)?ul(t):Fe(t)&&!be(t)&&!Eu(t)?String(t):t,ul=(e,t="")=>{var n;return qn(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let yt;class em{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=yt,!t&&yt&&(this.index=(yt.scopes||(yt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=yt;try{return yt=this,t()}finally{yt=n}}}on(){++this._on===1&&(this.prevScope=yt,yt=this)}off(){this._on>0&&--this._on===0&&(yt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(this.effects.length=0,n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const a=this.parent.scopes.pop();a&&a!==this&&(this.parent.scopes[this.index]=a,a.index=this.index)}this.parent=void 0}}}function tm(){return yt}let Le;const dl=new WeakSet;class Iu{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,yt&&yt.active&&yt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,dl.has(this)&&(dl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Du(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,bs(this),Bu(this);const t=Le,n=Mt;Le=this,Mt=!0;try{return this.fn()}finally{Mu(this),Le=t,Mt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Er(t);this.deps=this.depsTail=void 0,bs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?dl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ul(this)&&this.run()}get dirty(){return Ul(this)}}let Ru=0,da,fa;function Du(e,t=!1){if(e.flags|=8,t){e.next=fa,fa=e;return}e.next=da,da=e}function _r(){Ru++}function kr(){if(--Ru>0)return;if(fa){let t=fa;for(fa=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;da;){let t=da;for(da=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=n}}if(e)throw e}function Bu(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Mu(e){let t,n=e.depsTail,o=n;for(;o;){const a=o.prevDep;o.version===-1?(o===n&&(n=a),Er(o),nm(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=a}e.deps=t,e.depsTail=n}function Ul(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Lu(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Lu(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Sa)||(e.globalVersion=Sa,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Ul(e))))return;e.flags|=2;const t=e.dep,n=Le,o=Mt;Le=e,Mt=!0;try{Bu(e);const a=e.fn(e._value);(t.version===0||Un(a,e._value))&&(e.flags|=128,e._value=a,t.version++)}catch(a){throw t.version++,a}finally{Le=n,Mt=o,Mu(e),e.flags&=-3}}function Er(e,t=!1){const{dep:n,prevSub:o,nextSub:a}=e;if(o&&(o.nextSub=a,e.prevSub=void 0),a&&(a.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Er(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function nm(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Mt=!0;const Vu=[];function Sn(){Vu.push(Mt),Mt=!1}function Cn(){const e=Vu.pop();Mt=e===void 0?!0:e}function bs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Le;Le=void 0;try{t()}finally{Le=n}}}let Sa=0;class om{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Pr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Le||!Mt||Le===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Le)n=this.activeLink=new om(Le,this),Le.deps?(n.prevDep=Le.depsTail,Le.depsTail.nextDep=n,Le.depsTail=n):Le.deps=Le.depsTail=n,Fu(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const o=n.nextDep;o.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=o),n.prevDep=Le.depsTail,n.nextDep=void 0,Le.depsTail.nextDep=n,Le.depsTail=n,Le.deps===n&&(Le.deps=o)}return n}trigger(t){this.version++,Sa++,this.notify(t)}notify(t){_r();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{kr()}}}function Fu(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)Fu(o)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Yl=new WeakMap,so=Symbol(""),Kl=Symbol(""),Ca=Symbol("");function lt(e,t,n){if(Mt&&Le){let o=Yl.get(e);o||Yl.set(e,o=new Map);let a=o.get(n);a||(o.set(n,a=new Pr),a.map=o,a.key=n),a.track()}}function gn(e,t,n,o,a,i){const l=Yl.get(e);if(!l){Sa++;return}const r=s=>{s&&s.trigger()};if(_r(),t==="clear")l.forEach(r);else{const s=be(e),c=s&&Tr(n);if(s&&n==="length"){const u=Number(o);l.forEach((d,h)=>{(h==="length"||h===Ca||!qn(h)&&h>=u)&&r(d)})}else switch((n!==void 0||l.has(void 0))&&r(l.get(n)),c&&r(l.get(Ca)),t){case"add":s?c&&r(l.get("length")):(r(l.get(so)),Ro(e)&&r(l.get(Kl)));break;case"delete":s||(r(l.get(so)),Ro(e)&&r(l.get(Kl)));break;case"set":Ro(e)&&r(l.get(so));break}}kr()}function go(e){const t=Ae(e);return t===e?t:(lt(t,"iterate",Ca),Et(e)?t:t.map(et))}function Fi(e){return lt(e=Ae(e),"iterate",Ca),e}const am={__proto__:null,[Symbol.iterator](){return fl(this,Symbol.iterator,et)},concat(...e){return go(this).concat(...e.map(t=>be(t)?go(t):t))},entries(){return fl(this,"entries",e=>(e[1]=et(e[1]),e))},every(e,t){return sn(this,"every",e,t,void 0,arguments)},filter(e,t){return sn(this,"filter",e,t,n=>n.map(et),arguments)},find(e,t){return sn(this,"find",e,t,et,arguments)},findIndex(e,t){return sn(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return sn(this,"findLast",e,t,et,arguments)},findLastIndex(e,t){return sn(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return sn(this,"forEach",e,t,void 0,arguments)},includes(...e){return hl(this,"includes",e)},indexOf(...e){return hl(this,"indexOf",e)},join(e){return go(this).join(e)},lastIndexOf(...e){return hl(this,"lastIndexOf",e)},map(e,t){return sn(this,"map",e,t,void 0,arguments)},pop(){return Jo(this,"pop")},push(...e){return Jo(this,"push",e)},reduce(e,...t){return ys(this,"reduce",e,t)},reduceRight(e,...t){return ys(this,"reduceRight",e,t)},shift(){return Jo(this,"shift")},some(e,t){return sn(this,"some",e,t,void 0,arguments)},splice(...e){return Jo(this,"splice",e)},toReversed(){return go(this).toReversed()},toSorted(e){return go(this).toSorted(e)},toSpliced(...e){return go(this).toSpliced(...e)},unshift(...e){return Jo(this,"unshift",e)},values(){return fl(this,"values",et)}};function fl(e,t,n){const o=Fi(e),a=o[t]();return o!==e&&!Et(e)&&(a._next=a.next,a.next=()=>{const i=a._next();return i.value&&(i.value=n(i.value)),i}),a}const im=Array.prototype;function sn(e,t,n,o,a,i){const l=Fi(e),r=l!==e&&!Et(e),s=l[t];if(s!==im[t]){const d=s.apply(e,i);return r?et(d):d}let c=n;l!==e&&(r?c=function(d,h){return n.call(this,et(d),h,e)}:n.length>2&&(c=function(d,h){return n.call(this,d,h,e)}));const u=s.call(l,c,o);return r&&a?a(u):u}function ys(e,t,n,o){const a=Fi(e);let i=n;return a!==e&&(Et(e)?n.length>3&&(i=function(l,r,s){return n.call(this,l,r,s,e)}):i=function(l,r,s){return n.call(this,l,et(r),s,e)}),a[t](i,...o)}function hl(e,t,n){const o=Ae(e);lt(o,"iterate",Ca);const a=o[t](...n);return(a===-1||a===!1)&&Or(n[0])?(n[0]=Ae(n[0]),o[t](...n)):a}function Jo(e,t,n=[]){Sn(),_r();const o=Ae(e)[t].apply(e,n);return kr(),Cn(),o}const lm=xr("__proto__,__v_isRef,__isVue"),Nu=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(qn));function rm(e){qn(e)||(e=String(e));const t=Ae(this);return lt(t,"has",e),t.hasOwnProperty(e)}class zu{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){if(n==="__v_skip")return t.__v_skip;const a=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!a;if(n==="__v_isReadonly")return a;if(n==="__v_isShallow")return i;if(n==="__v_raw")return o===(a?i?bm:Uu:i?Wu:ju).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const l=be(t);if(!a){let s;if(l&&(s=am[n]))return s;if(n==="hasOwnProperty")return rm}const r=Reflect.get(t,n,ot(t)?t:o);return(qn(n)?Nu.has(n):lm(n))||(a||lt(t,"get",n),i)?r:ot(r)?l&&Tr(n)?r:r.value:Fe(r)?a?Ku(r):je(r):r}}class Hu extends zu{constructor(t=!1){super(!1,t)}set(t,n,o,a){let i=t[n];if(!this._isShallow){const s=Yn(i);if(!Et(o)&&!Yn(o)&&(i=Ae(i),o=Ae(o)),!be(t)&&ot(i)&&!ot(o))return s?!1:(i.value=o,!0)}const l=be(t)&&Tr(n)?Number(n)<t.length:Re(t,n),r=Reflect.set(t,n,o,ot(t)?t:a);return t===Ae(a)&&(l?Un(o,i)&&gn(t,"set",n,o):gn(t,"add",n,o)),r}deleteProperty(t,n){const o=Re(t,n);t[n];const a=Reflect.deleteProperty(t,n);return a&&o&&gn(t,"delete",n,void 0),a}has(t,n){const o=Reflect.has(t,n);return(!qn(n)||!Nu.has(n))&&lt(t,"has",n),o}ownKeys(t){return lt(t,"iterate",be(t)?"length":so),Reflect.ownKeys(t)}}class sm extends zu{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const cm=new Hu,um=new sm,dm=new Hu(!0);const ql=e=>e,Na=e=>Reflect.getPrototypeOf(e);function fm(e,t,n){return function(...o){const a=this.__v_raw,i=Ae(a),l=Ro(i),r=e==="entries"||e===Symbol.iterator&&l,s=e==="keys"&&l,c=a[e](...o),u=n?ql:t?gi:et;return!t&&lt(i,"iterate",s?Kl:so),{next(){const{value:d,done:h}=c.next();return h?{value:d,done:h}:{value:r?[u(d[0]),u(d[1])]:u(d),done:h}},[Symbol.iterator](){return this}}}}function za(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function hm(e,t){const n={get(a){const i=this.__v_raw,l=Ae(i),r=Ae(a);e||(Un(a,r)&&lt(l,"get",a),lt(l,"get",r));const{has:s}=Na(l),c=t?ql:e?gi:et;if(s.call(l,a))return c(i.get(a));if(s.call(l,r))return c(i.get(r));i!==l&&i.get(a)},get size(){const a=this.__v_raw;return!e&&lt(Ae(a),"iterate",so),Reflect.get(a,"size",a)},has(a){const i=this.__v_raw,l=Ae(i),r=Ae(a);return e||(Un(a,r)&&lt(l,"has",a),lt(l,"has",r)),a===r?i.has(a):i.has(a)||i.has(r)},forEach(a,i){const l=this,r=l.__v_raw,s=Ae(r),c=t?ql:e?gi:et;return!e&&lt(s,"iterate",so),r.forEach((u,d)=>a.call(i,c(u),c(d),l))}};return Qe(n,e?{add:za("add"),set:za("set"),delete:za("delete"),clear:za("clear")}:{add(a){!t&&!Et(a)&&!Yn(a)&&(a=Ae(a));const i=Ae(this);return Na(i).has.call(i,a)||(i.add(a),gn(i,"add",a,a)),this},set(a,i){!t&&!Et(i)&&!Yn(i)&&(i=Ae(i));const l=Ae(this),{has:r,get:s}=Na(l);let c=r.call(l,a);c||(a=Ae(a),c=r.call(l,a));const u=s.call(l,a);return l.set(a,i),c?Un(i,u)&&gn(l,"set",a,i):gn(l,"add",a,i),this},delete(a){const i=Ae(this),{has:l,get:r}=Na(i);let s=l.call(i,a);s||(a=Ae(a),s=l.call(i,a)),r&&r.call(i,a);const c=i.delete(a);return s&&gn(i,"delete",a,void 0),c},clear(){const a=Ae(this),i=a.size!==0,l=a.clear();return i&&gn(a,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(a=>{n[a]=fm(a,e,t)}),n}function $r(e,t){const n=hm(e,t);return(o,a,i)=>a==="__v_isReactive"?!e:a==="__v_isReadonly"?e:a==="__v_raw"?o:Reflect.get(Re(n,a)&&a in o?n:o,a,i)}const mm={get:$r(!1,!1)},vm={get:$r(!1,!0)},gm={get:$r(!0,!1)};const ju=new WeakMap,Wu=new WeakMap,Uu=new WeakMap,bm=new WeakMap;function ym(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function pm(e){return e.__v_skip||!Object.isExtensible(e)?0:ym(zh(e))}function je(e){return Yn(e)?e:Ar(e,!1,cm,mm,ju)}function Yu(e){return Ar(e,!1,dm,vm,Wu)}function Ku(e){return Ar(e,!0,um,gm,Uu)}function Ar(e,t,n,o,a){if(!Fe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=pm(e);if(i===0)return e;const l=a.get(e);if(l)return l;const r=new Proxy(e,i===2?o:n);return a.set(e,r),r}function Do(e){return Yn(e)?Do(e.__v_raw):!!(e&&e.__v_isReactive)}function Yn(e){return!!(e&&e.__v_isReadonly)}function Et(e){return!!(e&&e.__v_isShallow)}function Or(e){return e?!!e.__v_raw:!1}function Ae(e){const t=e&&e.__v_raw;return t?Ae(t):e}function wm(e){return!Re(e,"__v_skip")&&Object.isExtensible(e)&&Pu(e,"__v_skip",!0),e}const et=e=>Fe(e)?je(e):e,gi=e=>Fe(e)?Ku(e):e;function ot(e){return e?e.__v_isRef===!0:!1}function M(e){return qu(e,!1)}function xm(e){return qu(e,!0)}function qu(e,t){return ot(e)?e:new Sm(e,t)}class Sm{constructor(t,n){this.dep=new Pr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Ae(t),this._value=n?t:et(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,o=this.__v_isShallow||Et(t)||Yn(t);t=o?t:Ae(t),Un(t,n)&&(this._rawValue=t,this._value=o?t:et(t),this.dep.trigger())}}function Lt(e){return ot(e)?e.value:e}const Cm={get:(e,t,n)=>t==="__v_raw"?e:Lt(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const a=e[t];return ot(a)&&!ot(n)?(a.value=n,!0):Reflect.set(e,t,n,o)}};function Gu(e){return Do(e)?e:new Proxy(e,Cm)}class Tm{constructor(t,n,o){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Pr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Sa-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&Le!==this)return Du(this,!0),!0}get value(){const t=this.dep.track();return Lu(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function _m(e,t,n=!1){let o,a;return pe(e)?o=e:(o=e.get,a=e.set),new Tm(o,a,n)}const Ha={},bi=new WeakMap;let io;function km(e,t=!1,n=io){if(n){let o=bi.get(n);o||bi.set(n,o=[]),o.push(e)}}function Em(e,t,n=Be){const{immediate:o,deep:a,once:i,scheduler:l,augmentJob:r,call:s}=n,c=S=>a?S:Et(S)||a===!1||a===0?bn(S,1):bn(S);let u,d,h,m,v=!1,p=!1;if(ot(e)?(d=()=>e.value,v=Et(e)):Do(e)?(d=()=>c(e),v=!0):be(e)?(p=!0,v=e.some(S=>Do(S)||Et(S)),d=()=>e.map(S=>{if(ot(S))return S.value;if(Do(S))return c(S);if(pe(S))return s?s(S,2):S()})):pe(e)?t?d=s?()=>s(e,2):e:d=()=>{if(h){Sn();try{h()}finally{Cn()}}const S=io;io=u;try{return s?s(e,3,[m]):e(m)}finally{io=S}}:d=nn,t&&a){const S=d,y=a===!0?1/0:a;d=()=>bn(S(),y)}const b=tm(),w=()=>{u.stop(),b&&b.active&&Cr(b.effects,u)};if(i&&t){const S=t;t=(...y)=>{S(...y),w()}}let g=p?new Array(e.length).fill(Ha):Ha;const x=S=>{if(!(!(u.flags&1)||!u.dirty&&!S))if(t){const y=u.run();if(a||v||(p?y.some((T,O)=>Un(T,g[O])):Un(y,g))){h&&h();const T=io;io=u;try{const O=[y,g===Ha?void 0:p&&g[0]===Ha?[]:g,m];g=y,s?s(t,3,O):t(...O)}finally{io=T}}}else u.run()};return r&&r(x),u=new Iu(d),u.scheduler=l?()=>l(x,!1):x,m=S=>km(S,!1,u),h=u.onStop=()=>{const S=bi.get(u);if(S){if(s)s(S,4);else for(const y of S)y();bi.delete(u)}},t?o?x(!0):g=u.run():l?l(x.bind(null,!0),!0):u.run(),w.pause=u.pause.bind(u),w.resume=u.resume.bind(u),w.stop=w,w}function bn(e,t=1/0,n){if(t<=0||!Fe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ot(e))bn(e.value,t,n);else if(be(e))for(let o=0;o<e.length;o++)bn(e[o],t,n);else if(Tu(e)||Ro(e))e.forEach(o=>{bn(o,t,n)});else if(Eu(e)){for(const o in e)bn(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&bn(e[o],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ma(e,t,n,o){try{return o?e(...o):e()}catch(a){Ni(a,t,n)}}function Nt(e,t,n,o){if(pe(e)){const a=Ma(e,t,n,o);return a&&_u(a)&&a.catch(i=>{Ni(i,t,n)}),a}if(be(e)){const a=[];for(let i=0;i<e.length;i++)a.push(Nt(e[i],t,n,o));return a}}function Ni(e,t,n,o=!0){const a=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||Be;if(t){let r=t.parent;const s=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;r;){const u=r.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,s,c)===!1)return}r=r.parent}if(i){Sn(),Ma(i,null,10,[e,s,c]),Cn();return}}Pm(e,n,a,o,l)}function Pm(e,t,n,o=!0,a=!1){if(a)throw e;console.error(e)}const gt=[];let en=-1;const Bo=[];let Nn=null,$o=0;const Xu=Promise.resolve();let yi=null;function Se(e){const t=yi||Xu;return e?t.then(this?e.bind(this):e):t}function $m(e){let t=en+1,n=gt.length;for(;t<n;){const o=t+n>>>1,a=gt[o],i=Ta(a);i<e||i===e&&a.flags&2?t=o+1:n=o}return t}function Ir(e){if(!(e.flags&1)){const t=Ta(e),n=gt[gt.length-1];!n||!(e.flags&2)&&t>=Ta(n)?gt.push(e):gt.splice($m(t),0,e),e.flags|=1,Zu()}}function Zu(){yi||(yi=Xu.then(Qu))}function Am(e){be(e)?Bo.push(...e):Nn&&e.id===-1?Nn.splice($o+1,0,e):e.flags&1||(Bo.push(e),e.flags|=1),Zu()}function ps(e,t,n=en+1){for(;n<gt.length;n++){const o=gt[n];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;gt.splice(n,1),n--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function Ju(e){if(Bo.length){const t=[...new Set(Bo)].sort((n,o)=>Ta(n)-Ta(o));if(Bo.length=0,Nn){Nn.push(...t);return}for(Nn=t,$o=0;$o<Nn.length;$o++){const n=Nn[$o];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Nn=null,$o=0}}const Ta=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Qu(e){try{for(en=0;en<gt.length;en++){const t=gt[en];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ma(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;en<gt.length;en++){const t=gt[en];t&&(t.flags&=-2)}en=-1,gt.length=0,Ju(),yi=null,(gt.length||Bo.length)&&Qu()}}let wt=null,ed=null;function pi(e){const t=wt;return wt=e,ed=e&&e.type.__scopeId||null,t}function Om(e,t=wt,n){if(!t||e._n)return e;const o=(...a)=>{o._d&&Rs(-1);const i=pi(t);let l;try{l=e(...a)}finally{pi(i),o._d&&Rs(1)}return l};return o._n=!0,o._c=!0,o._d=!0,o}function Je(e,t){if(wt===null)return e;const n=Wi(wt),o=e.dirs||(e.dirs=[]);for(let a=0;a<t.length;a++){let[i,l,r,s=Be]=t[a];i&&(pe(i)&&(i={mounted:i,updated:i}),i.deep&&bn(l),o.push({dir:i,instance:n,value:l,oldValue:void 0,arg:r,modifiers:s}))}return e}function Qn(e,t,n,o){const a=e.dirs,i=t&&t.dirs;for(let l=0;l<a.length;l++){const r=a[l];i&&(r.oldValue=i[l].value);let s=r.dir[o];s&&(Sn(),Nt(s,n,8,[e.el,r,e,t]),Cn())}}const td=Symbol("_vte"),nd=e=>e.__isTeleport,ha=e=>e&&(e.disabled||e.disabled===""),ws=e=>e&&(e.defer||e.defer===""),xs=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ss=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Gl=(e,t)=>{const n=e&&e.to;return He(n)?t?t(n):null:n},od={name:"Teleport",__isTeleport:!0,process(e,t,n,o,a,i,l,r,s,c){const{mc:u,pc:d,pbc:h,o:{insert:m,querySelector:v,createText:p,createComment:b}}=c,w=ha(t.props);let{shapeFlag:g,children:x,dynamicChildren:S}=t;if(e==null){const y=t.el=p(""),T=t.anchor=p("");m(y,n,o),m(T,n,o);const O=(A,I)=>{g&16&&(a&&a.isCE&&(a.ce._teleportTarget=A),u(x,A,I,a,i,l,r,s))},C=()=>{const A=t.target=Gl(t.props,v),I=ad(A,t,p,m);A&&(l!=="svg"&&xs(A)?l="svg":l!=="mathml"&&Ss(A)&&(l="mathml"),w||(O(A,I),ui(t,!1)))};w&&(O(n,T),ui(t,!0)),ws(t.props)?(t.el.__isMounted=!1,vt(()=>{C(),delete t.el.__isMounted},i)):C()}else{if(ws(t.props)&&e.el.__isMounted===!1){vt(()=>{od.process(e,t,n,o,a,i,l,r,s,c)},i);return}t.el=e.el,t.targetStart=e.targetStart;const y=t.anchor=e.anchor,T=t.target=e.target,O=t.targetAnchor=e.targetAnchor,C=ha(e.props),A=C?n:T,I=C?y:O;if(l==="svg"||xs(T)?l="svg":(l==="mathml"||Ss(T))&&(l="mathml"),S?(h(e.dynamicChildren,S,A,a,i,l,r),Br(e,t,!0)):s||d(e,t,A,I,a,i,l,r,!1),w)C?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):ja(t,n,y,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const P=t.target=Gl(t.props,v);P&&ja(t,P,null,c,0)}else C&&ja(t,T,O,c,1);ui(t,w)}},remove(e,t,n,{um:o,o:{remove:a}},i){const{shapeFlag:l,children:r,anchor:s,targetStart:c,targetAnchor:u,target:d,props:h}=e;if(d&&(a(c),a(u)),i&&a(s),l&16){const m=i||!ha(h);for(let v=0;v<r.length;v++){const p=r[v];o(p,t,n,m,!!p.dynamicChildren)}}},move:ja,hydrate:Im};function ja(e,t,n,{o:{insert:o},m:a},i=2){i===0&&o(e.targetAnchor,t,n);const{el:l,anchor:r,shapeFlag:s,children:c,props:u}=e,d=i===2;if(d&&o(l,t,n),(!d||ha(u))&&s&16)for(let h=0;h<c.length;h++)a(c[h],t,n,2);d&&o(r,t,n)}function Im(e,t,n,o,a,i,{o:{nextSibling:l,parentNode:r,querySelector:s,insert:c,createText:u}},d){const h=t.target=Gl(t.props,s);if(h){const m=ha(t.props),v=h._lpa||h.firstChild;if(t.shapeFlag&16)if(m)t.anchor=d(l(e),t,r(e),n,o,a,i),t.targetStart=v,t.targetAnchor=v&&l(v);else{t.anchor=l(e);let p=v;for(;p;){if(p&&p.nodeType===8){if(p.data==="teleport start anchor")t.targetStart=p;else if(p.data==="teleport anchor"){t.targetAnchor=p,h._lpa=t.targetAnchor&&l(t.targetAnchor);break}}p=l(p)}t.targetAnchor||ad(h,t,u,c),d(v&&l(v),t,h,n,o,a,i)}ui(t,m)}return t.anchor&&l(t.anchor)}const ho=od;function ui(e,t){const n=e.ctx;if(n&&n.ut){let o,a;for(t?(o=e.el,a=e.anchor):(o=e.targetStart,a=e.targetAnchor);o&&o!==a;)o.nodeType===1&&o.setAttribute("data-v-owner",n.uid),o=o.nextSibling;n.ut()}}function ad(e,t,n,o){const a=t.targetStart=n(""),i=t.targetAnchor=n("");return a[td]=i,e&&(o(a,e),o(i,e)),i}const zn=Symbol("_leaveCb"),Wa=Symbol("_enterCb");function Rm(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return We(()=>{e.isMounted=!0}),rn(()=>{e.isUnmounting=!0}),e}const Tt=[Function,Array],id={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Tt,onEnter:Tt,onAfterEnter:Tt,onEnterCancelled:Tt,onBeforeLeave:Tt,onLeave:Tt,onAfterLeave:Tt,onLeaveCancelled:Tt,onBeforeAppear:Tt,onAppear:Tt,onAfterAppear:Tt,onAppearCancelled:Tt},ld=e=>{const t=e.subTree;return t.component?ld(t.component):t},Dm={name:"BaseTransition",props:id,setup(e,{slots:t}){const n=jt(),o=Rm();return()=>{const a=t.default&&cd(t.default(),!0);if(!a||!a.length)return;const i=rd(a),l=Ae(e),{mode:r}=l;if(o.isLeaving)return ml(i);const s=Cs(i);if(!s)return ml(i);let c=Xl(s,l,o,n,d=>c=d);s.type!==tt&&_a(s,c);let u=n.subTree&&Cs(n.subTree);if(u&&u.type!==tt&&!lo(s,u)&&ld(n).type!==tt){let d=Xl(u,l,o,n);if(_a(u,d),r==="out-in"&&s.type!==tt)return o.isLeaving=!0,d.afterLeave=()=>{o.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,u=void 0},ml(i);r==="in-out"&&s.type!==tt?d.delayLeave=(h,m,v)=>{const p=sd(o,u);p[String(u.key)]=u,h[zn]=()=>{m(),h[zn]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{v(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return i}}};function rd(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==tt){t=n;break}}return t}const Bm=Dm;function sd(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function Xl(e,t,n,o,a){const{appear:i,mode:l,persisted:r=!1,onBeforeEnter:s,onEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:h,onLeave:m,onAfterLeave:v,onLeaveCancelled:p,onBeforeAppear:b,onAppear:w,onAfterAppear:g,onAppearCancelled:x}=t,S=String(e.key),y=sd(n,e),T=(A,I)=>{A&&Nt(A,o,9,I)},O=(A,I)=>{const P=I[1];T(A,I),be(A)?A.every(k=>k.length<=1)&&P():A.length<=1&&P()},C={mode:l,persisted:r,beforeEnter(A){let I=s;if(!n.isMounted)if(i)I=b||s;else return;A[zn]&&A[zn](!0);const P=y[S];P&&lo(e,P)&&P.el[zn]&&P.el[zn](),T(I,[A])},enter(A){let I=c,P=u,k=d;if(!n.isMounted)if(i)I=w||c,P=g||u,k=x||d;else return;let D=!1;const j=A[Wa]=ae=>{D||(D=!0,ae?T(k,[A]):T(P,[A]),C.delayedLeave&&C.delayedLeave(),A[Wa]=void 0)};I?O(I,[A,j]):j()},leave(A,I){const P=String(e.key);if(A[Wa]&&A[Wa](!0),n.isUnmounting)return I();T(h,[A]);let k=!1;const D=A[zn]=j=>{k||(k=!0,I(),j?T(p,[A]):T(v,[A]),A[zn]=void 0,y[P]===e&&delete y[P])};y[P]=e,m?O(m,[A,D]):D()},clone(A){const I=Xl(A,t,n,o,a);return a&&a(I),I}};return C}function ml(e){if(zi(e))return e=Kn(e),e.children=null,e}function Cs(e){if(!zi(e))return nd(e.type)&&e.children?rd(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&pe(n.default))return n.default()}}function _a(e,t){e.shapeFlag&6&&e.component?(e.transition=t,_a(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function cd(e,t=!1,n){let o=[],a=0;for(let i=0;i<e.length;i++){let l=e[i];const r=n==null?l.key:String(n)+String(l.key!=null?l.key:i);l.type===qe?(l.patchFlag&128&&a++,o=o.concat(cd(l.children,t,r))):(t||l.type!==tt)&&o.push(r!=null?Kn(l,{key:r}):l)}if(a>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}/*! #__NO_SIDE_EFFECTS__ */function W(e,t){return pe(e)?Qe({name:e.name},t,{setup:e}):e}function ud(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function wi(e,t,n,o,a=!1){if(be(e)){e.forEach((v,p)=>wi(v,t&&(be(t)?t[p]:t),n,o,a));return}if(ma(o)&&!a){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&wi(e,t,n,o.component.subTree);return}const i=o.shapeFlag&4?Wi(o.component):o.el,l=a?null:i,{i:r,r:s}=e,c=t&&t.r,u=r.refs===Be?r.refs={}:r.refs,d=r.setupState,h=Ae(d),m=d===Be?()=>!1:v=>Re(h,v);if(c!=null&&c!==s&&(He(c)?(u[c]=null,m(c)&&(d[c]=null)):ot(c)&&(c.value=null)),pe(s))Ma(s,r,12,[l,u]);else{const v=He(s),p=ot(s);if(v||p){const b=()=>{if(e.f){const w=v?m(s)?d[s]:u[s]:s.value;a?be(w)&&Cr(w,i):be(w)?w.includes(i)||w.push(i):v?(u[s]=[i],m(s)&&(d[s]=u[s])):(s.value=[i],e.k&&(u[e.k]=s.value))}else v?(u[s]=l,m(s)&&(d[s]=l)):p&&(s.value=l,e.k&&(u[e.k]=l))};l?(b.id=-1,vt(b,n)):b()}}}Mi().requestIdleCallback;Mi().cancelIdleCallback;const ma=e=>!!e.type.__asyncLoader,zi=e=>e.type.__isKeepAlive;function an(e,t){dd(e,"a",t)}function ln(e,t){dd(e,"da",t)}function dd(e,t,n=nt){const o=e.__wdc||(e.__wdc=()=>{let a=n;for(;a;){if(a.isDeactivated)return;a=a.parent}return e()});if(Hi(t,o,n),n){let a=n.parent;for(;a&&a.parent;)zi(a.parent.vnode)&&Mm(o,t,n,a),a=a.parent}}function Mm(e,t,n,o){const a=Hi(t,e,o,!0);Uo(()=>{Cr(o[t],a)},n)}function Hi(e,t,n=nt,o=!1){if(n){const a=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...l)=>{Sn();const r=Va(n),s=Nt(t,n,e,l);return r(),Cn(),s});return o?a.unshift(i):a.push(i),i}}const $n=e=>(t,n=nt)=>{(!Pa||e==="sp")&&Hi(e,(...o)=>t(...o),n)},Lm=$n("bm"),We=$n("m"),fd=$n("bu"),hd=$n("u"),rn=$n("bum"),Uo=$n("um"),Vm=$n("sp"),Fm=$n("rtg"),Nm=$n("rtc");function zm(e,t=nt){Hi("ec",e,t)}const md="components",Hm="directives";function jm(e,t){return vd(md,e,!0,t)||e}const Wm=Symbol.for("v-ndc");function Um(e){return vd(Hm,e)}function vd(e,t,n=!0,o=!1){const a=wt||nt;if(a){const i=a.type;if(e===md){const r=Iv(i,!1);if(r&&(r===t||r===$t(t)||r===Bi($t(t))))return i}const l=Ts(a[e]||i[e],t)||Ts(a.appContext[e],t);return!l&&o?i:l}}function Ts(e,t){return e&&(e[t]||e[$t(t)]||e[Bi($t(t))])}function tk(e,t,n,o){let a;const i=n,l=be(e);if(l||He(e)){const r=l&&Do(e);let s=!1,c=!1;r&&(s=!Et(e),c=Yn(e),e=Fi(e)),a=new Array(e.length);for(let u=0,d=e.length;u<d;u++)a[u]=t(s?c?gi(et(e[u])):et(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){a=new Array(e);for(let r=0;r<e;r++)a[r]=t(r+1,r,void 0,i)}else if(Fe(e))if(e[Symbol.iterator])a=Array.from(e,(r,s)=>t(r,s,void 0,i));else{const r=Object.keys(e);a=new Array(r.length);for(let s=0,c=r.length;s<c;s++){const u=r[s];a[s]=t(e[u],u,s,i)}}else a=[];return a}const Zl=e=>e?Bd(e)?Wi(e):Zl(e.parent):null,va=Qe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Zl(e.parent),$root:e=>Zl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>bd(e),$forceUpdate:e=>e.f||(e.f=()=>{Ir(e.update)}),$nextTick:e=>e.n||(e.n=Se.bind(e.proxy)),$watch:e=>fv.bind(e)}),vl=(e,t)=>e!==Be&&!e.__isScriptSetup&&Re(e,t),Ym={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:a,props:i,accessCache:l,type:r,appContext:s}=e;let c;if(t[0]!=="$"){const m=l[t];if(m!==void 0)switch(m){case 1:return o[t];case 2:return a[t];case 4:return n[t];case 3:return i[t]}else{if(vl(o,t))return l[t]=1,o[t];if(a!==Be&&Re(a,t))return l[t]=2,a[t];if((c=e.propsOptions[0])&&Re(c,t))return l[t]=3,i[t];if(n!==Be&&Re(n,t))return l[t]=4,n[t];Jl&&(l[t]=0)}}const u=va[t];let d,h;if(u)return t==="$attrs"&&lt(e.attrs,"get",""),u(e);if((d=r.__cssModules)&&(d=d[t]))return d;if(n!==Be&&Re(n,t))return l[t]=4,n[t];if(h=s.config.globalProperties,Re(h,t))return h[t]},set({_:e},t,n){const{data:o,setupState:a,ctx:i}=e;return vl(a,t)?(a[t]=n,!0):o!==Be&&Re(o,t)?(o[t]=n,!0):Re(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:a,propsOptions:i}},l){let r;return!!n[l]||e!==Be&&Re(e,l)||vl(t,l)||(r=i[0])&&Re(r,l)||Re(o,l)||Re(va,l)||Re(a.config.globalProperties,l)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Re(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function _s(e){return be(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Jl=!0;function Km(e){const t=bd(e),n=e.proxy,o=e.ctx;Jl=!1,t.beforeCreate&&ks(t.beforeCreate,e,"bc");const{data:a,computed:i,methods:l,watch:r,provide:s,inject:c,created:u,beforeMount:d,mounted:h,beforeUpdate:m,updated:v,activated:p,deactivated:b,beforeDestroy:w,beforeUnmount:g,destroyed:x,unmounted:S,render:y,renderTracked:T,renderTriggered:O,errorCaptured:C,serverPrefetch:A,expose:I,inheritAttrs:P,components:k,directives:D,filters:j}=t;if(c&&qm(c,o,null),l)for(const ne in l){const oe=l[ne];pe(oe)&&(o[ne]=oe.bind(n))}if(a){const ne=a.call(n,n);Fe(ne)&&(e.data=je(ne))}if(Jl=!0,i)for(const ne in i){const oe=i[ne],Te=pe(oe)?oe.bind(n,n):pe(oe.get)?oe.get.bind(n,n):nn,Ee=!pe(oe)&&pe(oe.set)?oe.set.bind(n):nn,re=B({get:Te,set:Ee});Object.defineProperty(o,ne,{enumerable:!0,configurable:!0,get:()=>re.value,set:N=>re.value=N})}if(r)for(const ne in r)gd(r[ne],o,n,ne);if(s){const ne=pe(s)?s.call(n):s;Reflect.ownKeys(ne).forEach(oe=>{pn(oe,ne[oe])})}u&&ks(u,e,"c");function F(ne,oe){be(oe)?oe.forEach(Te=>ne(Te.bind(n))):oe&&ne(oe.bind(n))}if(F(Lm,d),F(We,h),F(fd,m),F(hd,v),F(an,p),F(ln,b),F(zm,C),F(Nm,T),F(Fm,O),F(rn,g),F(Uo,S),F(Vm,A),be(I))if(I.length){const ne=e.exposed||(e.exposed={});I.forEach(oe=>{Object.defineProperty(ne,oe,{get:()=>n[oe],set:Te=>n[oe]=Te})})}else e.exposed||(e.exposed={});y&&e.render===nn&&(e.render=y),P!=null&&(e.inheritAttrs=P),k&&(e.components=k),D&&(e.directives=D),A&&ud(e)}function qm(e,t,n=nn){be(e)&&(e=Ql(e));for(const o in e){const a=e[o];let i;Fe(a)?"default"in a?i=rt(a.from||o,a.default,!0):i=rt(a.from||o):i=rt(a),ot(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:l=>i.value=l}):t[o]=i}}function ks(e,t,n){Nt(be(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function gd(e,t,n,o){let a=o.includes(".")?Pd(n,o):()=>n[o];if(He(e)){const i=t[e];pe(i)&&te(a,i)}else if(pe(e))te(a,e.bind(n));else if(Fe(e))if(be(e))e.forEach(i=>gd(i,t,n,o));else{const i=pe(e.handler)?e.handler.bind(n):t[e.handler];pe(i)&&te(a,i,e)}}function bd(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:a,optionsCache:i,config:{optionMergeStrategies:l}}=e.appContext,r=i.get(t);let s;return r?s=r:!a.length&&!n&&!o?s=t:(s={},a.length&&a.forEach(c=>xi(s,c,l,!0)),xi(s,t,l)),Fe(t)&&i.set(t,s),s}function xi(e,t,n,o=!1){const{mixins:a,extends:i}=t;i&&xi(e,i,n,!0),a&&a.forEach(l=>xi(e,l,n,!0));for(const l in t)if(!(o&&l==="expose")){const r=Gm[l]||n&&n[l];e[l]=r?r(e[l],t[l]):t[l]}return e}const Gm={data:Es,props:Ps,emits:Ps,methods:ca,computed:ca,beforeCreate:mt,created:mt,beforeMount:mt,mounted:mt,beforeUpdate:mt,updated:mt,beforeDestroy:mt,beforeUnmount:mt,destroyed:mt,unmounted:mt,activated:mt,deactivated:mt,errorCaptured:mt,serverPrefetch:mt,components:ca,directives:ca,watch:Zm,provide:Es,inject:Xm};function Es(e,t){return t?e?function(){return Qe(pe(e)?e.call(this,this):e,pe(t)?t.call(this,this):t)}:t:e}function Xm(e,t){return ca(Ql(e),Ql(t))}function Ql(e){if(be(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function mt(e,t){return e?[...new Set([].concat(e,t))]:t}function ca(e,t){return e?Qe(Object.create(null),e,t):t}function Ps(e,t){return e?be(e)&&be(t)?[...new Set([...e,...t])]:Qe(Object.create(null),_s(e),_s(t??{})):t}function Zm(e,t){if(!e)return t;if(!t)return e;const n=Qe(Object.create(null),e);for(const o in t)n[o]=mt(e[o],t[o]);return n}function yd(){return{app:null,config:{isNativeTag:Fh,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Jm=0;function Qm(e,t){return function(o,a=null){pe(o)||(o=Qe({},o)),a!=null&&!Fe(a)&&(a=null);const i=yd(),l=new WeakSet,r=[];let s=!1;const c=i.app={_uid:Jm++,_component:o,_props:a,_container:null,_context:i,_instance:null,version:Dv,get config(){return i.config},set config(u){},use(u,...d){return l.has(u)||(u&&pe(u.install)?(l.add(u),u.install(c,...d)):pe(u)&&(l.add(u),u(c,...d))),c},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),c},component(u,d){return d?(i.components[u]=d,c):i.components[u]},directive(u,d){return d?(i.directives[u]=d,c):i.directives[u]},mount(u,d,h){if(!s){const m=c._ceVNode||f(o,a);return m.appContext=i,h===!0?h="svg":h===!1&&(h=void 0),e(m,u,h),s=!0,c._container=u,u.__vue_app__=c,Wi(m.component)}},onUnmount(u){r.push(u)},unmount(){s&&(Nt(r,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,d){return i.provides[u]=d,c},runWithContext(u){const d=Mo;Mo=c;try{return u()}finally{Mo=d}}};return c}}let Mo=null;function pn(e,t){if(nt){let n=nt.provides;const o=nt.parent&&nt.parent.provides;o===n&&(n=nt.provides=Object.create(o)),n[e]=t}}function rt(e,t,n=!1){const o=nt||wt;if(o||Mo){let a=Mo?Mo._context.provides:o?o.parent==null||o.ce?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(a&&e in a)return a[e];if(arguments.length>1)return n&&pe(t)?t.call(o&&o.proxy):t}}const pd={},wd=()=>Object.create(pd),xd=e=>Object.getPrototypeOf(e)===pd;function ev(e,t,n,o=!1){const a={},i=wd();e.propsDefaults=Object.create(null),Sd(e,t,a,i);for(const l in e.propsOptions[0])l in a||(a[l]=void 0);n?e.props=o?a:Yu(a):e.type.props?e.props=a:e.props=i,e.attrs=i}function tv(e,t,n,o){const{props:a,attrs:i,vnode:{patchFlag:l}}=e,r=Ae(a),[s]=e.propsOptions;let c=!1;if((o||l>0)&&!(l&16)){if(l&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let h=u[d];if(ji(e.emitsOptions,h))continue;const m=t[h];if(s)if(Re(i,h))m!==i[h]&&(i[h]=m,c=!0);else{const v=$t(h);a[v]=er(s,r,v,m,e,!1)}else m!==i[h]&&(i[h]=m,c=!0)}}}else{Sd(e,t,a,i)&&(c=!0);let u;for(const d in r)(!t||!Re(t,d)&&((u=Pn(d))===d||!Re(t,u)))&&(s?n&&(n[d]!==void 0||n[u]!==void 0)&&(a[d]=er(s,r,d,void 0,e,!0)):delete a[d]);if(i!==r)for(const d in i)(!t||!Re(t,d))&&(delete i[d],c=!0)}c&&gn(e.attrs,"set","")}function Sd(e,t,n,o){const[a,i]=e.propsOptions;let l=!1,r;if(t)for(let s in t){if(ua(s))continue;const c=t[s];let u;a&&Re(a,u=$t(s))?!i||!i.includes(u)?n[u]=c:(r||(r={}))[u]=c:ji(e.emitsOptions,s)||(!(s in o)||c!==o[s])&&(o[s]=c,l=!0)}if(i){const s=Ae(n),c=r||Be;for(let u=0;u<i.length;u++){const d=i[u];n[d]=er(a,s,d,c[d],e,!Re(c,d))}}return l}function er(e,t,n,o,a,i){const l=e[n];if(l!=null){const r=Re(l,"default");if(r&&o===void 0){const s=l.default;if(l.type!==Function&&!l.skipFactory&&pe(s)){const{propsDefaults:c}=a;if(n in c)o=c[n];else{const u=Va(a);o=c[n]=s.call(null,t),u()}}else o=s;a.ce&&a.ce._setProp(n,o)}l[0]&&(i&&!r?o=!1:l[1]&&(o===""||o===Pn(n))&&(o=!0))}return o}const nv=new WeakMap;function Cd(e,t,n=!1){const o=n?nv:t.propsCache,a=o.get(e);if(a)return a;const i=e.props,l={},r=[];let s=!1;if(!pe(e)){const u=d=>{s=!0;const[h,m]=Cd(d,t,!0);Qe(l,h),m&&r.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!s)return Fe(e)&&o.set(e,Io),Io;if(be(i))for(let u=0;u<i.length;u++){const d=$t(i[u]);$s(d)&&(l[d]=Be)}else if(i)for(const u in i){const d=$t(u);if($s(d)){const h=i[u],m=l[d]=be(h)||pe(h)?{type:h}:Qe({},h),v=m.type;let p=!1,b=!0;if(be(v))for(let w=0;w<v.length;++w){const g=v[w],x=pe(g)&&g.name;if(x==="Boolean"){p=!0;break}else x==="String"&&(b=!1)}else p=pe(v)&&v.name==="Boolean";m[0]=p,m[1]=b,(p||Re(m,"default"))&&r.push(d)}}const c=[l,r];return Fe(e)&&o.set(e,c),c}function $s(e){return e[0]!=="$"&&!ua(e)}const Rr=e=>e[0]==="_"||e==="$stable",Dr=e=>be(e)?e.map(tn):[tn(e)],ov=(e,t,n)=>{if(t._n)return t;const o=Om((...a)=>Dr(t(...a)),n);return o._c=!1,o},Td=(e,t,n)=>{const o=e._ctx;for(const a in e){if(Rr(a))continue;const i=e[a];if(pe(i))t[a]=ov(a,i,o);else if(i!=null){const l=Dr(i);t[a]=()=>l}}},_d=(e,t)=>{const n=Dr(t);e.slots.default=()=>n},kd=(e,t,n)=>{for(const o in t)(n||!Rr(o))&&(e[o]=t[o])},av=(e,t,n)=>{const o=e.slots=wd();if(e.vnode.shapeFlag&32){const a=t._;a?(kd(o,t,n),n&&Pu(o,"_",a,!0)):Td(t,o)}else t&&_d(e,t)},iv=(e,t,n)=>{const{vnode:o,slots:a}=e;let i=!0,l=Be;if(o.shapeFlag&32){const r=t._;r?n&&r===1?i=!1:kd(a,t,n):(i=!t.$stable,Td(t,a)),l=t}else t&&(_d(e,t),l={default:1});if(i)for(const r in a)!Rr(r)&&l[r]==null&&delete a[r]},vt=pv;function lv(e){return rv(e)}function rv(e,t){const n=Mi();n.__VUE__=!0;const{insert:o,remove:a,patchProp:i,createElement:l,createText:r,createComment:s,setText:c,setElementText:u,parentNode:d,nextSibling:h,setScopeId:m=nn,insertStaticContent:v}=e,p=(E,$,R,z=null,K=null,U=null,ue=void 0,le=null,ie=!!$.dynamicChildren)=>{if(E===$)return;E&&!lo(E,$)&&(z=_(E),N(E,K,U,!0),E=null),$.patchFlag===-2&&(ie=!1,$.dynamicChildren=null);const{type:Q,ref:ge,shapeFlag:de}=$;switch(Q){case La:b(E,$,R,z);break;case tt:w(E,$,R,z);break;case bl:E==null&&g($,R,z,ue);break;case qe:k(E,$,R,z,K,U,ue,le,ie);break;default:de&1?y(E,$,R,z,K,U,ue,le,ie):de&6?D(E,$,R,z,K,U,ue,le,ie):(de&64||de&128)&&Q.process(E,$,R,z,K,U,ue,le,ie,X)}ge!=null&&K&&wi(ge,E&&E.ref,U,$||E,!$)},b=(E,$,R,z)=>{if(E==null)o($.el=r($.children),R,z);else{const K=$.el=E.el;$.children!==E.children&&c(K,$.children)}},w=(E,$,R,z)=>{E==null?o($.el=s($.children||""),R,z):$.el=E.el},g=(E,$,R,z)=>{[E.el,E.anchor]=v(E.children,$,R,z,E.el,E.anchor)},x=({el:E,anchor:$},R,z)=>{let K;for(;E&&E!==$;)K=h(E),o(E,R,z),E=K;o($,R,z)},S=({el:E,anchor:$})=>{let R;for(;E&&E!==$;)R=h(E),a(E),E=R;a($)},y=(E,$,R,z,K,U,ue,le,ie)=>{$.type==="svg"?ue="svg":$.type==="math"&&(ue="mathml"),E==null?T($,R,z,K,U,ue,le,ie):A(E,$,K,U,ue,le,ie)},T=(E,$,R,z,K,U,ue,le)=>{let ie,Q;const{props:ge,shapeFlag:de,transition:ve,dirs:ye}=E;if(ie=E.el=l(E.type,U,ge&&ge.is,ge),de&8?u(ie,E.children):de&16&&C(E.children,ie,null,z,K,gl(E,U),ue,le),ye&&Qn(E,null,z,"created"),O(ie,E,E.scopeId,ue,z),ge){for(const Me in ge)Me!=="value"&&!ua(Me)&&i(ie,Me,null,ge[Me],U,z);"value"in ge&&i(ie,"value",null,ge.value,U),(Q=ge.onVnodeBeforeMount)&&Zt(Q,z,E)}ye&&Qn(E,null,z,"beforeMount");const Pe=sv(K,ve);Pe&&ve.beforeEnter(ie),o(ie,$,R),((Q=ge&&ge.onVnodeMounted)||Pe||ye)&&vt(()=>{Q&&Zt(Q,z,E),Pe&&ve.enter(ie),ye&&Qn(E,null,z,"mounted")},K)},O=(E,$,R,z,K)=>{if(R&&m(E,R),z)for(let U=0;U<z.length;U++)m(E,z[U]);if(K){let U=K.subTree;if($===U||Ad(U.type)&&(U.ssContent===$||U.ssFallback===$)){const ue=K.vnode;O(E,ue,ue.scopeId,ue.slotScopeIds,K.parent)}}},C=(E,$,R,z,K,U,ue,le,ie=0)=>{for(let Q=ie;Q<E.length;Q++){const ge=E[Q]=le?Hn(E[Q]):tn(E[Q]);p(null,ge,$,R,z,K,U,ue,le)}},A=(E,$,R,z,K,U,ue)=>{const le=$.el=E.el;let{patchFlag:ie,dynamicChildren:Q,dirs:ge}=$;ie|=E.patchFlag&16;const de=E.props||Be,ve=$.props||Be;let ye;if(R&&eo(R,!1),(ye=ve.onVnodeBeforeUpdate)&&Zt(ye,R,$,E),ge&&Qn($,E,R,"beforeUpdate"),R&&eo(R,!0),(de.innerHTML&&ve.innerHTML==null||de.textContent&&ve.textContent==null)&&u(le,""),Q?I(E.dynamicChildren,Q,le,R,z,gl($,K),U):ue||oe(E,$,le,null,R,z,gl($,K),U,!1),ie>0){if(ie&16)P(le,de,ve,R,K);else if(ie&2&&de.class!==ve.class&&i(le,"class",null,ve.class,K),ie&4&&i(le,"style",de.style,ve.style,K),ie&8){const Pe=$.dynamicProps;for(let Me=0;Me<Pe.length;Me++){const De=Pe[Me],xt=de[De],bt=ve[De];(bt!==xt||De==="value")&&i(le,De,xt,bt,K,R)}}ie&1&&E.children!==$.children&&u(le,$.children)}else!ue&&Q==null&&P(le,de,ve,R,K);((ye=ve.onVnodeUpdated)||ge)&&vt(()=>{ye&&Zt(ye,R,$,E),ge&&Qn($,E,R,"updated")},z)},I=(E,$,R,z,K,U,ue)=>{for(let le=0;le<$.length;le++){const ie=E[le],Q=$[le],ge=ie.el&&(ie.type===qe||!lo(ie,Q)||ie.shapeFlag&198)?d(ie.el):R;p(ie,Q,ge,null,z,K,U,ue,!0)}},P=(E,$,R,z,K)=>{if($!==R){if($!==Be)for(const U in $)!ua(U)&&!(U in R)&&i(E,U,$[U],null,K,z);for(const U in R){if(ua(U))continue;const ue=R[U],le=$[U];ue!==le&&U!=="value"&&i(E,U,le,ue,K,z)}"value"in R&&i(E,"value",$.value,R.value,K)}},k=(E,$,R,z,K,U,ue,le,ie)=>{const Q=$.el=E?E.el:r(""),ge=$.anchor=E?E.anchor:r("");let{patchFlag:de,dynamicChildren:ve,slotScopeIds:ye}=$;ye&&(le=le?le.concat(ye):ye),E==null?(o(Q,R,z),o(ge,R,z),C($.children||[],R,ge,K,U,ue,le,ie)):de>0&&de&64&&ve&&E.dynamicChildren?(I(E.dynamicChildren,ve,R,K,U,ue,le),($.key!=null||K&&$===K.subTree)&&Br(E,$,!0)):oe(E,$,R,ge,K,U,ue,le,ie)},D=(E,$,R,z,K,U,ue,le,ie)=>{$.slotScopeIds=le,E==null?$.shapeFlag&512?K.ctx.activate($,R,z,ue,ie):j($,R,z,K,U,ue,ie):ae(E,$,ie)},j=(E,$,R,z,K,U,ue)=>{const le=E.component=Ev(E,z,K);if(zi(E)&&(le.ctx.renderer=X),Pv(le,!1,ue),le.asyncDep){if(K&&K.registerDep(le,F,ue),!E.el){const ie=le.subTree=f(tt);w(null,ie,$,R)}}else F(le,E,$,R,K,U,ue)},ae=(E,$,R)=>{const z=$.component=E.component;if(bv(E,$,R))if(z.asyncDep&&!z.asyncResolved){ne(z,$,R);return}else z.next=$,z.update();else $.el=E.el,z.vnode=$},F=(E,$,R,z,K,U,ue)=>{const le=()=>{if(E.isMounted){let{next:de,bu:ve,u:ye,parent:Pe,vnode:Me}=E;{const Gt=Ed(E);if(Gt){de&&(de.el=Me.el,ne(E,de,ue)),Gt.asyncDep.then(()=>{E.isUnmounted||le()});return}}let De=de,xt;eo(E,!1),de?(de.el=Me.el,ne(E,de,ue)):de=Me,ve&&cl(ve),(xt=de.props&&de.props.onVnodeBeforeUpdate)&&Zt(xt,Pe,de,Me),eo(E,!0);const bt=Os(E),qt=E.subTree;E.subTree=bt,p(qt,bt,d(qt.el),_(qt),E,K,U),de.el=bt.el,De===null&&yv(E,bt.el),ye&&vt(ye,K),(xt=de.props&&de.props.onVnodeUpdated)&&vt(()=>Zt(xt,Pe,de,Me),K)}else{let de;const{el:ve,props:ye}=$,{bm:Pe,m:Me,parent:De,root:xt,type:bt}=E,qt=ma($);eo(E,!1),Pe&&cl(Pe),!qt&&(de=ye&&ye.onVnodeBeforeMount)&&Zt(de,De,$),eo(E,!0);{xt.ce&&xt.ce._injectChildStyle(bt);const Gt=E.subTree=Os(E);p(null,Gt,R,z,E,K,U),$.el=Gt.el}if(Me&&vt(Me,K),!qt&&(de=ye&&ye.onVnodeMounted)){const Gt=$;vt(()=>Zt(de,De,Gt),K)}($.shapeFlag&256||De&&ma(De.vnode)&&De.vnode.shapeFlag&256)&&E.a&&vt(E.a,K),E.isMounted=!0,$=R=z=null}};E.scope.on();const ie=E.effect=new Iu(le);E.scope.off();const Q=E.update=ie.run.bind(ie),ge=E.job=ie.runIfDirty.bind(ie);ge.i=E,ge.id=E.uid,ie.scheduler=()=>Ir(ge),eo(E,!0),Q()},ne=(E,$,R)=>{$.component=E;const z=E.vnode.props;E.vnode=$,E.next=null,tv(E,$.props,z,R),iv(E,$.children,R),Sn(),ps(E),Cn()},oe=(E,$,R,z,K,U,ue,le,ie=!1)=>{const Q=E&&E.children,ge=E?E.shapeFlag:0,de=$.children,{patchFlag:ve,shapeFlag:ye}=$;if(ve>0){if(ve&128){Ee(Q,de,R,z,K,U,ue,le,ie);return}else if(ve&256){Te(Q,de,R,z,K,U,ue,le,ie);return}}ye&8?(ge&16&&ce(Q,K,U),de!==Q&&u(R,de)):ge&16?ye&16?Ee(Q,de,R,z,K,U,ue,le,ie):ce(Q,K,U,!0):(ge&8&&u(R,""),ye&16&&C(de,R,z,K,U,ue,le,ie))},Te=(E,$,R,z,K,U,ue,le,ie)=>{E=E||Io,$=$||Io;const Q=E.length,ge=$.length,de=Math.min(Q,ge);let ve;for(ve=0;ve<de;ve++){const ye=$[ve]=ie?Hn($[ve]):tn($[ve]);p(E[ve],ye,R,null,K,U,ue,le,ie)}Q>ge?ce(E,K,U,!0,!1,de):C($,R,z,K,U,ue,le,ie,de)},Ee=(E,$,R,z,K,U,ue,le,ie)=>{let Q=0;const ge=$.length;let de=E.length-1,ve=ge-1;for(;Q<=de&&Q<=ve;){const ye=E[Q],Pe=$[Q]=ie?Hn($[Q]):tn($[Q]);if(lo(ye,Pe))p(ye,Pe,R,null,K,U,ue,le,ie);else break;Q++}for(;Q<=de&&Q<=ve;){const ye=E[de],Pe=$[ve]=ie?Hn($[ve]):tn($[ve]);if(lo(ye,Pe))p(ye,Pe,R,null,K,U,ue,le,ie);else break;de--,ve--}if(Q>de){if(Q<=ve){const ye=ve+1,Pe=ye<ge?$[ye].el:z;for(;Q<=ve;)p(null,$[Q]=ie?Hn($[Q]):tn($[Q]),R,Pe,K,U,ue,le,ie),Q++}}else if(Q>ve)for(;Q<=de;)N(E[Q],K,U,!0),Q++;else{const ye=Q,Pe=Q,Me=new Map;for(Q=Pe;Q<=ve;Q++){const St=$[Q]=ie?Hn($[Q]):tn($[Q]);St.key!=null&&Me.set(St.key,Q)}let De,xt=0;const bt=ve-Pe+1;let qt=!1,Gt=0;const Zo=new Array(bt);for(Q=0;Q<bt;Q++)Zo[Q]=0;for(Q=ye;Q<=de;Q++){const St=E[Q];if(xt>=bt){N(St,K,U,!0);continue}let Xt;if(St.key!=null)Xt=Me.get(St.key);else for(De=Pe;De<=ve;De++)if(Zo[De-Pe]===0&&lo(St,$[De])){Xt=De;break}Xt===void 0?N(St,K,U,!0):(Zo[Xt-Pe]=Q+1,Xt>=Gt?Gt=Xt:qt=!0,p(St,$[Xt],R,null,K,U,ue,le,ie),xt++)}const hs=qt?cv(Zo):Io;for(De=hs.length-1,Q=bt-1;Q>=0;Q--){const St=Pe+Q,Xt=$[St],ms=St+1<ge?$[St+1].el:z;Zo[Q]===0?p(null,Xt,R,ms,K,U,ue,le,ie):qt&&(De<0||Q!==hs[De]?re(Xt,R,ms,2):De--)}}},re=(E,$,R,z,K=null)=>{const{el:U,type:ue,transition:le,children:ie,shapeFlag:Q}=E;if(Q&6){re(E.component.subTree,$,R,z);return}if(Q&128){E.suspense.move($,R,z);return}if(Q&64){ue.move(E,$,R,X);return}if(ue===qe){o(U,$,R);for(let de=0;de<ie.length;de++)re(ie[de],$,R,z);o(E.anchor,$,R);return}if(ue===bl){x(E,$,R);return}if(z!==2&&Q&1&&le)if(z===0)le.beforeEnter(U),o(U,$,R),vt(()=>le.enter(U),K);else{const{leave:de,delayLeave:ve,afterLeave:ye}=le,Pe=()=>{E.ctx.isUnmounted?a(U):o(U,$,R)},Me=()=>{de(U,()=>{Pe(),ye&&ye()})};ve?ve(U,Pe,Me):Me()}else o(U,$,R)},N=(E,$,R,z=!1,K=!1)=>{const{type:U,props:ue,ref:le,children:ie,dynamicChildren:Q,shapeFlag:ge,patchFlag:de,dirs:ve,cacheIndex:ye}=E;if(de===-2&&(K=!1),le!=null&&(Sn(),wi(le,null,R,E,!0),Cn()),ye!=null&&($.renderCache[ye]=void 0),ge&256){$.ctx.deactivate(E);return}const Pe=ge&1&&ve,Me=!ma(E);let De;if(Me&&(De=ue&&ue.onVnodeBeforeUnmount)&&Zt(De,$,E),ge&6)q(E.component,R,z);else{if(ge&128){E.suspense.unmount(R,z);return}Pe&&Qn(E,null,$,"beforeUnmount"),ge&64?E.type.remove(E,$,R,X,z):Q&&!Q.hasOnce&&(U!==qe||de>0&&de&64)?ce(Q,$,R,!1,!0):(U===qe&&de&384||!K&&ge&16)&&ce(ie,$,R),z&&ee(E)}(Me&&(De=ue&&ue.onVnodeUnmounted)||Pe)&&vt(()=>{De&&Zt(De,$,E),Pe&&Qn(E,null,$,"unmounted")},R)},ee=E=>{const{type:$,el:R,anchor:z,transition:K}=E;if($===qe){fe(R,z);return}if($===bl){S(E);return}const U=()=>{a(R),K&&!K.persisted&&K.afterLeave&&K.afterLeave()};if(E.shapeFlag&1&&K&&!K.persisted){const{leave:ue,delayLeave:le}=K,ie=()=>ue(R,U);le?le(E.el,U,ie):ie()}else U()},fe=(E,$)=>{let R;for(;E!==$;)R=h(E),a(E),E=R;a($)},q=(E,$,R)=>{const{bum:z,scope:K,job:U,subTree:ue,um:le,m:ie,a:Q,parent:ge,slots:{__:de}}=E;As(ie),As(Q),z&&cl(z),ge&&be(de)&&de.forEach(ve=>{ge.renderCache[ve]=void 0}),K.stop(),U&&(U.flags|=8,N(ue,E,$,R)),le&&vt(le,$),vt(()=>{E.isUnmounted=!0},$),$&&$.pendingBranch&&!$.isUnmounted&&E.asyncDep&&!E.asyncResolved&&E.suspenseId===$.pendingId&&($.deps--,$.deps===0&&$.resolve())},ce=(E,$,R,z=!1,K=!1,U=0)=>{for(let ue=U;ue<E.length;ue++)N(E[ue],$,R,z,K)},_=E=>{if(E.shapeFlag&6)return _(E.component.subTree);if(E.shapeFlag&128)return E.suspense.next();const $=h(E.anchor||E.el),R=$&&$[td];return R?h(R):$};let V=!1;const L=(E,$,R)=>{E==null?$._vnode&&N($._vnode,null,null,!0):p($._vnode||null,E,$,null,null,null,R),$._vnode=E,V||(V=!0,ps(),Ju(),V=!1)},X={p,um:N,m:re,r:ee,mt:j,mc:C,pc:oe,pbc:I,n:_,o:e};return{render:L,hydrate:void 0,createApp:Qm(L)}}function gl({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function eo({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function sv(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Br(e,t,n=!1){const o=e.children,a=t.children;if(be(o)&&be(a))for(let i=0;i<o.length;i++){const l=o[i];let r=a[i];r.shapeFlag&1&&!r.dynamicChildren&&((r.patchFlag<=0||r.patchFlag===32)&&(r=a[i]=Hn(a[i]),r.el=l.el),!n&&r.patchFlag!==-2&&Br(l,r)),r.type===La&&(r.el=l.el),r.type===tt&&!r.el&&(r.el=l.el)}}function cv(e){const t=e.slice(),n=[0];let o,a,i,l,r;const s=e.length;for(o=0;o<s;o++){const c=e[o];if(c!==0){if(a=n[n.length-1],e[a]<c){t[o]=a,n.push(o);continue}for(i=0,l=n.length-1;i<l;)r=i+l>>1,e[n[r]]<c?i=r+1:l=r;c<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}for(i=n.length,l=n[i-1];i-- >0;)n[i]=l,l=t[l];return n}function Ed(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ed(t)}function As(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const uv=Symbol.for("v-scx"),dv=()=>rt(uv);function Yo(e,t){return Mr(e,null,t)}function te(e,t,n){return Mr(e,t,n)}function Mr(e,t,n=Be){const{immediate:o,deep:a,flush:i,once:l}=n,r=Qe({},n),s=t&&o||!t&&i!=="post";let c;if(Pa){if(i==="sync"){const m=dv();c=m.__watcherHandles||(m.__watcherHandles=[])}else if(!s){const m=()=>{};return m.stop=nn,m.resume=nn,m.pause=nn,m}}const u=nt;r.call=(m,v,p)=>Nt(m,u,v,p);let d=!1;i==="post"?r.scheduler=m=>{vt(m,u&&u.suspense)}:i!=="sync"&&(d=!0,r.scheduler=(m,v)=>{v?m():Ir(m)}),r.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const h=Em(e,t,r);return Pa&&(c?c.push(h):s&&h()),h}function fv(e,t,n){const o=this.proxy,a=He(e)?e.includes(".")?Pd(o,e):()=>o[e]:e.bind(o,o);let i;pe(t)?i=t:(i=t.handler,n=t);const l=Va(this),r=Mr(a,i.bind(o),n);return l(),r}function Pd(e,t){const n=t.split(".");return()=>{let o=e;for(let a=0;a<n.length&&o;a++)o=o[n[a]];return o}}const hv=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${$t(t)}Modifiers`]||e[`${Pn(t)}Modifiers`];function mv(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||Be;let a=n;const i=t.startsWith("update:"),l=i&&hv(o,t.slice(7));l&&(l.trim&&(a=n.map(u=>He(u)?u.trim():u)),l.number&&(a=n.map(Wh)));let r,s=o[r=sl(t)]||o[r=sl($t(t))];!s&&i&&(s=o[r=sl(Pn(t))]),s&&Nt(s,e,6,a);const c=o[r+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[r])return;e.emitted[r]=!0,Nt(c,e,6,a)}}function $d(e,t,n=!1){const o=t.emitsCache,a=o.get(e);if(a!==void 0)return a;const i=e.emits;let l={},r=!1;if(!pe(e)){const s=c=>{const u=$d(c,t,!0);u&&(r=!0,Qe(l,u))};!n&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}return!i&&!r?(Fe(e)&&o.set(e,null),null):(be(i)?i.forEach(s=>l[s]=null):Qe(l,i),Fe(e)&&o.set(e,l),l)}function ji(e,t){return!e||!Ii(t)?!1:(t=t.slice(2).replace(/Once$/,""),Re(e,t[0].toLowerCase()+t.slice(1))||Re(e,Pn(t))||Re(e,t))}function Os(e){const{type:t,vnode:n,proxy:o,withProxy:a,propsOptions:[i],slots:l,attrs:r,emit:s,render:c,renderCache:u,props:d,data:h,setupState:m,ctx:v,inheritAttrs:p}=e,b=pi(e);let w,g;try{if(n.shapeFlag&4){const S=a||o,y=S;w=tn(c.call(y,S,u,d,m,h,v)),g=r}else{const S=t;w=tn(S.length>1?S(d,{attrs:r,slots:l,emit:s}):S(d,null)),g=t.props?r:vv(r)}}catch(S){ga.length=0,Ni(S,e,1),w=f(tt)}let x=w;if(g&&p!==!1){const S=Object.keys(g),{shapeFlag:y}=x;S.length&&y&7&&(i&&S.some(Sr)&&(g=gv(g,i)),x=Kn(x,g,!1,!0))}return n.dirs&&(x=Kn(x,null,!1,!0),x.dirs=x.dirs?x.dirs.concat(n.dirs):n.dirs),n.transition&&_a(x,n.transition),w=x,pi(b),w}const vv=e=>{let t;for(const n in e)(n==="class"||n==="style"||Ii(n))&&((t||(t={}))[n]=e[n]);return t},gv=(e,t)=>{const n={};for(const o in e)(!Sr(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function bv(e,t,n){const{props:o,children:a,component:i}=e,{props:l,children:r,patchFlag:s}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&s>=0){if(s&1024)return!0;if(s&16)return o?Is(o,l,c):!!l;if(s&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const h=u[d];if(l[h]!==o[h]&&!ji(c,h))return!0}}}else return(a||r)&&(!r||!r.$stable)?!0:o===l?!1:o?l?Is(o,l,c):!0:!!l;return!1}function Is(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let a=0;a<o.length;a++){const i=o[a];if(t[i]!==e[i]&&!ji(n,i))return!0}return!1}function yv({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ad=e=>e.__isSuspense;function pv(e,t){t&&t.pendingBranch?be(e)?t.effects.push(...e):t.effects.push(e):Am(e)}const qe=Symbol.for("v-fgt"),La=Symbol.for("v-txt"),tt=Symbol.for("v-cmt"),bl=Symbol.for("v-stc"),ga=[];let Ct=null;function Od(e=!1){ga.push(Ct=e?null:[])}function wv(){ga.pop(),Ct=ga[ga.length-1]||null}let ka=1;function Rs(e,t=!1){ka+=e,e<0&&Ct&&t&&(Ct.hasOnce=!0)}function Id(e){return e.dynamicChildren=ka>0?Ct||Io:null,wv(),ka>0&&Ct&&Ct.push(e),e}function xv(e,t,n,o,a,i){return Id(Dd(e,t,n,o,a,i,!0))}function Sv(e,t,n,o,a){return Id(f(e,t,n,o,a,!0))}function Ea(e){return e?e.__v_isVNode===!0:!1}function lo(e,t){return e.type===t.type&&e.key===t.key}const Rd=({key:e})=>e??null,di=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?He(e)||ot(e)||pe(e)?{i:wt,r:e,k:t,f:!!n}:e:null);function Dd(e,t=null,n=null,o=0,a=null,i=e===qe?0:1,l=!1,r=!1){const s={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Rd(t),ref:t&&di(t),scopeId:ed,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:a,dynamicChildren:null,appContext:null,ctx:wt};return r?(Vr(s,n),i&128&&e.normalize(s)):n&&(s.shapeFlag|=He(n)?8:16),ka>0&&!l&&Ct&&(s.patchFlag>0||i&6)&&s.patchFlag!==32&&Ct.push(s),s}const f=Cv;function Cv(e,t=null,n=null,o=0,a=null,i=!1){if((!e||e===Wm)&&(e=tt),Ea(e)){const r=Kn(e,t,!0);return n&&Vr(r,n),ka>0&&!i&&Ct&&(r.shapeFlag&6?Ct[Ct.indexOf(e)]=r:Ct.push(r)),r.patchFlag=-2,r}if(Rv(e)&&(e=e.__vccOpts),t){t=Tv(t);let{class:r,style:s}=t;r&&!He(r)&&(t.class=Vi(r)),Fe(s)&&(Or(s)&&!be(s)&&(s=Qe({},s)),t.style=Li(s))}const l=He(e)?1:Ad(e)?128:nd(e)?64:Fe(e)?4:pe(e)?2:0;return Dd(e,t,n,o,a,l,i,!0)}function Tv(e){return e?Or(e)||xd(e)?Qe({},e):e:null}function Kn(e,t,n=!1,o=!1){const{props:a,ref:i,patchFlag:l,children:r,transition:s}=e,c=t?Ce(a||{},t):a,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Rd(c),ref:t&&t.ref?n&&i?be(i)?i.concat(di(t)):[i,di(t)]:di(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:r,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==qe?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:s,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Kn(e.ssContent),ssFallback:e.ssFallback&&Kn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return s&&o&&_a(u,s.clone(u)),u}function Lr(e=" ",t=0){return f(La,null,e,t)}function nk(e="",t=!1){return t?(Od(),Sv(tt,null,e)):f(tt,null,e)}function tn(e){return e==null||typeof e=="boolean"?f(tt):be(e)?f(qe,null,e.slice()):Ea(e)?Hn(e):f(La,null,String(e))}function Hn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Kn(e)}function Vr(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(be(t))n=16;else if(typeof t=="object")if(o&65){const a=t.default;a&&(a._c&&(a._d=!1),Vr(e,a()),a._c&&(a._d=!0));return}else{n=32;const a=t._;!a&&!xd(t)?t._ctx=wt:a===3&&wt&&(wt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else pe(t)?(t={default:t,_ctx:wt},n=32):(t=String(t),o&64?(n=16,t=[Lr(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ce(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const a in o)if(a==="class")t.class!==o.class&&(t.class=Vi([t.class,o.class]));else if(a==="style")t.style=Li([t.style,o.style]);else if(Ii(a)){const i=t[a],l=o[a];l&&i!==l&&!(be(i)&&i.includes(l))&&(t[a]=i?[].concat(i,l):l)}else a!==""&&(t[a]=o[a])}return t}function Zt(e,t,n,o=null){Nt(e,t,7,[n,o])}const _v=yd();let kv=0;function Ev(e,t,n){const o=e.type,a=(t?t.appContext:e.appContext)||_v,i={uid:kv++,vnode:e,type:o,parent:t,appContext:a,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new em(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(a.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Cd(o,a),emitsOptions:$d(o,a),emit:null,emitted:null,propsDefaults:Be,inheritAttrs:o.inheritAttrs,ctx:Be,data:Be,props:Be,attrs:Be,slots:Be,refs:Be,setupState:Be,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=mv.bind(null,i),e.ce&&e.ce(i),i}let nt=null;const jt=()=>nt||wt;let Si,tr;{const e=Mi(),t=(n,o)=>{let a;return(a=e[n])||(a=e[n]=[]),a.push(o),i=>{a.length>1?a.forEach(l=>l(i)):a[0](i)}};Si=t("__VUE_INSTANCE_SETTERS__",n=>nt=n),tr=t("__VUE_SSR_SETTERS__",n=>Pa=n)}const Va=e=>{const t=nt;return Si(e),e.scope.on(),()=>{e.scope.off(),Si(t)}},Ds=()=>{nt&&nt.scope.off(),Si(null)};function Bd(e){return e.vnode.shapeFlag&4}let Pa=!1;function Pv(e,t=!1,n=!1){t&&tr(t);const{props:o,children:a}=e.vnode,i=Bd(e);ev(e,o,i,t),av(e,a,n||t);const l=i?$v(e,t):void 0;return t&&tr(!1),l}function $v(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ym);const{setup:o}=n;if(o){Sn();const a=e.setupContext=o.length>1?Ov(e):null,i=Va(e),l=Ma(o,e,0,[e.props,a]),r=_u(l);if(Cn(),i(),(r||e.sp)&&!ma(e)&&ud(e),r){if(l.then(Ds,Ds),t)return l.then(s=>{Bs(e,s)}).catch(s=>{Ni(s,e,0)});e.asyncDep=l}else Bs(e,l)}else Md(e)}function Bs(e,t,n){pe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Fe(t)&&(e.setupState=Gu(t)),Md(e)}function Md(e,t,n){const o=e.type;e.render||(e.render=o.render||nn);{const a=Va(e);Sn();try{Km(e)}finally{Cn(),a()}}}const Av={get(e,t){return lt(e,"get",""),e[t]}};function Ov(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Av),slots:e.slots,emit:e.emit,expose:t}}function Wi(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Gu(wm(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in va)return va[n](e)},has(t,n){return n in t||n in va}})):e.proxy}function Iv(e,t=!0){return pe(e)?e.displayName||e.name:e.name||t&&e.__name}function Rv(e){return pe(e)&&"__vccOpts"in e}const B=(e,t)=>_m(e,t,Pa);function Fr(e,t,n){const o=arguments.length;return o===2?Fe(t)&&!be(t)?Ea(t)?f(e,null,[t]):f(e,t):f(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&Ea(n)&&(n=[n]),f(e,t,n))}const Dv="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let nr;const Ms=typeof window<"u"&&window.trustedTypes;if(Ms)try{nr=Ms.createPolicy("vue",{createHTML:e=>e})}catch{}const Ld=nr?e=>nr.createHTML(e):e=>e,Bv="http://www.w3.org/2000/svg",Mv="http://www.w3.org/1998/Math/MathML",vn=typeof document<"u"?document:null,Ls=vn&&vn.createElement("template"),Lv={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const a=t==="svg"?vn.createElementNS(Bv,e):t==="mathml"?vn.createElementNS(Mv,e):n?vn.createElement(e,{is:n}):vn.createElement(e);return e==="select"&&o&&o.multiple!=null&&a.setAttribute("multiple",o.multiple),a},createText:e=>vn.createTextNode(e),createComment:e=>vn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>vn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,a,i){const l=n?n.previousSibling:t.lastChild;if(a&&(a===i||a.nextSibling))for(;t.insertBefore(a.cloneNode(!0),n),!(a===i||!(a=a.nextSibling)););else{Ls.innerHTML=Ld(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const r=Ls.content;if(o==="svg"||o==="mathml"){const s=r.firstChild;for(;s.firstChild;)r.appendChild(s.firstChild);r.removeChild(s)}t.insertBefore(r,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Rn="transition",Qo="animation",$a=Symbol("_vtc"),Vd={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Vv=Qe({},id,Vd),Fv=e=>(e.displayName="Transition",e.props=Vv,e),Ui=Fv((e,{slots:t})=>Fr(Bm,Nv(e),t)),to=(e,t=[])=>{be(e)?e.forEach(n=>n(...t)):e&&e(...t)},Vs=e=>e?be(e)?e.some(t=>t.length>1):e.length>1:!1;function Nv(e){const t={};for(const k in e)k in Vd||(t[k]=e[k]);if(e.css===!1)return t;const{name:n="v",type:o,duration:a,enterFromClass:i=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:r=`${n}-enter-to`,appearFromClass:s=i,appearActiveClass:c=l,appearToClass:u=r,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,v=zv(a),p=v&&v[0],b=v&&v[1],{onBeforeEnter:w,onEnter:g,onEnterCancelled:x,onLeave:S,onLeaveCancelled:y,onBeforeAppear:T=w,onAppear:O=g,onAppearCancelled:C=x}=t,A=(k,D,j,ae)=>{k._enterCancelled=ae,no(k,D?u:r),no(k,D?c:l),j&&j()},I=(k,D)=>{k._isLeaving=!1,no(k,d),no(k,m),no(k,h),D&&D()},P=k=>(D,j)=>{const ae=k?O:g,F=()=>A(D,k,j);to(ae,[D,F]),Fs(()=>{no(D,k?s:i),cn(D,k?u:r),Vs(ae)||Ns(D,o,p,F)})};return Qe(t,{onBeforeEnter(k){to(w,[k]),cn(k,i),cn(k,l)},onBeforeAppear(k){to(T,[k]),cn(k,s),cn(k,c)},onEnter:P(!1),onAppear:P(!0),onLeave(k,D){k._isLeaving=!0;const j=()=>I(k,D);cn(k,d),k._enterCancelled?(cn(k,h),js()):(js(),cn(k,h)),Fs(()=>{k._isLeaving&&(no(k,d),cn(k,m),Vs(S)||Ns(k,o,b,j))}),to(S,[k,j])},onEnterCancelled(k){A(k,!1,void 0,!0),to(x,[k])},onAppearCancelled(k){A(k,!0,void 0,!0),to(C,[k])},onLeaveCancelled(k){I(k),to(y,[k])}})}function zv(e){if(e==null)return null;if(Fe(e))return[yl(e.enter),yl(e.leave)];{const t=yl(e);return[t,t]}}function yl(e){return Uh(e)}function cn(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[$a]||(e[$a]=new Set)).add(t)}function no(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.remove(o));const n=e[$a];n&&(n.delete(t),n.size||(e[$a]=void 0))}function Fs(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Hv=0;function Ns(e,t,n,o){const a=e._endId=++Hv,i=()=>{a===e._endId&&o()};if(n!=null)return setTimeout(i,n);const{type:l,timeout:r,propCount:s}=jv(e,t);if(!l)return o();const c=l+"end";let u=0;const d=()=>{e.removeEventListener(c,h),i()},h=m=>{m.target===e&&++u>=s&&d()};setTimeout(()=>{u<s&&d()},r+1),e.addEventListener(c,h)}function jv(e,t){const n=window.getComputedStyle(e),o=v=>(n[v]||"").split(", "),a=o(`${Rn}Delay`),i=o(`${Rn}Duration`),l=zs(a,i),r=o(`${Qo}Delay`),s=o(`${Qo}Duration`),c=zs(r,s);let u=null,d=0,h=0;t===Rn?l>0&&(u=Rn,d=l,h=i.length):t===Qo?c>0&&(u=Qo,d=c,h=s.length):(d=Math.max(l,c),u=d>0?l>c?Rn:Qo:null,h=u?u===Rn?i.length:s.length:0);const m=u===Rn&&/\b(transform|all)(,|$)/.test(o(`${Rn}Property`).toString());return{type:u,timeout:d,propCount:h,hasTransform:m}}function zs(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,o)=>Hs(n)+Hs(e[o])))}function Hs(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function js(){return document.body.offsetHeight}function Wv(e,t,n){const o=e[$a];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ci=Symbol("_vod"),Fd=Symbol("_vsh"),at={beforeMount(e,{value:t},{transition:n}){e[Ci]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):ea(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),ea(e,!0),o.enter(e)):o.leave(e,()=>{ea(e,!1)}):ea(e,t))},beforeUnmount(e,{value:t}){ea(e,t)}};function ea(e,t){e.style.display=t?e[Ci]:"none",e[Fd]=!t}const Uv=Symbol(""),Yv=/(^|;)\s*display\s*:/;function Kv(e,t,n){const o=e.style,a=He(n);let i=!1;if(n&&!a){if(t)if(He(t))for(const l of t.split(";")){const r=l.slice(0,l.indexOf(":")).trim();n[r]==null&&fi(o,r,"")}else for(const l in t)n[l]==null&&fi(o,l,"");for(const l in n)l==="display"&&(i=!0),fi(o,l,n[l])}else if(a){if(t!==n){const l=o[Uv];l&&(n+=";"+l),o.cssText=n,i=Yv.test(n)}}else t&&e.removeAttribute("style");Ci in e&&(e[Ci]=i?o.display:"",e[Fd]&&(o.display="none"))}const Ws=/\s*!important$/;function fi(e,t,n){if(be(n))n.forEach(o=>fi(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=qv(e,t);Ws.test(n)?e.setProperty(Pn(o),n.replace(Ws,""),"important"):e[o]=n}}const Us=["Webkit","Moz","ms"],pl={};function qv(e,t){const n=pl[t];if(n)return n;let o=$t(t);if(o!=="filter"&&o in e)return pl[t]=o;o=Bi(o);for(let a=0;a<Us.length;a++){const i=Us[a]+o;if(i in e)return pl[t]=i}return t}const Ys="http://www.w3.org/1999/xlink";function Ks(e,t,n,o,a,i=Jh(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ys,t.slice(6,t.length)):e.setAttributeNS(Ys,t,n):n==null||i&&!$u(n)?e.removeAttribute(t):e.setAttribute(t,i?"":qn(n)?String(n):n)}function qs(e,t,n,o,a){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Ld(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const r=i==="OPTION"?e.getAttribute("value")||"":e.value,s=n==null?e.type==="checkbox"?"on":"":String(n);(r!==s||!("_value"in e))&&(e.value=s),n==null&&e.removeAttribute(t),e._value=n;return}let l=!1;if(n===""||n==null){const r=typeof e[t];r==="boolean"?n=$u(n):n==null&&r==="string"?(n="",l=!0):r==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(a||t)}function Gv(e,t,n,o){e.addEventListener(t,n,o)}function Xv(e,t,n,o){e.removeEventListener(t,n,o)}const Gs=Symbol("_vei");function Zv(e,t,n,o,a=null){const i=e[Gs]||(e[Gs]={}),l=i[t];if(o&&l)l.value=o;else{const[r,s]=Jv(t);if(o){const c=i[t]=tg(o,a);Gv(e,r,c,s)}else l&&(Xv(e,r,l,s),i[t]=void 0)}}const Xs=/(?:Once|Passive|Capture)$/;function Jv(e){let t;if(Xs.test(e)){t={};let o;for(;o=e.match(Xs);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Pn(e.slice(2)),t]}let wl=0;const Qv=Promise.resolve(),eg=()=>wl||(Qv.then(()=>wl=0),wl=Date.now());function tg(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;Nt(ng(o,n.value),t,5,[o])};return n.value=e,n.attached=eg(),n}function ng(e,t){if(be(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>a=>!a._stopped&&o&&o(a))}else return t}const Zs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,og=(e,t,n,o,a,i)=>{const l=a==="svg";t==="class"?Wv(e,o,l):t==="style"?Kv(e,n,o):Ii(t)?Sr(t)||Zv(e,t,n,o,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ag(e,t,o,l))?(qs(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ks(e,t,o,l,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!He(o))?qs(e,$t(t),o,i,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Ks(e,t,o,l))};function ag(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&Zs(t)&&pe(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const a=e.tagName;if(a==="IMG"||a==="VIDEO"||a==="CANVAS"||a==="SOURCE")return!1}return Zs(t)&&He(n)?!1:t in e}const ig=["ctrl","shift","alt","meta"],lg={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ig.some(n=>e[`${n}Key`]&&!t.includes(n))},ok=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(a,...i)=>{for(let l=0;l<t.length;l++){const r=lg[t[l]];if(r&&r(a,t))return}return e(a,...i)})},rg={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},sg=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=a=>{if(!("key"in a))return;const i=Pn(a.key);if(t.some(l=>l===i||rg[l]===i))return e(a)})},cg=Qe({patchProp:og},Lv);let Js;function ug(){return Js||(Js=lv(cg))}const Nd=(...e)=>{const t=ug().createApp(...e),{mount:n}=t;return t.mount=o=>{const a=fg(o);if(!a)return;const i=t._component;!pe(i)&&!i.render&&!i.template&&(i.template=a.innerHTML),a.nodeType===1&&(a.textContent="");const l=n(a,!1,dg(a));return a instanceof Element&&(a.removeAttribute("v-cloak"),a.setAttribute("data-v-app","")),l},t};function dg(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function fg(e){return He(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ao=typeof document<"u";function zd(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function hg(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&zd(e.default)}const Ie=Object.assign;function xl(e,t){const n={};for(const o in t){const a=t[o];n[o]=zt(a)?a.map(e):e(a)}return n}const ba=()=>{},zt=Array.isArray,Hd=/#/g,mg=/&/g,vg=/\//g,gg=/=/g,bg=/\?/g,jd=/\+/g,yg=/%5B/g,pg=/%5D/g,Wd=/%5E/g,wg=/%60/g,Ud=/%7B/g,xg=/%7C/g,Yd=/%7D/g,Sg=/%20/g;function Nr(e){return encodeURI(""+e).replace(xg,"|").replace(yg,"[").replace(pg,"]")}function Cg(e){return Nr(e).replace(Ud,"{").replace(Yd,"}").replace(Wd,"^")}function or(e){return Nr(e).replace(jd,"%2B").replace(Sg,"+").replace(Hd,"%23").replace(mg,"%26").replace(wg,"`").replace(Ud,"{").replace(Yd,"}").replace(Wd,"^")}function Tg(e){return or(e).replace(gg,"%3D")}function _g(e){return Nr(e).replace(Hd,"%23").replace(bg,"%3F")}function kg(e){return e==null?"":_g(e).replace(vg,"%2F")}function Aa(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Eg=/\/$/,Pg=e=>e.replace(Eg,"");function Sl(e,t,n="/"){let o,a={},i="",l="";const r=t.indexOf("#");let s=t.indexOf("?");return r<s&&r>=0&&(s=-1),s>-1&&(o=t.slice(0,s),i=t.slice(s+1,r>-1?r:t.length),a=e(i)),r>-1&&(o=o||t.slice(0,r),l=t.slice(r,t.length)),o=Ig(o??t,n),{fullPath:o+(i&&"?")+i+l,path:o,query:a,hash:Aa(l)}}function $g(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Qs(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ag(e,t,n){const o=t.matched.length-1,a=n.matched.length-1;return o>-1&&o===a&&No(t.matched[o],n.matched[a])&&Kd(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function No(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Kd(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Og(e[n],t[n]))return!1;return!0}function Og(e,t){return zt(e)?ec(e,t):zt(t)?ec(t,e):e===t}function ec(e,t){return zt(t)?e.length===t.length&&e.every((n,o)=>n===t[o]):e.length===1&&e[0]===t}function Ig(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),a=o[o.length-1];(a===".."||a===".")&&o.push("");let i=n.length-1,l,r;for(l=0;l<o.length;l++)if(r=o[l],r!==".")if(r==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+o.slice(l).join("/")}const Dn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Oa;(function(e){e.pop="pop",e.push="push"})(Oa||(Oa={}));var ya;(function(e){e.back="back",e.forward="forward",e.unknown=""})(ya||(ya={}));function Rg(e){if(!e)if(Ao){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Pg(e)}const Dg=/^[^#]+#/;function Bg(e,t){return e.replace(Dg,"#")+t}function Mg(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}const Yi=()=>({left:window.scrollX,top:window.scrollY});function Lg(e){let t;if("el"in e){const n=e.el,o=typeof n=="string"&&n.startsWith("#"),a=typeof n=="string"?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!a)return;t=Mg(a,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function tc(e,t){return(history.state?history.state.position-t:-1)+e}const ar=new Map;function Vg(e,t){ar.set(e,t)}function Fg(e){const t=ar.get(e);return ar.delete(e),t}let Ng=()=>location.protocol+"//"+location.host;function qd(e,t){const{pathname:n,search:o,hash:a}=t,i=e.indexOf("#");if(i>-1){let r=a.includes(e.slice(i))?e.slice(i).length:1,s=a.slice(r);return s[0]!=="/"&&(s="/"+s),Qs(s,"")}return Qs(n,e)+o+a}function zg(e,t,n,o){let a=[],i=[],l=null;const r=({state:h})=>{const m=qd(e,location),v=n.value,p=t.value;let b=0;if(h){if(n.value=m,t.value=h,l&&l===v){l=null;return}b=p?h.position-p.position:0}else o(m);a.forEach(w=>{w(n.value,v,{delta:b,type:Oa.pop,direction:b?b>0?ya.forward:ya.back:ya.unknown})})};function s(){l=n.value}function c(h){a.push(h);const m=()=>{const v=a.indexOf(h);v>-1&&a.splice(v,1)};return i.push(m),m}function u(){const{history:h}=window;h.state&&h.replaceState(Ie({},h.state,{scroll:Yi()}),"")}function d(){for(const h of i)h();i=[],window.removeEventListener("popstate",r),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",r),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:s,listen:c,destroy:d}}function nc(e,t,n,o=!1,a=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:a?Yi():null}}function Hg(e){const{history:t,location:n}=window,o={value:qd(e,n)},a={value:t.state};a.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(s,c,u){const d=e.indexOf("#"),h=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+s:Ng()+e+s;try{t[u?"replaceState":"pushState"](c,"",h),a.value=c}catch(m){console.error(m),n[u?"replace":"assign"](h)}}function l(s,c){const u=Ie({},t.state,nc(a.value.back,s,a.value.forward,!0),c,{position:a.value.position});i(s,u,!0),o.value=s}function r(s,c){const u=Ie({},a.value,t.state,{forward:s,scroll:Yi()});i(u.current,u,!0);const d=Ie({},nc(o.value,s,null),{position:u.position+1},c);i(s,d,!1),o.value=s}return{location:o,state:a,push:r,replace:l}}function jg(e){e=Rg(e);const t=Hg(e),n=zg(e,t.state,t.location,t.replace);function o(i,l=!0){l||n.pauseListeners(),history.go(i)}const a=Ie({location:"",base:e,go:o,createHref:Bg.bind(null,e)},t,n);return Object.defineProperty(a,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(a,"state",{enumerable:!0,get:()=>t.state.value}),a}function Wg(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),jg(e)}function Ug(e){return typeof e=="string"||e&&typeof e=="object"}function Gd(e){return typeof e=="string"||typeof e=="symbol"}const Xd=Symbol("");var oc;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(oc||(oc={}));function zo(e,t){return Ie(new Error,{type:e,[Xd]:!0},t)}function un(e,t){return e instanceof Error&&Xd in e&&(t==null||!!(e.type&t))}const ac="[^/]+?",Yg={sensitive:!1,strict:!1,start:!0,end:!0},Kg=/[.+*?^${}()[\]/\\]/g;function qg(e,t){const n=Ie({},Yg,t),o=[];let a=n.start?"^":"";const i=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(a+="/");for(let d=0;d<c.length;d++){const h=c[d];let m=40+(n.sensitive?.25:0);if(h.type===0)d||(a+="/"),a+=h.value.replace(Kg,"\\$&"),m+=40;else if(h.type===1){const{value:v,repeatable:p,optional:b,regexp:w}=h;i.push({name:v,repeatable:p,optional:b});const g=w||ac;if(g!==ac){m+=10;try{new RegExp(`(${g})`)}catch(S){throw new Error(`Invalid custom RegExp for param "${v}" (${g}): `+S.message)}}let x=p?`((?:${g})(?:/(?:${g}))*)`:`(${g})`;d||(x=b&&c.length<2?`(?:/${x})`:"/"+x),b&&(x+="?"),a+=x,m+=20,b&&(m+=-8),p&&(m+=-20),g===".*"&&(m+=-50)}u.push(m)}o.push(u)}if(n.strict&&n.end){const c=o.length-1;o[c][o[c].length-1]+=.7000000000000001}n.strict||(a+="/?"),n.end?a+="$":n.strict&&!a.endsWith("/")&&(a+="(?:/|$)");const l=new RegExp(a,n.sensitive?"":"i");function r(c){const u=c.match(l),d={};if(!u)return null;for(let h=1;h<u.length;h++){const m=u[h]||"",v=i[h-1];d[v.name]=m&&v.repeatable?m.split("/"):m}return d}function s(c){let u="",d=!1;for(const h of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const m of h)if(m.type===0)u+=m.value;else if(m.type===1){const{value:v,repeatable:p,optional:b}=m,w=v in c?c[v]:"";if(zt(w)&&!p)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const g=zt(w)?w.join("/"):w;if(!g)if(b)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${v}"`);u+=g}}return u||"/"}return{re:l,score:o,keys:i,parse:r,stringify:s}}function Gg(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Zd(e,t){let n=0;const o=e.score,a=t.score;for(;n<o.length&&n<a.length;){const i=Gg(o[n],a[n]);if(i)return i;n++}if(Math.abs(a.length-o.length)===1){if(ic(o))return 1;if(ic(a))return-1}return a.length-o.length}function ic(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Xg={type:0,value:""},Zg=/[a-zA-Z0-9_]/;function Jg(e){if(!e)return[[]];if(e==="/")return[[Xg]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${c}": ${m}`)}let n=0,o=n;const a=[];let i;function l(){i&&a.push(i),i=[]}let r=0,s,c="",u="";function d(){c&&(n===0?i.push({type:0,value:c}):n===1||n===2||n===3?(i.length>1&&(s==="*"||s==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:s==="*"||s==="+",optional:s==="*"||s==="?"})):t("Invalid state to consume buffer"),c="")}function h(){c+=s}for(;r<e.length;){if(s=e[r++],s==="\\"&&n!==2){o=n,n=4;continue}switch(n){case 0:s==="/"?(c&&d(),l()):s===":"?(d(),n=1):h();break;case 4:h(),n=o;break;case 1:s==="("?n=2:Zg.test(s)?h():(d(),n=0,s!=="*"&&s!=="?"&&s!=="+"&&r--);break;case 2:s===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:d(),n=0,s!=="*"&&s!=="?"&&s!=="+"&&r--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),d(),l(),a}function Qg(e,t,n){const o=qg(Jg(e.path),n),a=Ie(o,{record:e,parent:t,children:[],alias:[]});return t&&!a.record.aliasOf==!t.record.aliasOf&&t.children.push(a),a}function eb(e,t){const n=[],o=new Map;t=cc({strict:!1,end:!0,sensitive:!1},t);function a(d){return o.get(d)}function i(d,h,m){const v=!m,p=rc(d);p.aliasOf=m&&m.record;const b=cc(t,d),w=[p];if("alias"in d){const S=typeof d.alias=="string"?[d.alias]:d.alias;for(const y of S)w.push(rc(Ie({},p,{components:m?m.record.components:p.components,path:y,aliasOf:m?m.record:p})))}let g,x;for(const S of w){const{path:y}=S;if(h&&y[0]!=="/"){const T=h.record.path,O=T[T.length-1]==="/"?"":"/";S.path=h.record.path+(y&&O+y)}if(g=Qg(S,h,b),m?m.alias.push(g):(x=x||g,x!==g&&x.alias.push(g),v&&d.name&&!sc(g)&&l(d.name)),Jd(g)&&s(g),p.children){const T=p.children;for(let O=0;O<T.length;O++)i(T[O],g,m&&m.children[O])}m=m||g}return x?()=>{l(x)}:ba}function l(d){if(Gd(d)){const h=o.get(d);h&&(o.delete(d),n.splice(n.indexOf(h),1),h.children.forEach(l),h.alias.forEach(l))}else{const h=n.indexOf(d);h>-1&&(n.splice(h,1),d.record.name&&o.delete(d.record.name),d.children.forEach(l),d.alias.forEach(l))}}function r(){return n}function s(d){const h=ob(d,n);n.splice(h,0,d),d.record.name&&!sc(d)&&o.set(d.record.name,d)}function c(d,h){let m,v={},p,b;if("name"in d&&d.name){if(m=o.get(d.name),!m)throw zo(1,{location:d});b=m.record.name,v=Ie(lc(h.params,m.keys.filter(x=>!x.optional).concat(m.parent?m.parent.keys.filter(x=>x.optional):[]).map(x=>x.name)),d.params&&lc(d.params,m.keys.map(x=>x.name))),p=m.stringify(v)}else if(d.path!=null)p=d.path,m=n.find(x=>x.re.test(p)),m&&(v=m.parse(p),b=m.record.name);else{if(m=h.name?o.get(h.name):n.find(x=>x.re.test(h.path)),!m)throw zo(1,{location:d,currentLocation:h});b=m.record.name,v=Ie({},h.params,d.params),p=m.stringify(v)}const w=[];let g=m;for(;g;)w.unshift(g.record),g=g.parent;return{name:b,path:p,params:v,matched:w,meta:nb(w)}}e.forEach(d=>i(d));function u(){n.length=0,o.clear()}return{addRoute:i,resolve:c,removeRoute:l,clearRoutes:u,getRoutes:r,getRecordMatcher:a}}function lc(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function rc(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:tb(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function tb(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]=typeof n=="object"?n[o]:n;return t}function sc(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function nb(e){return e.reduce((t,n)=>Ie(t,n.meta),{})}function cc(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function ob(e,t){let n=0,o=t.length;for(;n!==o;){const i=n+o>>1;Zd(e,t[i])<0?o=i:n=i+1}const a=ab(e);return a&&(o=t.lastIndexOf(a,o-1)),o}function ab(e){let t=e;for(;t=t.parent;)if(Jd(t)&&Zd(e,t)===0)return t}function Jd({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ib(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let a=0;a<o.length;++a){const i=o[a].replace(jd," "),l=i.indexOf("="),r=Aa(l<0?i:i.slice(0,l)),s=l<0?null:Aa(i.slice(l+1));if(r in t){let c=t[r];zt(c)||(c=t[r]=[c]),c.push(s)}else t[r]=s}return t}function uc(e){let t="";for(let n in e){const o=e[n];if(n=Tg(n),o==null){o!==void 0&&(t+=(t.length?"&":"")+n);continue}(zt(o)?o.map(i=>i&&or(i)):[o&&or(o)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function lb(e){const t={};for(const n in e){const o=e[n];o!==void 0&&(t[n]=zt(o)?o.map(a=>a==null?null:""+a):o==null?o:""+o)}return t}const rb=Symbol(""),dc=Symbol(""),Ki=Symbol(""),zr=Symbol(""),ir=Symbol("");function ta(){let e=[];function t(o){return e.push(o),()=>{const a=e.indexOf(o);a>-1&&e.splice(a,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function jn(e,t,n,o,a,i=l=>l()){const l=o&&(o.enterCallbacks[a]=o.enterCallbacks[a]||[]);return()=>new Promise((r,s)=>{const c=h=>{h===!1?s(zo(4,{from:n,to:t})):h instanceof Error?s(h):Ug(h)?s(zo(2,{from:t,to:h})):(l&&o.enterCallbacks[a]===l&&typeof h=="function"&&l.push(h),r())},u=i(()=>e.call(o&&o.instances[a],t,n,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(h=>s(h))})}function Cl(e,t,n,o,a=i=>i()){const i=[];for(const l of e)for(const r in l.components){let s=l.components[r];if(!(t!=="beforeRouteEnter"&&!l.instances[r]))if(zd(s)){const u=(s.__vccOpts||s)[t];u&&i.push(jn(u,n,o,l,r,a))}else{let c=s();i.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${r}" at "${l.path}"`);const d=hg(u)?u.default:u;l.mods[r]=u,l.components[r]=d;const m=(d.__vccOpts||d)[t];return m&&jn(m,n,o,l,r,a)()}))}}return i}function fc(e){const t=rt(Ki),n=rt(zr),o=B(()=>{const s=Lt(e.to);return t.resolve(s)}),a=B(()=>{const{matched:s}=o.value,{length:c}=s,u=s[c-1],d=n.matched;if(!u||!d.length)return-1;const h=d.findIndex(No.bind(null,u));if(h>-1)return h;const m=hc(s[c-2]);return c>1&&hc(u)===m&&d[d.length-1].path!==m?d.findIndex(No.bind(null,s[c-2])):h}),i=B(()=>a.value>-1&&fb(n.params,o.value.params)),l=B(()=>a.value>-1&&a.value===n.matched.length-1&&Kd(n.params,o.value.params));function r(s={}){if(db(s)){const c=t[Lt(e.replace)?"replace":"push"](Lt(e.to)).catch(ba);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:o,href:B(()=>o.value.href),isActive:i,isExactActive:l,navigate:r}}function sb(e){return e.length===1?e[0]:e}const cb=W({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:fc,setup(e,{slots:t}){const n=je(fc(e)),{options:o}=rt(Ki),a=B(()=>({[mc(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[mc(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&sb(t.default(n));return e.custom?i:Fr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:a.value},i)}}}),ub=cb;function db(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function fb(e,t){for(const n in t){const o=t[n],a=e[n];if(typeof o=="string"){if(o!==a)return!1}else if(!zt(a)||a.length!==o.length||o.some((i,l)=>i!==a[l]))return!1}return!0}function hc(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const mc=(e,t,n)=>e??t??n,hb=W({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=rt(ir),a=B(()=>e.route||o.value),i=rt(dc,0),l=B(()=>{let c=Lt(i);const{matched:u}=a.value;let d;for(;(d=u[c])&&!d.components;)c++;return c}),r=B(()=>a.value.matched[l.value]);pn(dc,B(()=>l.value+1)),pn(rb,r),pn(ir,a);const s=M();return te(()=>[s.value,r.value,e.name],([c,u,d],[h,m,v])=>{u&&(u.instances[d]=c,m&&m!==u&&c&&c===h&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),c&&u&&(!m||!No(u,m)||!h)&&(u.enterCallbacks[d]||[]).forEach(p=>p(c))},{flush:"post"}),()=>{const c=a.value,u=e.name,d=r.value,h=d&&d.components[u];if(!h)return vc(n.default,{Component:h,route:c});const m=d.props[u],v=m?m===!0?c.params:typeof m=="function"?m(c):m:null,b=Fr(h,Ie({},v,t,{onVnodeUnmounted:w=>{w.component.isUnmounted&&(d.instances[u]=null)},ref:s}));return vc(n.default,{Component:b,route:c})||b}}});function vc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const mb=hb;function vb(e){const t=eb(e.routes,e),n=e.parseQuery||ib,o=e.stringifyQuery||uc,a=e.history,i=ta(),l=ta(),r=ta(),s=xm(Dn);let c=Dn;Ao&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=xl.bind(null,_=>""+_),d=xl.bind(null,kg),h=xl.bind(null,Aa);function m(_,V){let L,X;return Gd(_)?(L=t.getRecordMatcher(_),X=V):X=_,t.addRoute(X,L)}function v(_){const V=t.getRecordMatcher(_);V&&t.removeRoute(V)}function p(){return t.getRoutes().map(_=>_.record)}function b(_){return!!t.getRecordMatcher(_)}function w(_,V){if(V=Ie({},V||s.value),typeof _=="string"){const R=Sl(n,_,V.path),z=t.resolve({path:R.path},V),K=a.createHref(R.fullPath);return Ie(R,z,{params:h(z.params),hash:Aa(R.hash),redirectedFrom:void 0,href:K})}let L;if(_.path!=null)L=Ie({},_,{path:Sl(n,_.path,V.path).path});else{const R=Ie({},_.params);for(const z in R)R[z]==null&&delete R[z];L=Ie({},_,{params:d(R)}),V.params=d(V.params)}const X=t.resolve(L,V),me=_.hash||"";X.params=u(h(X.params));const E=$g(o,Ie({},_,{hash:Cg(me),path:X.path})),$=a.createHref(E);return Ie({fullPath:E,hash:me,query:o===uc?lb(_.query):_.query||{}},X,{redirectedFrom:void 0,href:$})}function g(_){return typeof _=="string"?Sl(n,_,s.value.path):Ie({},_)}function x(_,V){if(c!==_)return zo(8,{from:V,to:_})}function S(_){return O(_)}function y(_){return S(Ie(g(_),{replace:!0}))}function T(_){const V=_.matched[_.matched.length-1];if(V&&V.redirect){const{redirect:L}=V;let X=typeof L=="function"?L(_):L;return typeof X=="string"&&(X=X.includes("?")||X.includes("#")?X=g(X):{path:X},X.params={}),Ie({query:_.query,hash:_.hash,params:X.path!=null?{}:_.params},X)}}function O(_,V){const L=c=w(_),X=s.value,me=_.state,E=_.force,$=_.replace===!0,R=T(L);if(R)return O(Ie(g(R),{state:typeof R=="object"?Ie({},me,R.state):me,force:E,replace:$}),V||L);const z=L;z.redirectedFrom=V;let K;return!E&&Ag(o,X,L)&&(K=zo(16,{to:z,from:X}),re(X,X,!0,!1)),(K?Promise.resolve(K):I(z,X)).catch(U=>un(U)?un(U,2)?U:Ee(U):oe(U,z,X)).then(U=>{if(U){if(un(U,2))return O(Ie({replace:$},g(U.to),{state:typeof U.to=="object"?Ie({},me,U.to.state):me,force:E}),V||z)}else U=k(z,X,!0,$,me);return P(z,X,U),U})}function C(_,V){const L=x(_,V);return L?Promise.reject(L):Promise.resolve()}function A(_){const V=fe.values().next().value;return V&&typeof V.runWithContext=="function"?V.runWithContext(_):_()}function I(_,V){let L;const[X,me,E]=gb(_,V);L=Cl(X.reverse(),"beforeRouteLeave",_,V);for(const R of X)R.leaveGuards.forEach(z=>{L.push(jn(z,_,V))});const $=C.bind(null,_,V);return L.push($),ce(L).then(()=>{L=[];for(const R of i.list())L.push(jn(R,_,V));return L.push($),ce(L)}).then(()=>{L=Cl(me,"beforeRouteUpdate",_,V);for(const R of me)R.updateGuards.forEach(z=>{L.push(jn(z,_,V))});return L.push($),ce(L)}).then(()=>{L=[];for(const R of E)if(R.beforeEnter)if(zt(R.beforeEnter))for(const z of R.beforeEnter)L.push(jn(z,_,V));else L.push(jn(R.beforeEnter,_,V));return L.push($),ce(L)}).then(()=>(_.matched.forEach(R=>R.enterCallbacks={}),L=Cl(E,"beforeRouteEnter",_,V,A),L.push($),ce(L))).then(()=>{L=[];for(const R of l.list())L.push(jn(R,_,V));return L.push($),ce(L)}).catch(R=>un(R,8)?R:Promise.reject(R))}function P(_,V,L){r.list().forEach(X=>A(()=>X(_,V,L)))}function k(_,V,L,X,me){const E=x(_,V);if(E)return E;const $=V===Dn,R=Ao?history.state:{};L&&(X||$?a.replace(_.fullPath,Ie({scroll:$&&R&&R.scroll},me)):a.push(_.fullPath,me)),s.value=_,re(_,V,L,$),Ee()}let D;function j(){D||(D=a.listen((_,V,L)=>{if(!q.listening)return;const X=w(_),me=T(X);if(me){O(Ie(me,{replace:!0,force:!0}),X).catch(ba);return}c=X;const E=s.value;Ao&&Vg(tc(E.fullPath,L.delta),Yi()),I(X,E).catch($=>un($,12)?$:un($,2)?(O(Ie(g($.to),{force:!0}),X).then(R=>{un(R,20)&&!L.delta&&L.type===Oa.pop&&a.go(-1,!1)}).catch(ba),Promise.reject()):(L.delta&&a.go(-L.delta,!1),oe($,X,E))).then($=>{$=$||k(X,E,!1),$&&(L.delta&&!un($,8)?a.go(-L.delta,!1):L.type===Oa.pop&&un($,20)&&a.go(-1,!1)),P(X,E,$)}).catch(ba)}))}let ae=ta(),F=ta(),ne;function oe(_,V,L){Ee(_);const X=F.list();return X.length?X.forEach(me=>me(_,V,L)):console.error(_),Promise.reject(_)}function Te(){return ne&&s.value!==Dn?Promise.resolve():new Promise((_,V)=>{ae.add([_,V])})}function Ee(_){return ne||(ne=!_,j(),ae.list().forEach(([V,L])=>_?L(_):V()),ae.reset()),_}function re(_,V,L,X){const{scrollBehavior:me}=e;if(!Ao||!me)return Promise.resolve();const E=!L&&Fg(tc(_.fullPath,0))||(X||!L)&&history.state&&history.state.scroll||null;return Se().then(()=>me(_,V,E)).then($=>$&&Lg($)).catch($=>oe($,_,V))}const N=_=>a.go(_);let ee;const fe=new Set,q={currentRoute:s,listening:!0,addRoute:m,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:b,getRoutes:p,resolve:w,options:e,push:S,replace:y,go:N,back:()=>N(-1),forward:()=>N(1),beforeEach:i.add,beforeResolve:l.add,afterEach:r.add,onError:F.add,isReady:Te,install(_){const V=this;_.component("RouterLink",ub),_.component("RouterView",mb),_.config.globalProperties.$router=V,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>Lt(s)}),Ao&&!ee&&s.value===Dn&&(ee=!0,S(a.location).catch(me=>{}));const L={};for(const me in Dn)Object.defineProperty(L,me,{get:()=>s.value[me],enumerable:!0});_.provide(Ki,V),_.provide(zr,Yu(L)),_.provide(ir,s);const X=_.unmount;fe.add(_),_.unmount=function(){fe.delete(_),fe.size<1&&(c=Dn,D&&D(),D=null,s.value=Dn,ee=!1,ne=!1),X()}}};function ce(_){return _.reduce((V,L)=>V.then(()=>A(L)),Promise.resolve())}return q}function gb(e,t){const n=[],o=[],a=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const r=t.matched[l];r&&(e.matched.find(c=>No(c,r))?o.push(r):n.push(r));const s=e.matched[l];s&&(t.matched.find(c=>No(c,s))||a.push(s))}return[n,o,a]}function ak(){return rt(Ki)}function ik(e){return rt(zr)}const bb={id:"app"},yb={__name:"App",setup(e){return(t,n)=>{const o=jm("router-view");return Od(),xv("div",bb,[f(o)])}}};function lr(){}const he=Object.assign,At=typeof window<"u",Ht=e=>e!==null&&typeof e=="object",ke=e=>e!=null,Ho=e=>typeof e=="function",Hr=e=>Ht(e)&&Ho(e.then)&&Ho(e.catch),Ia=e=>Object.prototype.toString.call(e)==="[object Date]"&&!Number.isNaN(e.getTime());function Qd(e){return e=e.replace(/[^-|\d]/g,""),/^((\+86)|(86))?(1)\d{10}$/.test(e)||/^0[0-9-]{10,13}$/.test(e)}const ef=e=>typeof e=="number"||/^\d+(\.\d+)?$/.test(e),pb=()=>At?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function gc(e,t){const n=t.split(".");let o=e;return n.forEach(a=>{var i;o=Ht(o)&&(i=o[a])!=null?i:""}),o}function $e(e,t,n){return t.reduce((o,a)=>((!n||e[a]!==void 0)&&(o[a]=e[a]),o),{})}const on=(e,t)=>JSON.stringify(e)===JSON.stringify(t),Ti=e=>Array.isArray(e)?e:[e],wb=e=>e.reduce((t,n)=>t.concat(n),[]),ze=null,G=[Number,String],H={type:Boolean,default:!0},Ze=e=>({type:e,required:!0}),Ne=()=>({type:Array,default:()=>[]}),Ge=e=>({type:Number,default:e}),se=e=>({type:G,default:e}),J=e=>({type:String,default:e});var Gn=typeof window<"u";function st(e){return Gn?requestAnimationFrame(e):-1}function qi(e){Gn&&cancelAnimationFrame(e)}function Wn(e){st(()=>st(e))}var xb=e=>e===window,bc=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),Oe=e=>{const t=Lt(e);if(xb(t)){const n=t.innerWidth,o=t.innerHeight;return bc(n,o)}return t!=null&&t.getBoundingClientRect?t.getBoundingClientRect():bc(0,0)};function Sb(e=!1){const t=M(e);return[t,(o=!t.value)=>{t.value=o}]}function it(e){const t=rt(e,null);if(t){const n=jt(),{link:o,unlink:a,internalChildren:i}=t;o(n),Uo(()=>a(n));const l=B(()=>i.indexOf(n));return{parent:t,index:l}}return{parent:null,index:M(-1)}}function Cb(e){const t=[],n=o=>{Array.isArray(o)&&o.forEach(a=>{var i;Ea(a)&&(t.push(a),(i=a.component)!=null&&i.subTree&&(t.push(a.component.subTree),n(a.component.subTree.children)),a.children&&n(a.children))})};return n(e),t}var yc=(e,t)=>{const n=e.indexOf(t);return n===-1?e.findIndex(o=>t.key!==void 0&&t.key!==null&&o.type===t.type&&o.key===t.key):n};function Tb(e,t,n){const o=Cb(e.subTree.children);n.sort((i,l)=>yc(o,i.vnode)-yc(o,l.vnode));const a=n.map(i=>i.proxy);t.sort((i,l)=>{const r=a.indexOf(i),s=a.indexOf(l);return r-s})}function dt(e){const t=je([]),n=je([]),o=jt();return{children:t,linkChildren:i=>{pn(e,Object.assign({link:s=>{s.proxy&&(n.push(s),t.push(s.proxy),Tb(o,t,n))},unlink:s=>{const c=n.indexOf(s);t.splice(c,1),n.splice(c,1)},children:t,internalChildren:n},i))}}}var rr=1e3,sr=60*rr,cr=60*sr,pc=24*cr;function _b(e){const t=Math.floor(e/pc),n=Math.floor(e%pc/cr),o=Math.floor(e%cr/sr),a=Math.floor(e%sr/rr),i=Math.floor(e%rr);return{total:e,days:t,hours:n,minutes:o,seconds:a,milliseconds:i}}function kb(e,t){return Math.floor(e/1e3)===Math.floor(t/1e3)}function Eb(e){let t,n,o,a;const i=M(e.time),l=B(()=>_b(i.value)),r=()=>{o=!1,qi(t)},s=()=>Math.max(n-Date.now(),0),c=p=>{var b,w;i.value=p,(b=e.onChange)==null||b.call(e,l.value),p===0&&(r(),(w=e.onFinish)==null||w.call(e))},u=()=>{t=st(()=>{o&&(c(s()),i.value>0&&u())})},d=()=>{t=st(()=>{if(o){const p=s();(!kb(p,i.value)||p===0)&&c(p),i.value>0&&d()}})},h=()=>{Gn&&(e.millisecond?u():d())},m=()=>{o||(n=Date.now()+i.value,o=!0,h())},v=(p=e.time)=>{r(),i.value=p};return rn(r),an(()=>{a&&(o=!0,a=!1,h())}),ln(()=>{o&&(r(),a=!0)}),{start:m,pause:r,reset:v,current:l}}function Ko(e){let t;We(()=>{e(),Se(()=>{t=!0})}),an(()=>{t&&e()})}function Ye(e,t,n={}){if(!Gn)return;const{target:o=window,passive:a=!1,capture:i=!1}=n;let l=!1,r;const s=d=>{if(l)return;const h=Lt(d);h&&!r&&(h.addEventListener(e,t,{capture:i,passive:a}),r=!0)},c=d=>{if(l)return;const h=Lt(d);h&&r&&(h.removeEventListener(e,t,i),r=!1)};Uo(()=>c(o)),ln(()=>c(o)),Ko(()=>s(o));let u;return ot(o)&&(u=te(o,(d,h)=>{c(h),s(d)})),()=>{u==null||u(),c(o),l=!0}}function Gi(e,t,n={}){if(!Gn)return;const{eventName:o="click"}=n;Ye(o,i=>{(Array.isArray(e)?e:[e]).every(s=>{const c=Lt(s);return c&&!c.contains(i.target)})&&t(i)},{target:document})}var Ua,Tl;function Pb(){if(!Ua&&(Ua=M(0),Tl=M(0),Gn)){const e=()=>{Ua.value=window.innerWidth,Tl.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:Ua,height:Tl}}var $b=/scroll|auto|overlay/i,tf=Gn?window:void 0;function Ab(e){return e.tagName!=="HTML"&&e.tagName!=="BODY"&&e.nodeType===1}function jr(e,t=tf){let n=e;for(;n&&n!==t&&Ab(n);){const{overflowY:o}=window.getComputedStyle(n);if($b.test(o))return n;n=n.parentNode}return t}function qo(e,t=tf){const n=M();return We(()=>{e.value&&(n.value=jr(e.value,t))}),n}var Ya;function Ob(){if(!Ya&&(Ya=M("visible"),Gn)){const e=()=>{Ya.value=document.hidden?"hidden":"visible"};e(),window.addEventListener("visibilitychange",e)}return Ya}var nf=Symbol("van-field");function Xn(e){const t=rt(nf,null);t&&!t.customValue.value&&(t.customValue.value=e,te(e,()=>{t.resetValidation(),t.validateWithTrigger("onChange")}))}function Tn(e){const t="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(t,0)}function _i(e,t){"scrollTop"in e?e.scrollTop=t:e.scrollTo(e.scrollX,t)}function co(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}function Ra(e){_i(window,e),_i(document.body,e)}function wc(e,t){if(e===window)return 0;const n=t?Tn(t):co();return Oe(e).top+n}const Ib=pb();function of(){Ib&&Ra(co())}const Wr=e=>e.stopPropagation();function Ve(e,t){(typeof e.cancelable!="boolean"||e.cancelable)&&e.preventDefault(),t&&Wr(e)}function fo(e){const t=Lt(e);if(!t)return!1;const n=window.getComputedStyle(t),o=n.display==="none",a=t.offsetParent===null&&n.position!=="fixed";return o||a}const{width:Vt,height:Pt}=Pb();function Rb(e){const t=window.getComputedStyle(e);return t.transform!=="none"||t.perspective!=="none"||["transform","perspective","filter"].some(n=>(t.willChange||"").includes(n))}function Db(e){let t=e.parentElement;for(;t;){if(t&&t.tagName!=="HTML"&&t.tagName!=="BODY"&&Rb(t))return t;t=t.parentElement}return null}function we(e){if(ke(e))return ef(e)?`${e}px`:String(e)}function An(e){if(ke(e)){if(Array.isArray(e))return{width:we(e[0]),height:we(e[1])};const t=we(e);return{width:t,height:t}}}function On(e){const t={};return e!==void 0&&(t.zIndex=+e),t}let _l;function Bb(){if(!_l){const e=document.documentElement,t=e.style.fontSize||window.getComputedStyle(e).fontSize;_l=parseFloat(t)}return _l}function Mb(e){return e=e.replace(/rem/g,""),+e*Bb()}function Lb(e){return e=e.replace(/vw/g,""),+e*Vt.value/100}function Vb(e){return e=e.replace(/vh/g,""),+e*Pt.value/100}function Ur(e){if(typeof e=="number")return e;if(At){if(e.includes("rem"))return Mb(e);if(e.includes("vw"))return Lb(e);if(e.includes("vh"))return Vb(e)}return parseFloat(e)}const Fb=/-(\w)/g,af=e=>e.replace(Fb,(t,n)=>n.toUpperCase()),Nb=e=>e.replace(/([A-Z])/g,"-$1").toLowerCase().replace(/^-/,"");function Bt(e,t=2){let n=e+"";for(;n.length<t;)n="0"+n;return n}const Xe=(e,t,n)=>Math.min(Math.max(e,t),n);function xc(e,t,n){const o=e.indexOf(t);return o===-1?e:t==="-"&&o!==0?e.slice(0,o):e.slice(0,o+1)+e.slice(o).replace(n,"")}function ur(e,t=!0,n=!0){t?e=xc(e,".",/\./g):e=e.split(".")[0],n?e=xc(e,"-",/-/g):e=e.replace(/-/,"");const o=t?/[^-0-9.]/g:/[^-0-9]/g;return e.replace(o,"")}function lf(e,t){return Math.round((e+t)*1e10)/1e10}const{hasOwnProperty:zb}=Object.prototype;function Hb(e,t,n){const o=t[n];ke(o)&&(!zb.call(e,n)||!Ht(o)?e[n]=o:e[n]=rf(Object(e[n]),o))}function rf(e,t){return Object.keys(t).forEach(n=>{Hb(e,t,n)}),e}var jb={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>`${e}年${t}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const Sc=M("zh-CN"),Cc=je({"zh-CN":jb}),sf={messages(){return Cc[Sc.value]},use(e,t){Sc.value=e,this.add({[e]:t})},add(e={}){rf(Cc,e)}};var Wb=sf;function Ub(e){const t=af(e)+".";return(n,...o)=>{const a=Wb.messages(),i=gc(a,t+n)||gc(a,n);return Ho(i)?i(...o):i}}function dr(e,t){return t?typeof t=="string"?` ${e}--${t}`:Array.isArray(t)?t.reduce((n,o)=>n+dr(e,o),""):Object.keys(t).reduce((n,o)=>n+(t[o]?dr(e,o):""),""):""}function Yb(e){return(t,n)=>(t&&typeof t!="string"&&(n=t,t=""),t=t?`${e}__${t}`:e,`${t}${dr(t,n)}`)}function Y(e){const t=`van-${e}`;return[t,Yb(t),Ub(t)]}const In="van-hairline",cf=`${In}--top`,uf=`${In}--left`,Kb=`${In}--right`,Yr=`${In}--bottom`,pa=`${In}--surround`,Xi=`${In}--top-bottom`,qb=`${In}-unset--top-bottom`,ct="van-haptics-feedback",df=Symbol("van-form"),ff=500,Tc=5;function Zn(e,{args:t=[],done:n,canceled:o,error:a}){if(e){const i=e.apply(null,t);Hr(i)?i.then(l=>{l?n():o&&o()}).catch(a||lr):i?n():o&&o()}else n()}function Z(e){return e.install=t=>{const{name:n}=e;n&&(t.component(n,e),t.component(af(`-${n}`),e))},e}function ki(e,t){return e.reduce((n,o)=>Math.abs(n-t)<Math.abs(o-t)?n:o)}const hf=Symbol();function Zi(e){const t=rt(hf,null);t&&te(t,n=>{n&&e()})}const mf=(e,t)=>{const n=M(),o=()=>{n.value=Oe(e).height};return We(()=>{if(Se(o),t)for(let a=1;a<=3;a++)setTimeout(o,100*a)}),Zi(()=>Se(o)),te([Vt,Pt],o),n};function Ji(e,t){const n=mf(e,!0);return o=>f("div",{class:t("placeholder"),style:{height:n.value?`${n.value}px`:void 0}},[o()])}const[vf,_c]=Y("action-bar"),Kr=Symbol(vf),Gb={placeholder:Boolean,safeAreaInsetBottom:H};var Xb=W({name:vf,props:Gb,setup(e,{slots:t}){const n=M(),o=Ji(n,_c),{linkChildren:a}=dt(Kr);a();const i=()=>{var l;return f("div",{ref:n,class:[_c(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(l=t.default)==null?void 0:l.call(t)])};return()=>e.placeholder?o(i):i()}});const gf=Z(Xb);function _e(e){const t=jt();t&&he(t.proxy,e)}const Jn={to:[String,Object],url:String,replace:Boolean};function bf({to:e,url:t,replace:n,$router:o}){e&&o?o[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function mo(){const e=jt().proxy;return()=>bf(e)}const[Zb,kc]=Y("badge"),Jb={dot:Boolean,max:G,tag:J("div"),color:String,offset:Array,content:G,showZero:H,position:J("top-right")};var Qb=W({name:Zb,props:Jb,setup(e,{slots:t}){const n=()=>{if(t.content)return!0;const{content:r,showZero:s}=e;return ke(r)&&r!==""&&(s||r!==0&&r!=="0")},o=()=>{const{dot:r,max:s,content:c}=e;if(!r&&n())return t.content?t.content():ke(s)&&ef(c)&&+c>+s?`${s}+`:c},a=r=>r.startsWith("-")?r.replace("-",""):`-${r}`,i=B(()=>{const r={background:e.color};if(e.offset){const[s,c]=e.offset,{position:u}=e,[d,h]=u.split("-");t.default?(typeof c=="number"?r[d]=we(d==="top"?c:-c):r[d]=d==="top"?we(c):a(c),typeof s=="number"?r[h]=we(h==="left"?s:-s):r[h]=h==="left"?we(s):a(s)):(r.marginTop=we(c),r.marginLeft=we(s))}return r}),l=()=>{if(n()||e.dot)return f("div",{class:kc([e.position,{dot:e.dot,fixed:!!t.default}]),style:i.value},[o()])};return()=>{if(t.default){const{tag:r}=e;return f(r,{class:kc("wrapper")},{default:()=>[t.default(),l()]})}return l()}}});const vo=Z(Qb);let yf=2e3;const ey=()=>++yf,ty=e=>{yf=e},[pf,ny]=Y("config-provider"),wf=Symbol(pf),oy={tag:J("div"),theme:J("light"),zIndex:Number,themeVars:Object,themeVarsDark:Object,themeVarsLight:Object,themeVarsScope:J("local"),iconPrefix:String};function ay(e){return e.replace(/([a-zA-Z])(\d)/g,"$1-$2")}function iy(e){const t={};return Object.keys(e).forEach(n=>{const o=ay(Nb(n));t[`--van-${o}`]=e[n]}),t}function Ka(e={},t={}){Object.keys(e).forEach(n=>{e[n]!==t[n]&&document.documentElement.style.setProperty(n,e[n])}),Object.keys(t).forEach(n=>{e[n]||document.documentElement.style.removeProperty(n)})}var ly=W({name:pf,props:oy,setup(e,{slots:t}){const n=B(()=>iy(he({},e.themeVars,e.theme==="dark"?e.themeVarsDark:e.themeVarsLight)));if(At){const o=()=>{document.documentElement.classList.add(`van-theme-${e.theme}`)},a=(i=e.theme)=>{document.documentElement.classList.remove(`van-theme-${i}`)};te(()=>e.theme,(i,l)=>{l&&a(l),o()},{immediate:!0}),an(o),ln(a),rn(a),te(n,(i,l)=>{e.themeVarsScope==="global"&&Ka(i,l)}),te(()=>e.themeVarsScope,(i,l)=>{l==="global"&&Ka({},n.value),i==="global"&&Ka(n.value,{})}),e.themeVarsScope==="global"&&Ka(n.value,{})}return pn(wf,e),Yo(()=>{e.zIndex!==void 0&&ty(e.zIndex)}),()=>f(e.tag,{class:ny(),style:e.themeVarsScope==="local"?n.value:void 0},{default:()=>{var o;return[(o=t.default)==null?void 0:o.call(t)]}})}});const[ry,Ec]=Y("icon"),sy=e=>e==null?void 0:e.includes("/"),cy={dot:Boolean,tag:J("i"),name:String,size:G,badge:G,color:String,badgeProps:Object,classPrefix:String};var uy=W({name:ry,props:cy,setup(e,{slots:t}){const n=rt(wf,null),o=B(()=>e.classPrefix||(n==null?void 0:n.iconPrefix)||Ec());return()=>{const{tag:a,dot:i,name:l,size:r,badge:s,color:c}=e,u=sy(l);return f(vo,Ce({dot:i,tag:a,class:[o.value,u?"":`${o.value}-${l}`],style:{color:c,fontSize:we(r)},content:s},e.badgeProps),{default:()=>{var d;return[(d=t.default)==null?void 0:d.call(t),u&&f("img",{class:Ec("image"),src:l},null)]}})}}});const xe=Z(uy);var dy=xe;const[fy,wa]=Y("loading"),hy=Array(12).fill(null).map((e,t)=>f("i",{class:wa("line",String(t+1))},null)),my=f("svg",{class:wa("circular"),viewBox:"25 25 50 50"},[f("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),vy={size:G,type:J("circular"),color:String,vertical:Boolean,textSize:G,textColor:String};var gy=W({name:fy,props:vy,setup(e,{slots:t}){const n=B(()=>he({color:e.color},An(e.size))),o=()=>{const i=e.type==="spinner"?hy:my;return f("span",{class:wa("spinner",e.type),style:n.value},[t.icon?t.icon():i])},a=()=>{var i;if(t.default)return f("span",{class:wa("text"),style:{fontSize:we(e.textSize),color:(i=e.textColor)!=null?i:e.color}},[t.default()])};return()=>{const{type:i,vertical:l}=e;return f("div",{class:wa([i,{vertical:l}]),"aria-live":"polite","aria-busy":!0},[o(),a()])}}});const Wt=Z(gy),[by,bo]=Y("button"),yy=he({},Jn,{tag:J("button"),text:String,icon:String,type:J("default"),size:J("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:J("button"),loadingSize:G,loadingText:String,loadingType:String,iconPosition:J("left")});var py=W({name:by,props:yy,emits:["click"],setup(e,{emit:t,slots:n}){const o=mo(),a=()=>n.loading?n.loading():f(Wt,{size:e.loadingSize,type:e.loadingType,class:bo("loading")},null),i=()=>{if(e.loading)return a();if(n.icon)return f("div",{class:bo("icon")},[n.icon()]);if(e.icon)return f(xe,{name:e.icon,class:bo("icon"),classPrefix:e.iconPrefix},null)},l=()=>{let c;if(e.loading?c=e.loadingText:c=n.default?n.default():e.text,c)return f("span",{class:bo("text")},[c])},r=()=>{const{color:c,plain:u}=e;if(c){const d={color:u?c:"white"};return u||(d.background=c),c.includes("gradient")?d.border=0:d.borderColor=c,d}},s=c=>{e.loading?Ve(c):e.disabled||(t("click",c),o())};return()=>{const{tag:c,type:u,size:d,block:h,round:m,plain:v,square:p,loading:b,disabled:w,hairline:g,nativeType:x,iconPosition:S}=e,y=[bo([u,d,{plain:v,block:h,round:m,square:p,loading:b,disabled:w,hairline:g}]),{[pa]:g}];return f(c,{type:x,class:y,style:r(),disabled:w,onClick:s},{default:()=>[f("div",{class:bo("content")},[S==="left"&&i(),l(),S==="right"&&i()])]})}}});const ut=Z(py),[wy,xy]=Y("action-bar-button"),Sy=he({},Jn,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var Cy=W({name:wy,props:Sy,setup(e,{slots:t}){const n=mo(),{parent:o,index:a}=it(Kr),i=B(()=>{if(o){const r=o.children[a.value-1];return!(r&&"isButton"in r)}}),l=B(()=>{if(o){const r=o.children[a.value+1];return!(r&&"isButton"in r)}});return _e({isButton:!0}),()=>{const{type:r,icon:s,text:c,color:u,loading:d,disabled:h}=e;return f(ut,{class:xy([r,{last:l.value,first:i.value}]),size:"large",type:r,icon:s,color:u,loading:d,disabled:h,onClick:n},{default:()=>[t.default?t.default():c]})}}});const fr=Z(Cy),[Ty,kl]=Y("action-bar-icon"),_y=he({},Jn,{dot:Boolean,text:String,icon:String,color:String,badge:G,iconClass:ze,badgeProps:Object,iconPrefix:String});var ky=W({name:Ty,props:_y,setup(e,{slots:t}){const n=mo();it(Kr);const o=()=>{const{dot:a,badge:i,icon:l,color:r,iconClass:s,badgeProps:c,iconPrefix:u}=e;return t.icon?f(vo,Ce({dot:a,class:kl("icon"),content:i},c),{default:t.icon}):f(xe,{tag:"div",dot:a,name:l,badge:i,color:r,class:[kl("icon"),s],badgeProps:c,classPrefix:u},null)};return()=>f("div",{role:"button",class:kl(),tabindex:0,onClick:n},[o(),t.default?t.default():e.text])}});const Ey=Z(ky),Go={show:Boolean,zIndex:G,overlay:H,duration:G,teleport:[String,Object],lockScroll:H,lazyRender:H,beforeClose:Function,overlayProps:Object,overlayStyle:Object,overlayClass:ze,transitionAppear:Boolean,closeOnClickOverlay:H},qr=Object.keys(Go);function Py(e,t){return e>t?"horizontal":t>e?"vertical":""}function Ot(){const e=M(0),t=M(0),n=M(0),o=M(0),a=M(0),i=M(0),l=M(""),r=M(!0),s=()=>l.value==="vertical",c=()=>l.value==="horizontal",u=()=>{n.value=0,o.value=0,a.value=0,i.value=0,l.value="",r.value=!0};return{move:m=>{const v=m.touches[0];n.value=(v.clientX<0?0:v.clientX)-e.value,o.value=v.clientY-t.value,a.value=Math.abs(n.value),i.value=Math.abs(o.value);const p=10;(!l.value||a.value<p&&i.value<p)&&(l.value=Py(a.value,i.value)),r.value&&(a.value>Tc||i.value>Tc)&&(r.value=!1)},start:m=>{u(),e.value=m.touches[0].clientX,t.value=m.touches[0].clientY},reset:u,startX:e,startY:t,deltaX:n,deltaY:o,offsetX:a,offsetY:i,direction:l,isVertical:s,isHorizontal:c,isTap:r}}let na=0;const Pc="van-overflow-hidden";function xf(e,t){const n=Ot(),o="01",a="10",i=u=>{n.move(u);const d=n.deltaY.value>0?a:o,h=jr(u.target,e.value),{scrollHeight:m,offsetHeight:v,scrollTop:p}=h;let b="11";p===0?b=v>=m?"00":"01":p+v>=m&&(b="10"),b!=="11"&&n.isVertical()&&!(parseInt(b,2)&parseInt(d,2))&&Ve(u,!0)},l=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",i,{passive:!1}),na||document.body.classList.add(Pc),na++},r=()=>{na&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",i),na--,na||document.body.classList.remove(Pc))},s=()=>t()&&l(),c=()=>t()&&r();Ko(s),ln(c),rn(c),te(t,u=>{u?l():r()})}function Gr(e){const t=M(!1);return te(e,n=>{n&&(t.value=n)},{immediate:!0}),n=>()=>t.value?n():null}const Ei=()=>{var e;const{scopeId:t}=((e=jt())==null?void 0:e.vnode)||{};return t?{[t]:""}:null},[$y,Ay]=Y("overlay"),Oy={show:Boolean,zIndex:G,duration:G,className:ze,lockScroll:H,lazyRender:H,customStyle:Object,teleport:[String,Object]};var Iy=W({name:$y,inheritAttrs:!1,props:Oy,setup(e,{attrs:t,slots:n}){const o=M(),a=Gr(()=>e.show||!e.lazyRender),i=r=>{e.lockScroll&&Ve(r,!0)},l=a(()=>{var r;const s=he(On(e.zIndex),e.customStyle);return ke(e.duration)&&(s.animationDuration=`${e.duration}s`),Je(f("div",Ce({ref:o,style:s,class:[Ay(),e.className]},t),[(r=n.default)==null?void 0:r.call(n)]),[[at,e.show]])});return Ye("touchmove",i,{target:o}),()=>{const r=f(Ui,{name:"van-fade",appear:!0},{default:l});return e.teleport?f(ho,{to:e.teleport},{default:()=>[r]}):r}}});const Sf=Z(Iy),Ry=he({},Go,{round:Boolean,position:J("center"),closeIcon:J("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:J("top-right"),destroyOnClose:Boolean,safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[Dy,$c]=Y("popup");var By=W({name:Dy,inheritAttrs:!1,props:Ry,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:n,slots:o}){let a,i;const l=M(),r=M(),s=Gr(()=>e.show||!e.lazyRender),c=B(()=>{const T={zIndex:l.value};if(ke(e.duration)){const O=e.position==="center"?"animationDuration":"transitionDuration";T[O]=`${e.duration}s`}return T}),u=()=>{a||(a=!0,l.value=e.zIndex!==void 0?+e.zIndex:ey(),t("open"))},d=()=>{a&&Zn(e.beforeClose,{done(){a=!1,t("close"),t("update:show",!1)}})},h=T=>{t("clickOverlay",T),e.closeOnClickOverlay&&d()},m=()=>{if(e.overlay){const T=he({show:e.show,class:e.overlayClass,zIndex:l.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},e.overlayProps);return f(Sf,Ce(T,Ei(),{onClick:h}),{default:o["overlay-content"]})}},v=T=>{t("clickCloseIcon",T),d()},p=()=>{if(e.closeable)return f(xe,{role:"button",tabindex:0,name:e.closeIcon,class:[$c("close-icon",e.closeIconPosition),ct],classPrefix:e.iconPrefix,onClick:v},null)};let b;const w=()=>{b&&clearTimeout(b),b=setTimeout(()=>{t("opened")})},g=()=>t("closed"),x=T=>t("keydown",T),S=s(()=>{var T;const{destroyOnClose:O,round:C,position:A,safeAreaInsetTop:I,safeAreaInsetBottom:P,show:k}=e;if(!(!k&&O))return Je(f("div",Ce({ref:r,style:c.value,role:"dialog",tabindex:0,class:[$c({round:C,[A]:A}),{"van-safe-area-top":I,"van-safe-area-bottom":P}],onKeydown:x},n,Ei()),[(T=o.default)==null?void 0:T.call(o),p()]),[[at,k]])}),y=()=>{const{position:T,transition:O,transitionAppear:C}=e,A=T==="center"?"van-fade":`van-popup-slide-${T}`;return f(Ui,{name:O||A,appear:C,onAfterEnter:w,onAfterLeave:g},{default:S})};return te(()=>e.show,T=>{T&&!a&&(u(),n.tabindex===0&&Se(()=>{var O;(O=r.value)==null||O.focus()})),!T&&a&&(a=!1,t("close"))}),_e({popupRef:r}),xf(r,()=>e.show&&e.lockScroll),Ye("popstate",()=>{e.closeOnPopstate&&(d(),i=!1)}),We(()=>{e.show&&u()}),an(()=>{i&&(t("update:show",!0),i=!1)}),ln(()=>{e.show&&e.teleport&&(d(),i=!0)}),pn(hf,()=>e.show),()=>e.teleport?f(ho,{to:e.teleport},{default:()=>[m(),y()]}):f(qe,null,[m(),y()])}});const Ut=Z(By),[My,_t]=Y("action-sheet"),Ly=he({},Go,{title:String,round:H,actions:Ne(),closeIcon:J("cross"),closeable:H,cancelText:String,description:String,closeOnPopstate:H,closeOnClickAction:Boolean,safeAreaInsetBottom:H}),Vy=[...qr,"round","closeOnPopstate","safeAreaInsetBottom"];var Fy=W({name:My,props:Ly,emits:["select","cancel","update:show"],setup(e,{slots:t,emit:n}){const o=d=>n("update:show",d),a=()=>{o(!1),n("cancel")},i=()=>{if(e.title)return f("div",{class:_t("header")},[e.title,e.closeable&&f(xe,{name:e.closeIcon,class:[_t("close"),ct],onClick:a},null)])},l=()=>{if(t.cancel||e.cancelText)return[f("div",{class:_t("gap")},null),f("button",{type:"button",class:_t("cancel"),onClick:a},[t.cancel?t.cancel():e.cancelText])]},r=d=>{if(d.icon)return f(xe,{class:_t("item-icon"),name:d.icon},null)},s=(d,h)=>d.loading?f(Wt,{class:_t("loading-icon")},null):t.action?t.action({action:d,index:h}):[f("span",{class:_t("name")},[d.name]),d.subname&&f("div",{class:_t("subname")},[d.subname])],c=(d,h)=>{const{color:m,loading:v,callback:p,disabled:b,className:w}=d,g=()=>{b||v||(p&&p(d),e.closeOnClickAction&&o(!1),Se(()=>n("select",d,h)))};return f("button",{type:"button",style:{color:m},class:[_t("item",{loading:v,disabled:b}),w],onClick:g},[r(d),s(d,h)])},u=()=>{if(e.description||t.description){const d=t.description?t.description():e.description;return f("div",{class:_t("description")},[d])}};return()=>f(Ut,Ce({class:_t(),position:"bottom","onUpdate:show":o},$e(e,Vy)),{default:()=>{var d;return[i(),u(),f("div",{class:_t("content")},[e.actions.map(c),(d=t.default)==null?void 0:d.call(t)]),l()]}})}});const Ny=Z(Fy),[zy,yn,Ac]=Y("picker"),Cf=e=>e.find(t=>!t.disabled)||e[0];function Hy(e,t){const n=e[0];if(n){if(Array.isArray(n))return"multiple";if(t.children in n)return"cascade"}return"default"}function hi(e,t){t=Xe(t,0,e.length);for(let n=t;n<e.length;n++)if(!e[n].disabled)return n;for(let n=t-1;n>=0;n--)if(!e[n].disabled)return n;return 0}const Oc=(e,t,n)=>t!==void 0&&e.some(o=>o[n.value]===t);function hr(e,t,n){const o=e.findIndex(i=>i[n.value]===t),a=hi(e,o);return e[a]}function jy(e,t,n){const o=[];let a={[t.children]:e},i=0;for(;a&&a[t.children];){const l=a[t.children],r=n.value[i];if(a=ke(r)?hr(l,r,t):void 0,!a&&l.length){const s=Cf(l)[t.value];a=hr(l,s,t)}i++,o.push(l)}return o}function Wy(e){const{transform:t}=window.getComputedStyle(e),n=t.slice(7,t.length-1).split(", ")[5];return Number(n)}function Uy(e){return he({text:"text",value:"value",children:"children"},e)}const Ic=200,Rc=300,Yy=15,[Tf,El]=Y("picker-column"),_f=Symbol(Tf);var Ky=W({name:Tf,props:{value:G,fields:Ze(Object),options:Ne(),readonly:Boolean,allowHtml:Boolean,optionHeight:Ze(Number),swipeDuration:Ze(G),visibleOptionNum:Ze(G)},emits:["change","clickOption","scrollInto"],setup(e,{emit:t,slots:n}){let o,a,i,l,r;const s=M(),c=M(),u=M(0),d=M(0),h=Ot(),m=()=>e.options.length,v=()=>e.optionHeight*(+e.visibleOptionNum-1)/2,p=I=>{let P=hi(e.options,I);const k=-P*e.optionHeight,D=()=>{P>m()-1&&(P=hi(e.options,I));const j=e.options[P][e.fields.value];j!==e.value&&t("change",j)};o&&k!==u.value?r=D:D(),u.value=k},b=()=>e.readonly||!e.options.length,w=I=>{o||b()||(r=null,d.value=Ic,p(I),t("clickOption",e.options[I]))},g=I=>Xe(Math.round(-I/e.optionHeight),0,m()-1),x=B(()=>g(u.value)),S=(I,P)=>{const k=Math.abs(I/P);I=u.value+k/.003*(I<0?-1:1);const D=g(I);d.value=+e.swipeDuration,p(D)},y=()=>{o=!1,d.value=0,r&&(r(),r=null)},T=I=>{if(!b()){if(h.start(I),o){const P=Wy(c.value);u.value=Math.min(0,P-v())}d.value=0,a=u.value,i=Date.now(),l=a,r=null}},O=I=>{if(b())return;h.move(I),h.isVertical()&&(o=!0,Ve(I,!0));const P=Xe(a+h.deltaY.value,-(m()*e.optionHeight),e.optionHeight),k=g(P);k!==x.value&&t("scrollInto",e.options[k]),u.value=P;const D=Date.now();D-i>Rc&&(i=D,l=P)},C=()=>{if(b())return;const I=u.value-l,P=Date.now()-i;if(P<Rc&&Math.abs(I)>Yy){S(I,P);return}const D=g(u.value);d.value=Ic,p(D),setTimeout(()=>{o=!1},0)},A=()=>{const I={height:`${e.optionHeight}px`};return e.options.map((P,k)=>{const D=P[e.fields.text],{disabled:j}=P,ae=P[e.fields.value],F={role:"button",style:I,tabindex:j?-1:0,class:[El("item",{disabled:j,selected:ae===e.value}),P.className],onClick:()=>w(k)},ne={class:"van-ellipsis",[e.allowHtml?"innerHTML":"textContent"]:D};return f("li",F,[n.option?n.option(P,k):f("div",ne,null)])})};return it(_f),_e({stopMomentum:y}),Yo(()=>{const I=o?Math.floor(-u.value/e.optionHeight):e.options.findIndex(D=>D[e.fields.value]===e.value),P=hi(e.options,I),k=-P*e.optionHeight;o&&P<I&&y(),u.value=k}),Ye("touchmove",O,{target:s}),()=>f("div",{ref:s,class:El(),onTouchstartPassive:T,onTouchend:C,onTouchcancel:C},[f("ul",{ref:c,style:{transform:`translate3d(0, ${u.value+v()}px, 0)`,transitionDuration:`${d.value}ms`,transitionProperty:d.value?"all":"none"},class:El("wrapper"),onTransitionend:y},[A()])])}});const[qy]=Y("picker-toolbar"),Qi={title:String,cancelButtonText:String,confirmButtonText:String},kf=["cancel","confirm","title","toolbar"],Gy=Object.keys(Qi);var Ef=W({name:qy,props:Qi,emits:["confirm","cancel"],setup(e,{emit:t,slots:n}){const o=()=>{if(n.title)return n.title();if(e.title)return f("div",{class:[yn("title"),"van-ellipsis"]},[e.title])},a=()=>t("cancel"),i=()=>t("confirm"),l=()=>{var s;const c=(s=e.cancelButtonText)!=null?s:Ac("cancel");if(!(!n.cancel&&!c))return f("button",{type:"button",class:[yn("cancel"),ct],onClick:a},[n.cancel?n.cancel():c])},r=()=>{var s;const c=(s=e.confirmButtonText)!=null?s:Ac("confirm");if(!(!n.confirm&&!c))return f("button",{type:"button",class:[yn("confirm"),ct],onClick:i},[n.confirm?n.confirm():c])};return()=>f("div",{class:yn("toolbar")},[n.toolbar?n.toolbar():[l(),o(),r()]])}});const Xr=(e,t)=>{const n=M(e());return te(e,o=>{o!==n.value&&(n.value=o)}),te(n,o=>{o!==e()&&t(o)}),n};function Xy(e,t,n){let o,a=0;const i=e.scrollLeft,l=n===0?1:Math.round(n*1e3/16);let r=i;function s(){qi(o)}function c(){r+=(t-i)/l,e.scrollLeft=r,++a<l&&(o=st(c))}return c(),s}function Zy(e,t,n,o){let a,i=Tn(e);const l=i<t,r=n===0?1:Math.round(n*1e3/16),s=(t-i)/r;function c(){qi(a)}function u(){i+=s,(l&&i>t||!l&&i<t)&&(i=t),_i(e,i),l&&i<t||!l&&i>t?a=st(u):o&&(a=st(o))}return u(),c}let Jy=0;function Xo(){const e=jt(),{name:t="unknown"}=(e==null?void 0:e.type)||{};return`${t}-${++Jy}`}function Fa(){const e=M([]),t=[];return fd(()=>{e.value=[]}),[e,o=>(t[o]||(t[o]=a=>{e.value[o]=a}),t[o])]}function Pf(e,t){if(!At||!window.IntersectionObserver)return;const n=new IntersectionObserver(i=>{t(i[0].intersectionRatio>0)},{root:document.body}),o=()=>{e.value&&n.observe(e.value)},a=()=>{e.value&&n.unobserve(e.value)};ln(a),rn(a),Ko(o)}const[Qy,e0]=Y("sticky"),t0={zIndex:G,position:J("top"),container:Object,offsetTop:se(0),offsetBottom:se(0)};var n0=W({name:Qy,props:t0,emits:["scroll","change"],setup(e,{emit:t,slots:n}){const o=M(),a=qo(o),i=je({fixed:!1,width:0,height:0,transform:0}),l=M(!1),r=B(()=>Ur(e.position==="top"?e.offsetTop:e.offsetBottom)),s=B(()=>{if(l.value)return;const{fixed:h,height:m,width:v}=i;if(h)return{width:`${v}px`,height:`${m}px`}}),c=B(()=>{if(!i.fixed||l.value)return;const h=he(On(e.zIndex),{width:`${i.width}px`,height:`${i.height}px`,[e.position]:`${r.value}px`});return i.transform&&(h.transform=`translate3d(0, ${i.transform}px, 0)`),h}),u=h=>t("scroll",{scrollTop:h,isFixed:i.fixed}),d=()=>{if(!o.value||fo(o))return;const{container:h,position:m}=e,v=Oe(o),p=Tn(window);if(i.width=v.width,i.height=v.height,m==="top")if(h){const b=Oe(h),w=b.bottom-r.value-i.height;i.fixed=r.value>v.top&&b.bottom>0,i.transform=w<0?w:0}else i.fixed=r.value>v.top;else{const{clientHeight:b}=document.documentElement;if(h){const w=Oe(h),g=b-w.top-r.value-i.height;i.fixed=b-r.value<v.bottom&&b>w.top,i.transform=g<0?-g:0}else i.fixed=b-r.value<v.bottom}u(p)};return te(()=>i.fixed,h=>t("change",h)),Ye("scroll",d,{target:a,passive:!0}),Pf(o,d),te([Vt,Pt],()=>{!o.value||fo(o)||!i.fixed||(l.value=!0,Se(()=>{const h=Oe(o);i.width=h.width,i.height=h.height,l.value=!1}))}),()=>{var h;return f("div",{ref:o,style:s.value},[f("div",{class:e0({fixed:i.fixed&&!l.value}),style:c.value},[(h=n.default)==null?void 0:h.call(n)])])}}});const $f=Z(n0),[Af,qa]=Y("swipe"),o0={loop:H,width:G,height:G,vertical:Boolean,autoplay:se(0),duration:se(500),touchable:H,lazyRender:Boolean,initialSwipe:se(0),indicatorColor:String,showIndicators:H,stopPropagation:H},Of=Symbol(Af);var a0=W({name:Af,props:o0,emits:["change","dragStart","dragEnd"],setup(e,{emit:t,slots:n}){const o=M(),a=M(),i=je({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let l=!1;const r=Ot(),{children:s,linkChildren:c}=dt(Of),u=B(()=>s.length),d=B(()=>i[e.vertical?"height":"width"]),h=B(()=>e.vertical?r.deltaY.value:r.deltaX.value),m=B(()=>i.rect?(e.vertical?i.rect.height:i.rect.width)-d.value*u.value:0),v=B(()=>d.value?Math.ceil(Math.abs(m.value)/d.value):u.value),p=B(()=>u.value*d.value),b=B(()=>(i.active+u.value)%u.value),w=B(()=>{const re=e.vertical?"vertical":"horizontal";return r.direction.value===re}),g=B(()=>{const re={transitionDuration:`${i.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+i.offset.toFixed(2)}px)`};if(d.value){const N=e.vertical?"height":"width",ee=e.vertical?"width":"height";re[N]=`${p.value}px`,re[ee]=e[ee]?`${e[ee]}px`:""}return re}),x=re=>{const{active:N}=i;return re?e.loop?Xe(N+re,-1,u.value):Xe(N+re,0,v.value):N},S=(re,N=0)=>{let ee=re*d.value;e.loop||(ee=Math.min(ee,-m.value));let fe=N-ee;return e.loop||(fe=Xe(fe,m.value,0)),fe},y=({pace:re=0,offset:N=0,emitChange:ee})=>{if(u.value<=1)return;const{active:fe}=i,q=x(re),ce=S(q,N);if(e.loop){if(s[0]&&ce!==m.value){const _=ce<m.value;s[0].setOffset(_?p.value:0)}if(s[u.value-1]&&ce!==0){const _=ce>0;s[u.value-1].setOffset(_?-p.value:0)}}i.active=q,i.offset=ce,ee&&q!==fe&&t("change",b.value)},T=()=>{i.swiping=!0,i.active<=-1?y({pace:u.value}):i.active>=u.value&&y({pace:-u.value})},O=()=>{T(),r.reset(),Wn(()=>{i.swiping=!1,y({pace:-1,emitChange:!0})})},C=()=>{T(),r.reset(),Wn(()=>{i.swiping=!1,y({pace:1,emitChange:!0})})};let A;const I=()=>clearTimeout(A),P=()=>{I(),+e.autoplay>0&&u.value>1&&(A=setTimeout(()=>{C(),P()},+e.autoplay))},k=(re=+e.initialSwipe)=>{if(!o.value)return;const N=()=>{var ee,fe;if(!fo(o)){const q={width:o.value.offsetWidth,height:o.value.offsetHeight};i.rect=q,i.width=+((ee=e.width)!=null?ee:q.width),i.height=+((fe=e.height)!=null?fe:q.height)}u.value&&(re=Math.min(u.value-1,re),re===-1&&(re=u.value-1)),i.active=re,i.swiping=!0,i.offset=S(re),s.forEach(q=>{q.setOffset(0)}),P()};fo(o)?Se().then(N):N()},D=()=>k(i.active);let j;const ae=re=>{!e.touchable||re.touches.length>1||(r.start(re),l=!1,j=Date.now(),I(),T())},F=re=>{e.touchable&&i.swiping&&(r.move(re),w.value&&(!e.loop&&(i.active===0&&h.value>0||i.active===u.value-1&&h.value<0)||(Ve(re,e.stopPropagation),y({offset:h.value}),l||(t("dragStart",{index:b.value}),l=!0))))},ne=()=>{if(!e.touchable||!i.swiping)return;const re=Date.now()-j,N=h.value/re;if((Math.abs(N)>.25||Math.abs(h.value)>d.value/2)&&w.value){const fe=e.vertical?r.offsetY.value:r.offsetX.value;let q=0;e.loop?q=fe>0?h.value>0?-1:1:0:q=-Math[h.value>0?"ceil":"floor"](h.value/d.value),y({pace:q,emitChange:!0})}else h.value&&y({pace:0});l=!1,i.swiping=!1,t("dragEnd",{index:b.value}),P()},oe=(re,N={})=>{T(),r.reset(),Wn(()=>{let ee;e.loop&&re===u.value?ee=i.active===0?0:re:ee=re%u.value,N.immediate?Wn(()=>{i.swiping=!1}):i.swiping=!1,y({pace:ee-i.active,emitChange:!0})})},Te=(re,N)=>{const ee=N===b.value,fe=ee?{backgroundColor:e.indicatorColor}:void 0;return f("i",{style:fe,class:qa("indicator",{active:ee})},null)},Ee=()=>{if(n.indicator)return n.indicator({active:b.value,total:u.value});if(e.showIndicators&&u.value>1)return f("div",{class:qa("indicators",{vertical:e.vertical})},[Array(u.value).fill("").map(Te)])};return _e({prev:O,next:C,state:i,resize:D,swipeTo:oe}),c({size:d,props:e,count:u,activeIndicator:b}),te(()=>e.initialSwipe,re=>k(+re)),te(u,()=>k(i.active)),te(()=>e.autoplay,P),te([Vt,Pt,()=>e.width,()=>e.height],D),te(Ob(),re=>{re==="visible"?P():I()}),We(k),an(()=>k(i.active)),Zi(()=>k(i.active)),ln(I),rn(I),Ye("touchmove",F,{target:a}),()=>{var re;return f("div",{ref:o,class:qa()},[f("div",{ref:a,style:g.value,class:qa("track",{vertical:e.vertical}),onTouchstartPassive:ae,onTouchend:ne,onTouchcancel:ne},[(re=n.default)==null?void 0:re.call(n)]),Ee()])}}});const Zr=Z(a0),[i0,Dc]=Y("tabs");var l0=W({name:i0,props:{count:Ze(Number),inited:Boolean,animated:Boolean,duration:Ze(G),swipeable:Boolean,lazyRender:Boolean,currentIndex:Ze(Number)},emits:["change"],setup(e,{emit:t,slots:n}){const o=M(),a=r=>t("change",r),i=()=>{var r;const s=(r=n.default)==null?void 0:r.call(n);return e.animated||e.swipeable?f(Zr,{ref:o,loop:!1,class:Dc("track"),duration:+e.duration*1e3,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:a},{default:()=>[s]}):s},l=r=>{const s=o.value;s&&s.state.active!==r&&s.swipeTo(r,{immediate:!e.inited})};return te(()=>e.currentIndex,l),We(()=>{l(e.currentIndex)}),_e({swipeRef:o}),()=>f("div",{class:Dc("content",{animated:e.animated||e.swipeable})},[i()])}});const[If,Ga]=Y("tabs"),r0={type:J("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:se(0),duration:se(.3),animated:Boolean,ellipsis:H,swipeable:Boolean,scrollspy:Boolean,offsetTop:se(0),background:String,lazyRender:H,showHeader:H,lineWidth:G,lineHeight:G,beforeChange:Function,swipeThreshold:se(5),titleActiveColor:String,titleInactiveColor:String},Rf=Symbol(If);var s0=W({name:If,props:r0,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:t,slots:n}){let o,a,i,l,r;const s=M(),c=M(),u=M(),d=M(),h=Xo(),m=qo(s),[v,p]=Fa(),{children:b,linkChildren:w}=dt(Rf),g=je({inited:!1,position:"",lineStyle:{},currentIndex:-1}),x=B(()=>b.length>+e.swipeThreshold||!e.ellipsis||e.shrink),S=B(()=>({borderColor:e.color,background:e.background})),y=(q,ce)=>{var _;return(_=q.name)!=null?_:ce},T=B(()=>{const q=b[g.currentIndex];if(q)return y(q,g.currentIndex)}),O=B(()=>Ur(e.offsetTop)),C=B(()=>e.sticky?O.value+o:0),A=q=>{const ce=c.value,_=v.value;if(!x.value||!ce||!_||!_[g.currentIndex])return;const V=_[g.currentIndex].$el,L=V.offsetLeft-(ce.offsetWidth-V.offsetWidth)/2;l&&l(),l=Xy(ce,L,q?0:+e.duration)},I=()=>{const q=g.inited;Se(()=>{const ce=v.value;if(!ce||!ce[g.currentIndex]||e.type!=="line"||fo(s.value))return;const _=ce[g.currentIndex].$el,{lineWidth:V,lineHeight:L}=e,X=_.offsetLeft+_.offsetWidth/2,me={width:we(V),backgroundColor:e.color,transform:`translateX(${X}px) translateX(-50%)`};if(q&&(me.transitionDuration=`${e.duration}s`),ke(L)){const E=we(L);me.height=E,me.borderRadius=E}g.lineStyle=me})},P=q=>{const ce=q<g.currentIndex?-1:1;for(;q>=0&&q<b.length;){if(!b[q].disabled)return q;q+=ce}},k=(q,ce)=>{const _=P(q);if(!ke(_))return;const V=b[_],L=y(V,_),X=g.currentIndex!==null;g.currentIndex!==_&&(g.currentIndex=_,ce||A(),I()),L!==e.active&&(t("update:active",L),X&&t("change",L,V.title)),i&&!e.scrollspy&&Ra(Math.ceil(wc(s.value)-O.value))},D=(q,ce)=>{const _=b.findIndex((V,L)=>y(V,L)===q);k(_===-1?0:_,ce)},j=(q=!1)=>{if(e.scrollspy){const ce=b[g.currentIndex].$el;if(ce&&m.value){const _=wc(ce,m.value)-C.value;a=!0,r&&r(),r=Zy(m.value,_,q?0:+e.duration,()=>{a=!1})}}},ae=(q,ce,_)=>{const{title:V,disabled:L}=b[ce],X=y(b[ce],ce);L||(Zn(e.beforeChange,{args:[X],done:()=>{k(ce),j()}}),bf(q)),t("clickTab",{name:X,title:V,event:_,disabled:L})},F=q=>{i=q.isFixed,t("scroll",q)},ne=q=>{Se(()=>{D(q),j(!0)})},oe=()=>{for(let q=0;q<b.length;q++){const{top:ce}=Oe(b[q].$el);if(ce>C.value)return q===0?0:q-1}return b.length-1},Te=()=>{if(e.scrollspy&&!a){const q=oe();k(q)}},Ee=()=>{if(e.type==="line"&&b.length)return f("div",{class:Ga("line"),style:g.lineStyle},null)},re=()=>{var q,ce,_;const{type:V,border:L,sticky:X}=e,me=[f("div",{ref:X?void 0:u,class:[Ga("wrap"),{[Xi]:V==="line"&&L}]},[f("div",{ref:c,role:"tablist",class:Ga("nav",[V,{shrink:e.shrink,complete:x.value}]),style:S.value,"aria-orientation":"horizontal"},[(q=n["nav-left"])==null?void 0:q.call(n),b.map(E=>E.renderTitle(ae)),Ee(),(ce=n["nav-right"])==null?void 0:ce.call(n)])]),(_=n["nav-bottom"])==null?void 0:_.call(n)];return X?f("div",{ref:u},[me]):me},N=()=>{I(),Se(()=>{var q,ce;A(!0),(ce=(q=d.value)==null?void 0:q.swipeRef.value)==null||ce.resize()})};te(()=>[e.color,e.duration,e.lineWidth,e.lineHeight],I),te(Vt,N),te(()=>e.active,q=>{q!==T.value&&D(q)}),te(()=>b.length,()=>{g.inited&&(D(e.active),I(),Se(()=>{A(!0)}))});const ee=()=>{D(e.active,!0),Se(()=>{g.inited=!0,u.value&&(o=Oe(u.value).height),A(!0)})},fe=(q,ce)=>t("rendered",q,ce);return _e({resize:N,scrollTo:ne}),an(I),Zi(I),Ko(ee),Pf(s,I),Ye("scroll",Te,{target:m,passive:!0}),w({id:h,props:e,setLine:I,scrollable:x,onRendered:fe,currentName:T,setTitleRefs:p,scrollIntoView:A}),()=>f("div",{ref:s,class:Ga([e.type])},[e.showHeader?e.sticky?f($f,{container:s.value,offsetTop:O.value,onScroll:F},{default:()=>[re()]}):re():null,f(l0,{ref:d,count:b.length,inited:g.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:g.currentIndex,onChange:k},{default:()=>{var q;return[(q=n.default)==null?void 0:q.call(n)]}})])}});const Df=Symbol(),c0=()=>rt(Df,null),[u0,Bc]=Y("tab"),d0=W({name:u0,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:G,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:H},setup(e,{slots:t}){const n=B(()=>{const a={},{type:i,color:l,disabled:r,isActive:s,activeColor:c,inactiveColor:u}=e;l&&i==="card"&&(a.borderColor=l,r||(s?a.backgroundColor=l:a.color=l));const h=s?c:u;return h&&(a.color=h),a}),o=()=>{const a=f("span",{class:Bc("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||ke(e.badge)&&e.badge!==""?f(vo,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>f("div",{id:e.id,role:"tab",class:[Bc([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:n.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[o()])}}),[f0,h0]=Y("swipe-item");var m0=W({name:f0,setup(e,{slots:t}){let n;const o=je({offset:0,inited:!1,mounted:!1}),{parent:a,index:i}=it(Of);if(!a)return;const l=B(()=>{const c={},{vertical:u}=a.props;return a.size.value&&(c[u?"height":"width"]=`${a.size.value}px`),o.offset&&(c.transform=`translate${u?"Y":"X"}(${o.offset}px)`),c}),r=B(()=>{const{loop:c,lazyRender:u}=a.props;if(!u||n)return!0;if(!o.mounted)return!1;const d=a.activeIndicator.value,h=a.count.value-1,m=d===0&&c?h:d-1,v=d===h&&c?0:d+1;return n=i.value===d||i.value===m||i.value===v,n}),s=c=>{o.offset=c};return We(()=>{Se(()=>{o.mounted=!0})}),_e({setOffset:s}),()=>{var c;return f("div",{class:h0(),style:l.value},[r.value?(c=t.default)==null?void 0:c.call(t):null])}}});const Jr=Z(m0),[v0,Pl]=Y("tab"),g0=he({},Jn,{dot:Boolean,name:G,badge:G,title:String,disabled:Boolean,titleClass:ze,titleStyle:[String,Object],showZeroBadge:H});var b0=W({name:v0,props:g0,setup(e,{slots:t}){const n=Xo(),o=M(!1),a=jt(),{parent:i,index:l}=it(Rf);if(!i)return;const r=()=>{var v;return(v=e.name)!=null?v:l.value},s=()=>{o.value=!0,i.props.lazyRender&&Se(()=>{i.onRendered(r(),e.title)})},c=B(()=>{const v=r()===i.currentName.value;return v&&!o.value&&s(),v}),u=M(""),d=M("");Yo(()=>{const{titleClass:v,titleStyle:p}=e;u.value=v?Vi(v):"",d.value=p&&typeof p!="string"?Xh(Li(p)):p});const h=v=>f(d0,Ce({key:n,id:`${i.id}-${l.value}`,ref:i.setTitleRefs(l.value),style:d.value,class:u.value,isActive:c.value,controls:n,scrollable:i.scrollable.value,activeColor:i.props.titleActiveColor,inactiveColor:i.props.titleInactiveColor,onClick:p=>v(a.proxy,l.value,p)},$e(i.props,["type","color","shrink"]),$e(e,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title}),m=M(!c.value);return te(c,v=>{v?m.value=!1:Wn(()=>{m.value=!0})}),te(()=>e.title,()=>{i.setLine(),i.scrollIntoView()}),pn(Df,c),_e({id:n,renderTitle:h}),()=>{var v;const p=`${i.id}-${l.value}`,{animated:b,swipeable:w,scrollspy:g,lazyRender:x}=i.props;if(!t.default&&!b)return;const S=g||c.value;if(b||w)return f(Jr,{id:n,role:"tabpanel",class:Pl("panel-wrapper",{inactive:m.value}),tabindex:c.value?0:-1,"aria-hidden":!c.value,"aria-labelledby":p,"data-allow-mismatch":"attribute"},{default:()=>{var O;return[f("div",{class:Pl("panel")},[(O=t.default)==null?void 0:O.call(t)])]}});const T=o.value||g||!x?(v=t.default)==null?void 0:v.call(t):null;return Je(f("div",{id:n,role:"tabpanel",class:Pl("panel"),tabindex:S?0:-1,"aria-labelledby":p,"data-allow-mismatch":"attribute"},[T]),[[at,S]])}}});const Da=Z(b0),el=Z(s0),[Bf,$l]=Y("picker-group"),Mf=Symbol(Bf),y0=he({tabs:Ne(),activeTab:se(0),nextStepText:String,showToolbar:H},Qi);var p0=W({name:Bf,props:y0,emits:["confirm","cancel","update:activeTab"],setup(e,{emit:t,slots:n}){const o=Xr(()=>e.activeTab,c=>t("update:activeTab",c)),{children:a,linkChildren:i}=dt(Mf);i();const l=()=>+o.value<e.tabs.length-1&&e.nextStepText,r=()=>{l()?o.value=+o.value+1:t("confirm",a.map(c=>c.confirm()))},s=()=>t("cancel");return()=>{var c,u;let d=(u=(c=n.default)==null?void 0:c.call(n))==null?void 0:u.filter(m=>m.type!==tt).map(m=>m.type===qe?m.children:m);d&&(d=wb(d));const h=l()?e.nextStepText:e.confirmButtonText;return f("div",{class:$l()},[e.showToolbar?f(Ef,{title:e.title,cancelButtonText:e.cancelButtonText,confirmButtonText:h,onConfirm:r,onCancel:s},$e(n,kf)):null,f(el,{active:o.value,"onUpdate:active":m=>o.value=m,class:$l("tabs"),shrink:!0,animated:!0,lazyRender:!1},{default:()=>[e.tabs.map((m,v)=>f(Da,{title:m,titleClass:$l("tab-title")},{default:()=>[d==null?void 0:d[v]]}))]})])}}});const tl=he({loading:Boolean,readonly:Boolean,allowHtml:Boolean,optionHeight:se(44),showToolbar:H,swipeDuration:se(1e3),visibleOptionNum:se(6)},Qi),w0=he({},tl,{columns:Ne(),modelValue:Ne(),toolbarPosition:J("top"),columnsFieldNames:Object});var x0=W({name:zy,props:w0,emits:["confirm","cancel","change","scrollInto","clickOption","update:modelValue"],setup(e,{emit:t,slots:n}){const o=M(),a=M(e.modelValue.slice(0)),{parent:i}=it(Mf),{children:l,linkChildren:r}=dt(_f);r();const s=B(()=>Uy(e.columnsFieldNames)),c=B(()=>Ur(e.optionHeight)),u=B(()=>Hy(e.columns,s.value)),d=B(()=>{const{columns:k}=e;switch(u.value){case"multiple":return k;case"cascade":return jy(k,s.value,a);default:return[k]}}),h=B(()=>d.value.some(k=>k.length)),m=B(()=>d.value.map((k,D)=>hr(k,a.value[D],s.value))),v=B(()=>d.value.map((k,D)=>k.findIndex(j=>j[s.value.value]===a.value[D]))),p=(k,D)=>{if(a.value[k]!==D){const j=a.value.slice(0);j[k]=D,a.value=j}},b=()=>({selectedValues:a.value.slice(0),selectedOptions:m.value,selectedIndexes:v.value}),w=(k,D)=>{p(D,k),u.value==="cascade"&&a.value.forEach((j,ae)=>{const F=d.value[ae];Oc(F,j,s.value)||p(ae,F.length?F[0][s.value.value]:void 0)}),Se(()=>{t("change",he({columnIndex:D},b()))})},g=(k,D)=>{const j={columnIndex:D,currentOption:k};t("clickOption",he(b(),j)),t("scrollInto",j)},x=()=>{l.forEach(D=>D.stopMomentum());const k=b();return Se(()=>{const D=b();t("confirm",D)}),k},S=()=>t("cancel",b()),y=()=>d.value.map((k,D)=>f(Ky,{value:a.value[D],fields:s.value,options:k,readonly:e.readonly,allowHtml:e.allowHtml,optionHeight:c.value,swipeDuration:e.swipeDuration,visibleOptionNum:e.visibleOptionNum,onChange:j=>w(j,D),onClickOption:j=>g(j,D),onScrollInto:j=>{t("scrollInto",{currentOption:j,columnIndex:D})}},{option:n.option})),T=k=>{if(h.value){const D={height:`${c.value}px`},j={backgroundSize:`100% ${(k-c.value)/2}px`};return[f("div",{class:yn("mask"),style:j},null),f("div",{class:[qb,yn("frame")],style:D},null)]}},O=()=>{const k=c.value*+e.visibleOptionNum,D={height:`${k}px`};return!e.loading&&!h.value&&n.empty?n.empty():f("div",{ref:o,class:yn("columns"),style:D},[y(),T(k)])},C=()=>{if(e.showToolbar&&!i)return f(Ef,Ce($e(e,Gy),{onConfirm:x,onCancel:S}),$e(n,kf))},A=k=>{k.forEach((D,j)=>{D.length&&!Oc(D,a.value[j],s.value)&&p(j,Cf(D)[s.value.value])})};te(d,k=>A(k),{immediate:!0});let I;return te(()=>e.modelValue,k=>{!on(k,a.value)&&!on(k,I)&&(a.value=k.slice(0),I=k.slice(0)),e.modelValue.length===0&&A(d.value)},{deep:!0}),te(a,k=>{on(k,e.modelValue)||(I=k.slice(0),t("update:modelValue",I))},{immediate:!0}),Ye("touchmove",Ve,{target:o}),_e({confirm:x,getSelectedOptions:()=>m.value}),()=>{var k,D;return f("div",{class:yn()},[e.toolbarPosition==="top"?C():null,e.loading?f(Wt,{class:yn("loading")},null):null,(k=n["columns-top"])==null?void 0:k.call(n),O(),(D=n["columns-bottom"])==null?void 0:D.call(n),e.toolbarPosition==="bottom"?C():null])}}});const Lo="000000",S0=["title","cancel","confirm","toolbar","columns-top","columns-bottom"],Lf=["title","loading","readonly","optionHeight","swipeDuration","visibleOptionNum","cancelButtonText","confirmButtonText"],Bn=(e="",t=Lo,n=void 0)=>({text:e,value:t,children:n});function C0({areaList:e,columnsNum:t,columnsPlaceholder:n}){const{city_list:o={},county_list:a={},province_list:i={}}=e,l=+t>1,r=+t>2,s=()=>{if(l)return n.length>1?[Bn(n[1],Lo,r?[]:void 0)]:[]},c=new Map;Object.keys(i).forEach(h=>{c.set(h.slice(0,2),Bn(i[h],h,s()))});const u=new Map;if(l){const h=()=>{if(r)return n.length>2?[Bn(n[2])]:[]};Object.keys(o).forEach(m=>{const v=Bn(o[m],m,h());u.set(m.slice(0,4),v);const p=c.get(m.slice(0,2));p&&p.children.push(v)})}r&&Object.keys(a).forEach(h=>{const m=u.get(h.slice(0,4));m&&m.children.push(Bn(a[h],h))});const d=Array.from(c.values());if(n.length){const h=r?[Bn(n[2])]:void 0,m=l?[Bn(n[1],Lo,h)]:void 0;d.unshift(Bn(n[0],Lo,m))}return d}const nl=Z(x0),[T0,_0]=Y("area"),k0=he({},$e(tl,Lf),{modelValue:String,columnsNum:se(3),columnsPlaceholder:Ne(),areaList:{type:Object,default:()=>({})}});var E0=W({name:T0,props:k0,emits:["change","confirm","cancel","update:modelValue"],setup(e,{emit:t,slots:n}){const o=M([]),a=M(),i=B(()=>C0(e)),l=(...c)=>t("change",...c),r=(...c)=>t("cancel",...c),s=(...c)=>t("confirm",...c);return te(o,c=>{const u=c.length?c[c.length-1]:"";u&&u!==e.modelValue&&t("update:modelValue",u)},{deep:!0}),te(()=>e.modelValue,c=>{if(c){const u=o.value.length?o.value[o.value.length-1]:"";c!==u&&(o.value=[`${c.slice(0,2)}0000`,`${c.slice(0,4)}00`,c].slice(0,+e.columnsNum))}else o.value=[]},{immediate:!0}),_e({confirm:()=>{var c;return(c=a.value)==null?void 0:c.confirm()},getSelectedOptions:()=>{var c;return((c=a.value)==null?void 0:c.getSelectedOptions())||[]}}),()=>f(nl,Ce({ref:a,modelValue:o.value,"onUpdate:modelValue":c=>o.value=c,class:_0(),columns:i.value,onChange:l,onCancel:r,onConfirm:s},$e(e,Lf)),$e(n,S0))}});const Vf=Z(E0),[P0,yo]=Y("cell"),ol={tag:J("div"),icon:String,size:String,title:G,value:G,label:G,center:Boolean,isLink:Boolean,border:H,iconPrefix:String,valueClass:ze,labelClass:ze,titleClass:ze,titleStyle:null,arrowDirection:String,required:{type:[Boolean,String],default:null},clickable:{type:Boolean,default:null}},$0=he({},ol,Jn);var A0=W({name:P0,props:$0,setup(e,{slots:t}){const n=mo(),o=()=>{if(t.label||ke(e.label))return f("div",{class:[yo("label"),e.labelClass]},[t.label?t.label():e.label])},a=()=>{var s;if(t.title||ke(e.title)){const c=(s=t.title)==null?void 0:s.call(t);return Array.isArray(c)&&c.length===0?void 0:f("div",{class:[yo("title"),e.titleClass],style:e.titleStyle},[c||f("span",null,[e.title]),o()])}},i=()=>{const s=t.value||t.default;if(s||ke(e.value))return f("div",{class:[yo("value"),e.valueClass]},[s?s():f("span",null,[e.value])])},l=()=>{if(t.icon)return t.icon();if(e.icon)return f(xe,{name:e.icon,class:yo("left-icon"),classPrefix:e.iconPrefix},null)},r=()=>{if(t["right-icon"])return t["right-icon"]();if(e.isLink){const s=e.arrowDirection&&e.arrowDirection!=="right"?`arrow-${e.arrowDirection}`:"arrow";return f(xe,{name:s,class:yo("right-icon")},null)}};return()=>{var s;const{tag:c,size:u,center:d,border:h,isLink:m,required:v}=e,p=(s=e.clickable)!=null?s:m,b={center:d,required:!!v,clickable:p,borderless:!h};return u&&(b[u]=!!u),f(c,{class:yo(b),role:p?"button":void 0,tabindex:p?0:void 0,onClick:n},{default:()=>{var w;return[l(),a(),i(),r(),(w=t.extra)==null?void 0:w.call(t)]}})}}});const Yt=Z(A0),[O0,I0]=Y("form"),R0={colon:Boolean,disabled:Boolean,readonly:Boolean,required:[Boolean,String],showError:Boolean,labelWidth:G,labelAlign:String,inputAlign:String,scrollToError:Boolean,scrollToErrorPosition:String,validateFirst:Boolean,submitOnEnter:H,showErrorMessage:H,errorMessageAlign:String,validateTrigger:{type:[String,Array],default:"onBlur"}};var D0=W({name:O0,props:R0,emits:["submit","failed"],setup(e,{emit:t,slots:n}){const{children:o,linkChildren:a}=dt(df),i=b=>b?o.filter(w=>b.includes(w.name)):o,l=b=>new Promise((w,g)=>{const x=[];i(b).reduce((y,T)=>y.then(()=>{if(!x.length)return T.validate().then(O=>{O&&x.push(O)})}),Promise.resolve()).then(()=>{x.length?g(x):w()})}),r=b=>new Promise((w,g)=>{const x=i(b);Promise.all(x.map(S=>S.validate())).then(S=>{S=S.filter(Boolean),S.length?g(S):w()})}),s=b=>{const w=o.find(g=>g.name===b);return w?new Promise((g,x)=>{w.validate().then(S=>{S?x(S):g()})}):Promise.reject()},c=b=>typeof b=="string"?s(b):e.validateFirst?l(b):r(b),u=b=>{typeof b=="string"&&(b=[b]),i(b).forEach(g=>{g.resetValidation()})},d=()=>o.reduce((b,w)=>(b[w.name]=w.getValidationStatus(),b),{}),h=(b,w)=>{o.some(g=>g.name===b?(g.$el.scrollIntoView(w),!0):!1)},m=()=>o.reduce((b,w)=>(w.name!==void 0&&(b[w.name]=w.formValue.value),b),{}),v=()=>{const b=m();c().then(()=>t("submit",b)).catch(w=>{t("failed",{values:b,errors:w});const{scrollToError:g,scrollToErrorPosition:x}=e;g&&w[0].name&&h(w[0].name,x?{block:x}:void 0)})},p=b=>{Ve(b),v()};return a({props:e}),_e({submit:v,validate:c,getValues:m,scrollToField:h,resetValidation:u,getValidationStatus:d}),()=>{var b;return f("form",{class:I0(),onSubmit:p},[(b=n.default)==null?void 0:b.call(n)])}}});const Qr=Z(D0);function Ff(e){return Array.isArray(e)?!e.length:e===0?!1:!e}function B0(e,t){if(Ff(e)){if(t.required)return!1;if(t.validateEmpty===!1)return!0}return!(t.pattern&&!t.pattern.test(String(e)))}function M0(e,t){return new Promise(n=>{const o=t.validator(e,t);if(Hr(o)){o.then(n);return}n(o)})}function Mc(e,t){const{message:n}=t;return Ho(n)?n(e,t):n||""}function L0({target:e}){e.composing=!0}function Lc({target:e}){e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}function V0(e,t){const n=co();e.style.height="auto";let o=e.scrollHeight;if(Ht(t)){const{maxHeight:a,minHeight:i}=t;a!==void 0&&(o=Math.min(o,a)),i!==void 0&&(o=Math.max(o,i))}o&&(e.style.height=`${o}px`,Ra(n))}function F0(e,t){return e==="number"&&(e="text",t??(t="decimal")),e==="digit"&&(e="tel",t??(t="numeric")),{type:e,inputmode:t}}function dn(e){return[...e].length}function Al(e,t){return[...e].slice(0,t).join("")}const[N0,kt]=Y("field"),es={id:String,name:String,leftIcon:String,rightIcon:String,autofocus:Boolean,clearable:Boolean,maxlength:G,max:Number,min:Number,formatter:Function,clearIcon:J("clear"),modelValue:se(""),inputAlign:String,placeholder:String,autocomplete:String,autocapitalize:String,autocorrect:String,errorMessage:String,enterkeyhint:String,clearTrigger:J("focus"),formatTrigger:J("onChange"),spellcheck:{type:Boolean,default:null},error:{type:Boolean,default:null},disabled:{type:Boolean,default:null},readonly:{type:Boolean,default:null},inputmode:String},z0=he({},ol,es,{rows:G,type:J("text"),rules:Array,autosize:[Boolean,Object],labelWidth:G,labelClass:ze,labelAlign:String,showWordLimit:Boolean,errorMessageAlign:String,colon:{type:Boolean,default:null}});var H0=W({name:N0,props:z0,emits:["blur","focus","clear","keypress","clickInput","endValidate","startValidate","clickLeftIcon","clickRightIcon","update:modelValue"],setup(e,{emit:t,slots:n}){const o=Xo(),a=je({status:"unvalidated",focused:!1,validateMessage:""}),i=M(),l=M(),r=M(),{parent:s}=it(df),c=()=>{var _;return String((_=e.modelValue)!=null?_:"")},u=_=>{if(ke(e[_]))return e[_];if(s&&ke(s.props[_]))return s.props[_]},d=B(()=>{const _=u("readonly");if(e.clearable&&!_){const V=c()!=="",L=e.clearTrigger==="always"||e.clearTrigger==="focus"&&a.focused;return V&&L}return!1}),h=B(()=>r.value&&n.input?r.value():e.modelValue),m=B(()=>{var _;const V=u("required");return V==="auto"?(_=e.rules)==null?void 0:_.some(L=>L.required):V}),v=_=>_.reduce((V,L)=>V.then(()=>{if(a.status==="failed")return;let{value:X}=h;if(L.formatter&&(X=L.formatter(X,L)),!B0(X,L)){a.status="failed",a.validateMessage=Mc(X,L);return}if(L.validator)return Ff(X)&&L.validateEmpty===!1?void 0:M0(X,L).then(me=>{me&&typeof me=="string"?(a.status="failed",a.validateMessage=me):me===!1&&(a.status="failed",a.validateMessage=Mc(X,L))})}),Promise.resolve()),p=()=>{a.status="unvalidated",a.validateMessage=""},b=()=>t("endValidate",{status:a.status,message:a.validateMessage}),w=(_=e.rules)=>new Promise(V=>{p(),_?(t("startValidate"),v(_).then(()=>{a.status==="failed"?(V({name:e.name,message:a.validateMessage}),b()):(a.status="passed",V(),b())})):V()}),g=_=>{if(s&&e.rules){const{validateTrigger:V}=s.props,L=Ti(V).includes(_),X=e.rules.filter(me=>me.trigger?Ti(me.trigger).includes(_):L);X.length&&w(X)}},x=_=>{var V;const{maxlength:L}=e;if(ke(L)&&dn(_)>+L){const X=c();if(X&&dn(X)===+L)return X;const me=(V=i.value)==null?void 0:V.selectionEnd;if(a.focused&&me){const E=[..._],$=E.length-+L;return E.splice(me-$,$),E.join("")}return Al(_,+L)}return _},S=(_,V="onChange")=>{var L,X;const me=_;_=x(_);const E=dn(me)-dn(_);if(e.type==="number"||e.type==="digit"){const R=e.type==="number";if(_=ur(_,R,R),V==="onBlur"&&_!==""&&(e.min!==void 0||e.max!==void 0)){const z=Xe(+_,(L=e.min)!=null?L:-1/0,(X=e.max)!=null?X:1/0);+_!==z&&(_=z.toString())}}let $=0;if(e.formatter&&V===e.formatTrigger){const{formatter:R,maxlength:z}=e;if(_=R(_),ke(z)&&dn(_)>+z&&(_=Al(_,+z)),i.value&&a.focused){const{selectionEnd:K}=i.value,U=Al(me,K);$=dn(R(U))-dn(U)}}if(i.value&&i.value.value!==_)if(a.focused){let{selectionStart:R,selectionEnd:z}=i.value;if(i.value.value=_,ke(R)&&ke(z)){const K=dn(_);E?(R-=E,z-=E):$&&(R+=$,z+=$),i.value.setSelectionRange(Math.min(R,K),Math.min(z,K))}}else i.value.value=_;_!==e.modelValue&&t("update:modelValue",_)},y=_=>{_.target.composing||S(_.target.value)},T=()=>{var _;return(_=i.value)==null?void 0:_.blur()},O=()=>{var _;return(_=i.value)==null?void 0:_.focus()},C=()=>{const _=i.value;e.type==="textarea"&&e.autosize&&_&&V0(_,e.autosize)},A=_=>{a.focused=!0,t("focus",_),Se(C),u("readonly")&&T()},I=_=>{a.focused=!1,S(c(),"onBlur"),t("blur",_),!u("readonly")&&(g("onBlur"),Se(C),of())},P=_=>t("clickInput",_),k=_=>t("clickLeftIcon",_),D=_=>t("clickRightIcon",_),j=_=>{Ve(_),t("update:modelValue",""),t("clear",_)},ae=B(()=>{if(typeof e.error=="boolean")return e.error;if(s&&s.props.showError&&a.status==="failed")return!0}),F=B(()=>{const _=u("labelWidth"),V=u("labelAlign");if(_&&V!=="top")return{width:we(_)}}),ne=_=>{_.keyCode===13&&(!(s&&s.props.submitOnEnter)&&e.type!=="textarea"&&Ve(_),e.type==="search"&&T()),t("keypress",_)},oe=()=>e.id||`${o}-input`,Te=()=>a.status,Ee=()=>{const _=kt("control",[u("inputAlign"),{error:ae.value,custom:!!n.input,"min-height":e.type==="textarea"&&!e.autosize}]);if(n.input)return f("div",{class:_,onClick:P},[n.input()]);const V={id:oe(),ref:i,name:e.name,rows:e.rows!==void 0?+e.rows:void 0,class:_,disabled:u("disabled"),readonly:u("readonly"),autofocus:e.autofocus,placeholder:e.placeholder,autocomplete:e.autocomplete,autocapitalize:e.autocapitalize,autocorrect:e.autocorrect,enterkeyhint:e.enterkeyhint,spellcheck:e.spellcheck,"aria-labelledby":e.label?`${o}-label`:void 0,"data-allow-mismatch":"attribute",onBlur:I,onFocus:A,onInput:y,onClick:P,onChange:Lc,onKeypress:ne,onCompositionend:Lc,onCompositionstart:L0};return e.type==="textarea"?f("textarea",Ce(V,{inputmode:e.inputmode}),null):f("input",Ce(F0(e.type,e.inputmode),V),null)},re=()=>{const _=n["left-icon"];if(e.leftIcon||_)return f("div",{class:kt("left-icon"),onClick:k},[_?_():f(xe,{name:e.leftIcon,classPrefix:e.iconPrefix},null)])},N=()=>{const _=n["right-icon"];if(e.rightIcon||_)return f("div",{class:kt("right-icon"),onClick:D},[_?_():f(xe,{name:e.rightIcon,classPrefix:e.iconPrefix},null)])},ee=()=>{if(e.showWordLimit&&e.maxlength){const _=dn(c());return f("div",{class:kt("word-limit")},[f("span",{class:kt("word-num")},[_]),Lr("/"),e.maxlength])}},fe=()=>{if(s&&s.props.showErrorMessage===!1)return;const _=e.errorMessage||a.validateMessage;if(_){const V=n["error-message"],L=u("errorMessageAlign");return f("div",{class:kt("error-message",L)},[V?V({message:_}):_])}},q=()=>{const _=u("labelWidth"),V=u("labelAlign"),L=u("colon")?":":"";if(n.label)return[n.label(),L];if(e.label)return f("label",{id:`${o}-label`,for:n.input?void 0:oe(),"data-allow-mismatch":"attribute",onClick:X=>{Ve(X),O()},style:V==="top"&&_?{width:we(_)}:void 0},[e.label+L])},ce=()=>[f("div",{class:kt("body")},[Ee(),d.value&&f(xe,{ref:l,name:e.clearIcon,class:kt("clear")},null),N(),n.button&&f("div",{class:kt("button")},[n.button()])]),ee(),fe()];return _e({blur:T,focus:O,validate:w,formValue:h,resetValidation:p,getValidationStatus:Te}),pn(nf,{customValue:r,resetValidation:p,validateWithTrigger:g}),te(()=>e.modelValue,()=>{S(c()),p(),g("onChange"),Se(C)}),We(()=>{S(c(),e.formatTrigger),Se(C)}),Ye("touchstart",j,{target:B(()=>{var _;return(_=l.value)==null?void 0:_.$el})}),()=>{const _=u("disabled"),V=u("labelAlign"),L=re(),X=()=>{const me=q();return V==="top"?[L,me].filter(Boolean):me||[]};return f(Yt,{size:e.size,class:kt({error:ae.value,disabled:_,[`label-${V}`]:V}),center:e.center,border:e.border,isLink:e.isLink,clickable:e.clickable,titleStyle:F.value,valueClass:kt("value"),titleClass:[kt("label",[V,{required:m.value}]),e.labelClass],arrowDirection:e.arrowDirection},{icon:L&&V!=="top"?()=>L:null,title:X,value:ce,extra:n.extra})}}});const wn=Z(H0);let oa=0;function j0(e){e?(oa||document.body.classList.add("van-toast--unclickable"),oa++):oa&&(oa--,oa||document.body.classList.remove("van-toast--unclickable"))}const[W0,po]=Y("toast"),U0=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],Y0={icon:String,show:Boolean,type:J("text"),overlay:Boolean,message:G,iconSize:G,duration:Ge(2e3),position:J("middle"),teleport:[String,Object],wordBreak:String,className:ze,iconPrefix:String,transition:J("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:ze,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:G};var Nf=W({name:W0,props:Y0,emits:["update:show"],setup(e,{emit:t,slots:n}){let o,a=!1;const i=()=>{const d=e.show&&e.forbidClick;a!==d&&(a=d,j0(a))},l=d=>t("update:show",d),r=()=>{e.closeOnClick&&l(!1)},s=()=>clearTimeout(o),c=()=>{const{icon:d,type:h,iconSize:m,iconPrefix:v,loadingType:p}=e;if(d||h==="success"||h==="fail")return f(xe,{name:d||h,size:m,class:po("icon"),classPrefix:v},null);if(h==="loading")return f(Wt,{class:po("loading"),size:m,type:p},null)},u=()=>{const{type:d,message:h}=e;if(n.message)return f("div",{class:po("text")},[n.message()]);if(ke(h)&&h!=="")return d==="html"?f("div",{key:0,class:po("text"),innerHTML:String(h)},null):f("div",{class:po("text")},[h])};return te(()=>[e.show,e.forbidClick],i),te(()=>[e.show,e.type,e.message,e.duration],()=>{s(),e.show&&e.duration>0&&(o=setTimeout(()=>{l(!1)},e.duration))}),We(i),Uo(i),()=>f(Ut,Ce({class:[po([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:r,onClosed:s,"onUpdate:show":l},$e(e,U0)),{default:()=>[c(),u()]})}});function zf(){const e=je({show:!1}),t=a=>{e.show=a},n=a=>{he(e,a,{transitionAppear:!0}),t(!0)},o=()=>t(!1);return _e({open:n,close:o,toggle:t}),{open:n,close:o,state:e,toggle:t}}function Hf(e){const t=Nd(e),n=document.createElement("div");return document.body.appendChild(n),{instance:t.mount(n),unmount(){t.unmount(),document.body.removeChild(n)}}}const K0={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let Oo=[],q0=!1,Vc=he({},K0);const G0=new Map;function jf(e){return Ht(e)?e:{message:e}}function X0(){const{instance:e}=Hf({setup(){const t=M(""),{open:n,state:o,close:a,toggle:i}=zf(),l=()=>{},r=()=>f(Nf,Ce(o,{onClosed:l,"onUpdate:show":i}),null);return te(t,s=>{o.message=s}),jt().render=r,{open:n,close:a,message:t}}});return e}function Z0(){if(!Oo.length||q0){const e=X0();Oo.push(e)}return Oo[Oo.length-1]}function Pi(e={}){if(!At)return{};const t=Z0(),n=jf(e);return t.open(he({},Vc,G0.get(n.type||Vc.type),n)),t}const J0=e=>t=>Pi(he({type:e},jf(t))),lk=J0("loading"),rk=e=>{Oo.length&&Oo[0].close()},Q0=Z(Nf),[ep,Ol]=Y("switch"),tp={size:G,loading:Boolean,disabled:Boolean,modelValue:ze,activeColor:String,inactiveColor:String,activeValue:{type:ze,default:!0},inactiveValue:{type:ze,default:!1}};var np=W({name:ep,props:tp,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=()=>e.modelValue===e.activeValue,a=()=>{if(!e.disabled&&!e.loading){const l=o()?e.inactiveValue:e.activeValue;t("update:modelValue",l),t("change",l)}},i=()=>{if(e.loading){const l=o()?e.activeColor:e.inactiveColor;return f(Wt,{class:Ol("loading"),color:l},null)}if(n.node)return n.node()};return Xn(()=>e.modelValue),()=>{var l;const{size:r,loading:s,disabled:c,activeColor:u,inactiveColor:d}=e,h=o(),m={fontSize:we(r),backgroundColor:h?u:d};return f("div",{role:"switch",class:Ol({on:h,loading:s,disabled:c}),style:m,tabindex:c?void 0:0,"aria-checked":h,onClick:a},[f("div",{class:Ol("node")},[i()]),(l=n.background)==null?void 0:l.call(n)])}}});const ts=Z(np),[op,Fc]=Y("address-edit-detail"),Nc=Y("address-edit")[2];var ap=W({name:op,props:{show:Boolean,rows:G,value:String,rules:Array,focused:Boolean,maxlength:G,searchResult:Array,showSearchResult:Boolean},emits:["blur","focus","input","selectSearch"],setup(e,{emit:t}){const n=M(),o=()=>e.focused&&e.searchResult&&e.showSearchResult,a=c=>{t("selectSearch",c),t("input",`${c.address||""} ${c.name||""}`.trim())},i=()=>{if(!o())return;const{searchResult:c}=e;return c.map(u=>f(Yt,{clickable:!0,key:(u.name||"")+(u.address||""),icon:"location-o",title:u.name,label:u.address,class:Fc("search-item"),border:!1,onClick:()=>a(u)},null))},l=c=>t("blur",c),r=c=>t("focus",c),s=c=>t("input",c);return()=>{if(e.show)return f(qe,null,[f(wn,{autosize:!0,clearable:!0,ref:n,class:Fc(),rows:e.rows,type:"textarea",rules:e.rules,label:Nc("addressDetail"),border:!o(),maxlength:e.maxlength,modelValue:e.value,placeholder:Nc("addressDetail"),onBlur:l,onFocus:r,"onUpdate:modelValue":s},null),i()])}}});const[ip,wo,ft]=Y("address-edit"),Wf={name:"",tel:"",city:"",county:"",province:"",areaCode:"",isDefault:!1,addressDetail:""},lp={areaList:Object,isSaving:Boolean,isDeleting:Boolean,validator:Function,showArea:H,showDetail:H,showDelete:Boolean,disableArea:Boolean,searchResult:Array,telMaxlength:G,showSetDefault:Boolean,saveButtonText:String,areaPlaceholder:String,deleteButtonText:String,showSearchResult:Boolean,detailRows:se(1),detailMaxlength:se(200),areaColumnsPlaceholder:Ne(),addressInfo:{type:Object,default:()=>he({},Wf)},telValidator:{type:Function,default:Qd}};var rp=W({name:ip,props:lp,emits:["save","focus","change","delete","clickArea","changeArea","changeDetail","selectSearch","changeDefault"],setup(e,{emit:t,slots:n}){const o=M(),a=je({}),i=M(!1),l=M(!1),r=B(()=>Ht(e.areaList)&&Object.keys(e.areaList).length),s=B(()=>{const{province:T,city:O,county:C,areaCode:A}=a;if(A){const I=[T,O,C];return T&&T===O&&I.splice(1,1),I.filter(Boolean).join("/")}return""}),c=B(()=>{var T;return((T=e.searchResult)==null?void 0:T.length)&&l.value}),u=T=>{l.value=T==="addressDetail",t("focus",T)},d=(T,O)=>{t("change",{key:T,value:O})},h=B(()=>{const{validator:T,telValidator:O}=e,C=(A,I)=>({validator:P=>{if(T){const k=T(A,P);if(k)return k}return P?!0:I}});return{name:[C("name",ft("nameEmpty"))],tel:[C("tel",ft("telInvalid")),{validator:O,message:ft("telInvalid")}],areaCode:[C("areaCode",ft("areaEmpty"))],addressDetail:[C("addressDetail",ft("addressEmpty"))]}}),m=()=>t("save",a),v=T=>{a.addressDetail=T,t("changeDetail",T)},p=T=>{a.province=T[0].text,a.city=T[1].text,a.county=T[2].text},b=({selectedValues:T,selectedOptions:O})=>{T.some(C=>C===Lo)?Pi(ft("areaEmpty")):(i.value=!1,p(O),t("changeArea",O))},w=()=>t("delete",a),g=T=>{a.areaCode=T||""},x=()=>{setTimeout(()=>{l.value=!1})},S=T=>{a.addressDetail=T},y=()=>{if(e.showSetDefault){const T={"right-icon":()=>f(ts,{modelValue:a.isDefault,"onUpdate:modelValue":O=>a.isDefault=O,onChange:O=>t("changeDefault",O)},null)};return Je(f(Yt,{center:!0,border:!1,title:ft("defaultAddress"),class:wo("default")},T),[[at,!c.value]])}};return _e({setAreaCode:g,setAddressDetail:S}),te(()=>e.addressInfo,T=>{he(a,Wf,T),Se(()=>{var O;const C=(O=o.value)==null?void 0:O.getSelectedOptions();C&&C.every(A=>A&&A.value!==Lo)&&p(C)})},{deep:!0,immediate:!0}),()=>{const{disableArea:T}=e;return f(Qr,{class:wo(),onSubmit:m},{default:()=>{var O;return[f("div",{class:wo("fields")},[f(wn,{modelValue:a.name,"onUpdate:modelValue":[C=>a.name=C,C=>d("name",C)],clearable:!0,label:ft("name"),rules:h.value.name,placeholder:ft("name"),onFocus:()=>u("name")},null),f(wn,{modelValue:a.tel,"onUpdate:modelValue":[C=>a.tel=C,C=>d("tel",C)],clearable:!0,type:"tel",label:ft("tel"),rules:h.value.tel,maxlength:e.telMaxlength,placeholder:ft("tel"),onFocus:()=>u("tel")},null),Je(f(wn,{readonly:!0,label:ft("area"),"is-link":!T,modelValue:s.value,rules:e.showArea?h.value.areaCode:void 0,placeholder:e.areaPlaceholder||ft("area"),onFocus:()=>u("areaCode"),onClick:()=>{t("clickArea"),i.value=!T}},null),[[at,e.showArea]]),f(ap,{show:e.showDetail,rows:e.detailRows,rules:h.value.addressDetail,value:a.addressDetail,focused:l.value,maxlength:e.detailMaxlength,searchResult:e.searchResult,showSearchResult:e.showSearchResult,onBlur:x,onFocus:()=>u("addressDetail"),onInput:v,onSelectSearch:C=>t("selectSearch",C)},null),(O=n.default)==null?void 0:O.call(n)]),y(),Je(f("div",{class:wo("buttons")},[f(ut,{block:!0,round:!0,type:"primary",text:e.saveButtonText||ft("save"),class:wo("button"),loading:e.isSaving,nativeType:"submit"},null),e.showDelete&&f(ut,{block:!0,round:!0,class:wo("button"),loading:e.isDeleting,text:e.deleteButtonText||ft("delete"),onClick:w},null)]),[[at,!c.value]]),f(Ut,{show:i.value,"onUpdate:show":C=>i.value=C,round:!0,teleport:"body",position:"bottom",lazyRender:!1},{default:()=>[f(Vf,{modelValue:a.areaCode,"onUpdate:modelValue":C=>a.areaCode=C,ref:o,loading:!r.value,areaList:e.areaList,columnsPlaceholder:e.areaColumnsPlaceholder,onConfirm:b,onCancel:()=>{i.value=!1}},null)]})]}})}}});const sp=Z(rp),[Uf,cp]=Y("radio-group"),up={shape:String,disabled:Boolean,iconSize:G,direction:String,modelValue:ze,checkedColor:String},Yf=Symbol(Uf);var dp=W({name:Uf,props:up,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:o}=dt(Yf),a=i=>t("update:modelValue",i);return te(()=>e.modelValue,i=>t("change",i)),o({props:e,updateValue:a}),Xn(()=>e.modelValue),()=>{var i;return f("div",{class:cp([e.direction]),role:"radiogroup"},[(i=n.default)==null?void 0:i.call(n)])}}});const ns=Z(dp),[Kf,fp]=Y("checkbox-group"),hp={max:G,shape:J("round"),disabled:Boolean,iconSize:G,direction:String,modelValue:Ne(),checkedColor:String},qf=Symbol(Kf);var mp=W({name:Kf,props:hp,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{children:o,linkChildren:a}=dt(qf),i=r=>t("update:modelValue",r),l=(r={})=>{typeof r=="boolean"&&(r={checked:r});const{checked:s,skipDisabled:c}=r,d=o.filter(h=>h.props.bindGroup?h.props.disabled&&c?h.checked.value:s??!h.checked.value:!1).map(h=>h.name);i(d)};return te(()=>e.modelValue,r=>t("change",r)),_e({toggleAll:l}),Xn(()=>e.modelValue),a({props:e,updateValue:i}),()=>{var r;return f("div",{class:fp([e.direction])},[(r=n.default)==null?void 0:r.call(n)])}}});const Gf=Z(mp),[vp,zc]=Y("tag"),gp={size:String,mark:Boolean,show:H,type:J("default"),color:String,plain:Boolean,round:Boolean,textColor:String,closeable:Boolean};var bp=W({name:vp,props:gp,emits:["close"],setup(e,{slots:t,emit:n}){const o=l=>{l.stopPropagation(),n("close",l)},a=()=>e.plain?{color:e.textColor||e.color,borderColor:e.color}:{color:e.textColor,background:e.color},i=()=>{var l;const{type:r,mark:s,plain:c,round:u,size:d,closeable:h}=e,m={mark:s,plain:c,round:u};d&&(m[d]=d);const v=h&&f(xe,{name:"cross",class:[zc("close"),ct],onClick:o},null);return f("span",{style:a(),class:zc([m,r])},[(l=t.default)==null?void 0:l.call(t),v])};return()=>f(Ui,{name:e.closeable?"van-fade":void 0},{default:()=>[e.show?i():null]})}});const al=Z(bp),os={name:ze,disabled:Boolean,iconSize:G,modelValue:ze,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var Xf=W({props:he({},os,{bem:Ze(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:H,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:t,slots:n}){const o=M(),a=h=>{if(e.parent&&e.bindGroup)return e.parent.props[h]},i=B(()=>{if(e.parent&&e.bindGroup){const h=a("disabled")||e.disabled;if(e.role==="checkbox"){const m=a("modelValue").length,v=a("max"),p=v&&m>=+v;return h||p&&!e.checked}return h}return e.disabled}),l=B(()=>a("direction")),r=B(()=>{const h=e.checkedColor||a("checkedColor");if(h&&e.checked&&!i.value)return{borderColor:h,backgroundColor:h}}),s=B(()=>e.shape||a("shape")||"round"),c=h=>{const{target:m}=h,v=o.value,p=v===m||(v==null?void 0:v.contains(m));!i.value&&(p||!e.labelDisabled)&&t("toggle"),t("click",h)},u=()=>{var h,m;const{bem:v,checked:p,indeterminate:b}=e,w=e.iconSize||a("iconSize");return f("div",{ref:o,class:v("icon",[s.value,{disabled:i.value,checked:p,indeterminate:b}]),style:s.value!=="dot"?{fontSize:we(w)}:{width:we(w),height:we(w),borderColor:(h=r.value)==null?void 0:h.borderColor}},[n.icon?n.icon({checked:p,disabled:i.value}):s.value!=="dot"?f(xe,{name:b?"minus":"success",style:r.value},null):f("div",{class:v("icon--dot__icon"),style:{backgroundColor:(m=r.value)==null?void 0:m.backgroundColor}},null)])},d=()=>{const{checked:h}=e;if(n.default)return f("span",{class:e.bem("label",[e.labelPosition,{disabled:i.value}])},[n.default({checked:h,disabled:i.value})])};return()=>{const h=e.labelPosition==="left"?[d(),u()]:[u(),d()];return f("div",{role:e.role,class:e.bem([{disabled:i.value,"label-disabled":e.labelDisabled},l.value]),tabindex:i.value?void 0:0,"aria-checked":e.checked,onClick:c},[h])}}});const yp=he({},os,{shape:String}),[pp,wp]=Y("radio");var xp=W({name:pp,props:yp,emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{parent:o}=it(Yf),a=()=>(o?o.props.modelValue:e.modelValue)===e.name,i=()=>{o?o.updateValue(e.name):t("update:modelValue",e.name)};return()=>f(Xf,Ce({bem:wp,role:"radio",parent:o,checked:a(),onToggle:i},e),$e(n,["default","icon"]))}});const as=Z(xp),[Sp,Cp]=Y("checkbox"),Tp=he({},os,{shape:String,bindGroup:H,indeterminate:{type:Boolean,default:null}});var _p=W({name:Sp,props:Tp,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{parent:o}=it(qf),a=r=>{const{name:s}=e,{max:c,modelValue:u}=o.props,d=u.slice();if(r)!(c&&d.length>=+c)&&!d.includes(s)&&(d.push(s),e.bindGroup&&o.updateValue(d));else{const h=d.indexOf(s);h!==-1&&(d.splice(h,1),e.bindGroup&&o.updateValue(d))}},i=B(()=>o&&e.bindGroup?o.props.modelValue.indexOf(e.name)!==-1:!!e.modelValue),l=(r=!i.value)=>{o&&e.bindGroup?a(r):t("update:modelValue",r),e.indeterminate!==null&&t("change",r)};return te(()=>e.modelValue,r=>{e.indeterminate===null&&t("change",r)}),_e({toggle:l,props:e,checked:i}),Xn(()=>e.modelValue),()=>f(Xf,Ce({bem:Cp,role:"checkbox",parent:o,checked:i.value,onToggle:l},e),$e(n,["default","icon"]))}});const is=Z(_p),[kp,xo]=Y("address-item");var Ep=W({name:kp,props:{address:Ze(Object),disabled:Boolean,switchable:Boolean,singleChoice:Boolean,defaultTagText:String,rightIcon:J("edit")},emits:["edit","click","select"],setup(e,{slots:t,emit:n}){const o=r=>{e.switchable&&n("select"),n("click",r)},a=()=>f(xe,{name:e.rightIcon,class:xo("edit"),onClick:r=>{r.stopPropagation(),n("edit"),n("click",r)}},null),i=()=>{if(t.tag)return t.tag(e.address);if(e.address.isDefault&&e.defaultTagText)return f(al,{type:"primary",round:!0,class:xo("tag")},{default:()=>[e.defaultTagText]})},l=()=>{const{address:r,disabled:s,switchable:c,singleChoice:u}=e,d=[f("div",{class:xo("name")},[`${r.name} ${r.tel}`,i()]),f("div",{class:xo("address")},[r.address])];return c&&!s?u?f(as,{name:r.id,iconSize:18},{default:()=>[d]}):f(is,{name:r.id,iconSize:18},{default:()=>[d]}):d};return()=>{var r;const{disabled:s}=e;return f("div",{class:xo({disabled:s}),onClick:o},[f(Yt,{border:!1,titleClass:xo("title")},{title:l,"right-icon":a}),(r=t.bottom)==null?void 0:r.call(t,he({},e.address,{disabled:s}))])}}});const[Pp,Xa,$p]=Y("address-list"),Ap={list:Ne(),modelValue:[...G,Array],switchable:H,disabledText:String,disabledList:Ne(),showAddButton:H,addButtonText:String,defaultTagText:String,rightIcon:J("edit")};var Op=W({name:Pp,props:Ap,emits:["add","edit","select","clickItem","editDisabled","selectDisabled","update:modelValue"],setup(e,{slots:t,emit:n}){const o=B(()=>!Array.isArray(e.modelValue)),a=(r,s,c)=>{const u=()=>n(c?"editDisabled":"edit",r,s),d=m=>n("clickItem",r,s,{event:m}),h=()=>{if(n(c?"selectDisabled":"select",r,s),!c)if(o.value)n("update:modelValue",r.id);else{const m=e.modelValue;m.includes(r.id)?n("update:modelValue",m.filter(v=>v!==r.id)):n("update:modelValue",[...m,r.id])}};return f(Ep,{key:r.id,address:r,disabled:c,switchable:e.switchable,singleChoice:o.value,defaultTagText:e.defaultTagText,rightIcon:e.rightIcon,onEdit:u,onClick:d,onSelect:h},{bottom:t["item-bottom"],tag:t.tag})},i=(r,s)=>{if(r)return r.map((c,u)=>a(c,u,s))},l=()=>e.showAddButton?f("div",{class:[Xa("bottom"),"van-safe-area-bottom"]},[f(ut,{round:!0,block:!0,type:"primary",text:e.addButtonText||$p("add"),class:Xa("add"),onClick:()=>n("add")},null)]):void 0;return()=>{var r,s;const c=i(e.list),u=i(e.disabledList,!0),d=e.disabledText&&f("div",{class:Xa("disabled-text")},[e.disabledText]);return f("div",{class:Xa()},[(r=t.top)==null?void 0:r.call(t),!o.value&&Array.isArray(e.modelValue)?f(Gf,{modelValue:e.modelValue},{default:()=>[c]}):f(ns,{modelValue:e.modelValue},{default:()=>[c]}),d,u,(s=t.default)==null?void 0:s.call(t),l()])}}});const Ip=Z(Op);function Rp(e,t){let n=null,o=0;return function(...a){if(n)return;const i=Date.now()-o,l=()=>{o=Date.now(),n=!1,e.apply(this,a)};i>=t?l():n=setTimeout(l,t)}}const[Dp,Il]=Y("back-top"),Bp={right:G,bottom:G,zIndex:G,target:[String,Object],offset:se(200),immediate:Boolean,teleport:{type:[String,Object],default:"body"}};var Mp=W({name:Dp,inheritAttrs:!1,props:Bp,emits:["click"],setup(e,{emit:t,slots:n,attrs:o}){let a=!1;const i=M(!1),l=M(),r=M(),s=B(()=>he(On(e.zIndex),{right:we(e.right),bottom:we(e.bottom)})),c=m=>{var v;t("click",m),(v=r.value)==null||v.scrollTo({top:0,behavior:e.immediate?"auto":"smooth"})},u=()=>{i.value=r.value?Tn(r.value)>=+e.offset:!1},d=()=>{const{target:m}=e;if(typeof m=="string"){const v=document.querySelector(m);if(v)return v}else return m},h=()=>{At&&Se(()=>{r.value=e.target?d():jr(l.value),u()})};return Ye("scroll",Rp(u,100),{target:r}),We(h),an(()=>{a&&(i.value=!0,a=!1)}),ln(()=>{i.value&&e.teleport&&(i.value=!1,a=!0)}),te(()=>e.target,h),()=>{const m=f("div",Ce({ref:e.teleport?void 0:l,class:Il({active:i.value}),style:s.value,onClick:c},o),[n.default?n.default():f(xe,{name:"back-top",class:Il("icon")},null)]);return e.teleport?[f("div",{ref:l,class:Il("placeholder")},null),f(ho,{to:e.teleport},{default:()=>[m]})]:m}}});const Lp=Z(Mp);var Vp=(e,t,n)=>new Promise((o,a)=>{var i=s=>{try{r(n.next(s))}catch(c){a(c)}},l=s=>{try{r(n.throw(s))}catch(c){a(c)}},r=s=>s.done?o(s.value):Promise.resolve(s.value).then(i,l);r((n=n.apply(e,t)).next())});const Fp={top:se(10),rows:se(4),duration:se(4e3),autoPlay:H,delay:Ge(300),modelValue:Ne()},[Np,Hc]=Y("barrage");var zp=W({name:Np,props:Fp,emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const o=M(),a=Hc("item"),i=M(0),l=[],r=(p,b=e.delay)=>{const w=document.createElement("span");return w.className=a,w.innerText=String(p),w.style.animationDuration=`${e.duration}ms`,w.style.animationDelay=`${b}ms`,w.style.animationName="van-barrage",w.style.animationTimingFunction="linear",w},s=M(!0),c=M(e.autoPlay),u=({id:p,text:b},w)=>{var g;const x=r(b,s.value?w*e.delay:void 0);!e.autoPlay&&c.value===!1&&(x.style.animationPlayState="paused"),(g=o.value)==null||g.append(x),i.value++;const S=(i.value-1)%+e.rows*x.offsetHeight+ +e.top;x.style.top=`${S}px`,x.dataset.id=String(p),l.push(x),x.addEventListener("animationend",()=>{t("update:modelValue",[...e.modelValue].filter(y=>String(y.id)!==x.dataset.id))})},d=(p,b)=>{const w=new Map(b.map(g=>[g.id,g]));p.forEach((g,x)=>{w.has(g.id)?w.delete(g.id):u(g,x)}),w.forEach(g=>{const x=l.findIndex(S=>S.dataset.id===String(g.id));x>-1&&(l[x].remove(),l.splice(x,1))}),s.value=!1};te(()=>e.modelValue.slice(),(p,b)=>d(p??[],b??[]),{deep:!0});const h=M({});return We(()=>Vp(null,null,function*(){var p;h.value["--move-distance"]=`-${(p=o.value)==null?void 0:p.offsetWidth}px`,yield Se(),d(e.modelValue,[])})),_e({play:()=>{c.value=!0,l.forEach(p=>{p.style.animationPlayState="running"})},pause:()=>{c.value=!1,l.forEach(p=>{p.style.animationPlayState="paused"})}}),()=>{var p;return f("div",{class:Hc(),ref:o,style:h.value},[(p=n.default)==null?void 0:p.call(n)])}}});const Hp=Z(zp),[jp,Ue,xn]=Y("calendar"),Wp=e=>xn("monthTitle",e.getFullYear(),e.getMonth()+1);function ro(e,t){const n=e.getFullYear(),o=t.getFullYear();if(n===o){const a=e.getMonth(),i=t.getMonth();return a===i?0:a>i?1:-1}return n>o?1:-1}function pt(e,t){const n=ro(e,t);if(n===0){const o=e.getDate(),a=t.getDate();return o===a?0:o>a?1:-1}return n}const jo=e=>new Date(e),jc=e=>Array.isArray(e)?e.map(jo):jo(e);function ls(e,t){const n=jo(e);return n.setDate(n.getDate()+t),n}function rs(e,t){const n=jo(e);return n.setMonth(n.getMonth()+t),n.getDate()!==e.getDate()&&n.setDate(0),n}function Zf(e,t){const n=jo(e);return n.setFullYear(n.getFullYear()+t),n.getDate()!==e.getDate()&&n.setDate(0),n}const mr=e=>ls(e,-1),vr=e=>ls(e,1),Wc=e=>rs(e,-1),Uc=e=>rs(e,1),Yc=e=>Zf(e,-1),Kc=e=>Zf(e,1),Za=()=>{const e=new Date;return e.setHours(0,0,0,0),e};function Up(e){const t=e[0].getTime();return(e[1].getTime()-t)/(1e3*60*60*24)+1}function Yp(e,t=0){const n=new Date(e.getFullYear(),e.getMonth()+1,0),o=t+e.getDate()-1,a=t+n.getDate()-1;return Math.floor(o/7)===Math.floor(a/7)}const Jf=he({},tl,{modelValue:Ne(),filter:Function,formatter:{type:Function,default:(e,t)=>t}}),Qf=Object.keys(tl);function Kp(e,t){if(e<0)return[];const n=Array(e);let o=-1;for(;++o<e;)n[o]=t(o);return n}const eh=(e,t)=>32-new Date(e,t-1,32).getDate(),Vo=(e,t,n,o,a,i)=>{const l=Kp(t-e+1,r=>{const s=Bt(e+r);return o(n,{text:s,value:s})});return a?a(n,l,i):l},th=(e,t)=>e.map((n,o)=>{const a=t[o];if(a.length){const i=+a[0].value,l=+a[a.length-1].value;return Bt(Xe(+n,i,l))}return n}),[qp]=Y("calendar-day");var Gp=W({name:qp,props:{item:Ze(Object),color:String,index:Number,offset:Ge(0),rowHeight:String},emits:["click","clickDisabledDate"],setup(e,{emit:t,slots:n}){const o=B(()=>{const{item:c,index:u,color:d,offset:h,rowHeight:m}=e,v={height:m};if(c.type==="placeholder")return v.width="100%",v;if(u===0&&(v.marginLeft=`${100*h/7}%`),d)switch(c.type){case"end":case"start":case"start-end":case"multiple-middle":case"multiple-selected":v.background=d;break;case"middle":v.color=d;break}return c.date&&Yp(c.date,h)&&(v.marginBottom=0),v}),a=()=>{e.item.type!=="disabled"?t("click",e.item):t("clickDisabledDate",e.item)},i=()=>{const{topInfo:c}=e.item;if(c||n["top-info"])return f("div",{class:Ue("top-info")},[n["top-info"]?n["top-info"](e.item):c])},l=()=>{const{bottomInfo:c}=e.item;if(c||n["bottom-info"])return f("div",{class:Ue("bottom-info")},[n["bottom-info"]?n["bottom-info"](e.item):c])},r=()=>n.text?n.text(e.item):e.item.text,s=()=>{const{item:c,color:u,rowHeight:d}=e,{type:h}=c,m=[i(),r(),l()];return h==="selected"?f("div",{class:Ue("selected-day"),style:{width:d,height:d,background:u}},[m]):m};return()=>{const{type:c,className:u}=e.item;return c==="placeholder"?f("div",{class:Ue("day"),style:o.value},null):f("div",{role:"gridcell",style:o.value,class:[Ue("day",c),u],tabindex:c==="disabled"?void 0:-1,onClick:a},[s()])}}});const[Xp]=Y("calendar-month"),Zp={date:Ze(Date),type:String,color:String,minDate:Date,maxDate:Date,showMark:Boolean,rowHeight:G,formatter:Function,lazyRender:Boolean,currentDate:[Date,Array],allowSameDay:Boolean,showSubtitle:Boolean,showMonthTitle:Boolean,firstDayOfWeek:Number};var Jp=W({name:Xp,props:Zp,emits:["click","clickDisabledDate"],setup(e,{emit:t,slots:n}){const[o,a]=Sb(),i=M(),l=M(),r=mf(l),s=B(()=>Wp(e.date)),c=B(()=>we(e.rowHeight)),u=B(()=>{const I=e.date.getDate(),k=(e.date.getDay()-I%7+8)%7;return e.firstDayOfWeek?(k+7-e.firstDayOfWeek)%7:k}),d=B(()=>eh(e.date.getFullYear(),e.date.getMonth()+1)),h=B(()=>o.value||!e.lazyRender),m=()=>s.value,v=I=>{const P=k=>e.currentDate.some(D=>pt(D,k)===0);if(P(I)){const k=mr(I),D=vr(I),j=P(k),ae=P(D);return j&&ae?"multiple-middle":j?"end":ae?"start":"multiple-selected"}return""},p=I=>{const[P,k]=e.currentDate;if(!P)return"";const D=pt(I,P);if(!k)return D===0?"start":"";const j=pt(I,k);return e.allowSameDay&&D===0&&j===0?"start-end":D===0?"start":j===0?"end":D>0&&j<0?"middle":""},b=I=>{const{type:P,minDate:k,maxDate:D,currentDate:j}=e;if(k&&pt(I,k)<0||D&&pt(I,D)>0)return"disabled";if(j===null)return"";if(Array.isArray(j)){if(P==="multiple")return v(I);if(P==="range")return p(I)}else if(P==="single")return pt(I,j)===0?"selected":"";return""},w=I=>{if(e.type==="range"){if(I==="start"||I==="end")return xn(I);if(I==="start-end")return`${xn("start")}/${xn("end")}`}},g=()=>{if(e.showMonthTitle)return f("div",{class:Ue("month-title")},[n["month-title"]?n["month-title"]({date:e.date,text:s.value}):s.value])},x=()=>{if(e.showMark&&h.value)return f("div",{class:Ue("month-mark")},[e.date.getMonth()+1])},S=B(()=>{const I=Math.ceil((d.value+u.value)/7);return Array(I).fill({type:"placeholder"})}),y=B(()=>{const I=[],P=e.date.getFullYear(),k=e.date.getMonth();for(let D=1;D<=d.value;D++){const j=new Date(P,k,D),ae=b(j);let F={date:j,type:ae,text:D,bottomInfo:w(ae)};e.formatter&&(F=e.formatter(F)),I.push(F)}return I}),T=B(()=>y.value.filter(I=>I.type==="disabled")),O=(I,P)=>{if(i.value){const k=Oe(i.value),D=S.value.length,ae=(Math.ceil((P.getDate()+u.value)/7)-1)*k.height/D;_i(I,k.top+ae+I.scrollTop-Oe(I).top)}},C=(I,P)=>f(Gp,{item:I,index:P,color:e.color,offset:u.value,rowHeight:c.value,onClick:k=>t("click",k),onClickDisabledDate:k=>t("clickDisabledDate",k)},$e(n,["top-info","bottom-info","text"])),A=()=>f("div",{ref:i,role:"grid",class:Ue("days")},[x(),(h.value?y:S).value.map(C)]);return _e({getTitle:m,getHeight:()=>r.value,setVisible:a,scrollToDate:O,disabledDays:T}),()=>f("div",{class:Ue("month"),ref:l},[g(),A()])}});const[Qp]=Y("calendar-header");var ew=W({name:Qp,props:{date:Date,minDate:Date,maxDate:Date,title:String,subtitle:String,showTitle:Boolean,showSubtitle:Boolean,firstDayOfWeek:Number,switchMode:J("none")},emits:["clickSubtitle","panelChange"],setup(e,{slots:t,emit:n}){const o=B(()=>e.date&&e.minDate&&ro(Wc(e.date),e.minDate)<0),a=B(()=>e.date&&e.minDate&&ro(Yc(e.date),e.minDate)<0),i=B(()=>e.date&&e.maxDate&&ro(Uc(e.date),e.maxDate)>0),l=B(()=>e.date&&e.maxDate&&ro(Kc(e.date),e.maxDate)>0),r=()=>{if(e.showTitle){const m=e.title||xn("title"),v=t.title?t.title():m;return f("div",{class:Ue("header-title")},[v])}},s=m=>n("clickSubtitle",m),c=m=>n("panelChange",m),u=m=>{const v=e.switchMode==="year-month",p=t[m?"next-month":"prev-month"],b=t[m?"next-year":"prev-year"],w=m?i.value:o.value,g=m?l.value:a.value,x=m?"arrow":"arrow-left",S=m?"arrow-double-right":"arrow-double-left",y=()=>c((m?Uc:Wc)(e.date)),T=()=>c((m?Kc:Yc)(e.date)),O=f("view",{class:Ue("header-action",{disabled:w}),onClick:w?void 0:y},[p?p({disabled:w}):f(xe,{class:{[ct]:!w},name:x},null)]),C=v&&f("view",{class:Ue("header-action",{disabled:g}),onClick:g?void 0:T},[b?b({disabled:g}):f(xe,{class:{[ct]:!g},name:S},null)]);return m?[O,C]:[C,O]},d=()=>{if(e.showSubtitle){const m=t.subtitle?t.subtitle({date:e.date,text:e.subtitle}):e.subtitle,v=e.switchMode!=="none";return f("div",{class:Ue("header-subtitle",{"with-switch":v}),onClick:s},[v?[u(),f("div",{class:Ue("header-subtitle-text")},[m]),u(!0)]:m])}},h=()=>{const{firstDayOfWeek:m}=e,v=xn("weekdays"),p=[...v.slice(m,7),...v.slice(0,m)];return f("div",{class:Ue("weekdays")},[p.map(b=>f("span",{class:Ue("weekday")},[b]))])};return()=>f("div",{class:Ue("header")},[r(),d(),h()])}});const tw={show:Boolean,type:J("single"),switchMode:J("none"),title:String,color:String,round:H,readonly:Boolean,poppable:H,maxRange:se(null),position:J("bottom"),teleport:[String,Object],showMark:H,showTitle:H,formatter:Function,rowHeight:G,confirmText:String,rangePrompt:String,lazyRender:H,showConfirm:H,defaultDate:[Date,Array],allowSameDay:Boolean,showSubtitle:H,closeOnPopstate:H,showRangePrompt:H,confirmDisabledText:String,closeOnClickOverlay:H,safeAreaInsetTop:Boolean,safeAreaInsetBottom:H,minDate:{type:Date,validator:Ia},maxDate:{type:Date,validator:Ia},firstDayOfWeek:{type:G,default:0,validator:e=>e>=0&&e<=6}};var nw=W({name:jp,props:tw,emits:["select","confirm","unselect","monthShow","overRange","update:show","clickSubtitle","clickDisabledDate","clickOverlay","panelChange"],setup(e,{emit:t,slots:n}){const o=B(()=>e.switchMode!=="none"),a=B(()=>!e.minDate&&!o.value?Za():e.minDate),i=B(()=>!e.maxDate&&!o.value?rs(Za(),6):e.maxDate),l=(N,ee=a.value,fe=i.value)=>ee&&pt(N,ee)===-1?ee:fe&&pt(N,fe)===1?fe:N,r=(N=e.defaultDate)=>{const{type:ee,allowSameDay:fe}=e;if(N===null)return N;const q=Za();if(ee==="range"){Array.isArray(N)||(N=[]),N.length===1&&pt(N[0],q)===1&&(N=[]);const ce=a.value,_=i.value,V=l(N[0]||q,ce,_?fe?_:mr(_):void 0),L=l(N[1]||(fe?q:vr(q)),ce?fe?ce:vr(ce):void 0);return[V,L]}return ee==="multiple"?Array.isArray(N)?N.map(ce=>l(ce)):[l(q)]:((!N||Array.isArray(N))&&(N=q),l(N))},s=()=>{const N=Array.isArray(d.value)?d.value[0]:d.value;return N||l(Za())};let c;const u=M(),d=M(r()),h=M(s()),m=M(),[v,p]=Fa(),b=B(()=>e.firstDayOfWeek?+e.firstDayOfWeek%7:0),w=B(()=>{const N=[];if(!a.value||!i.value)return N;const ee=new Date(a.value);ee.setDate(1);do N.push(new Date(ee)),ee.setMonth(ee.getMonth()+1);while(ro(ee,i.value)!==1);return N}),g=B(()=>{if(d.value){if(e.type==="range")return!d.value[0]||!d.value[1];if(e.type==="multiple")return!d.value.length}return!d.value}),x=()=>d.value,S=()=>{const N=Tn(u.value),ee=N+c,fe=w.value.map((L,X)=>v.value[X].getHeight()),q=fe.reduce((L,X)=>L+X,0);if(ee>q&&N>0)return;let ce=0,_;const V=[-1,-1];for(let L=0;L<w.value.length;L++){const X=v.value[L];ce<=ee&&ce+fe[L]>=N&&(V[1]=L,_||(_=X,V[0]=L),v.value[L].showed||(v.value[L].showed=!0,t("monthShow",{date:X.date,title:X.getTitle()}))),ce+=fe[L]}w.value.forEach((L,X)=>{const me=X>=V[0]-1&&X<=V[1]+1;v.value[X].setVisible(me)}),_&&(m.value=_)},y=N=>{o.value?h.value=N:st(()=>{w.value.some((ee,fe)=>ro(ee,N)===0?(u.value&&v.value[fe].scrollToDate(u.value,N),!0):!1),S()})},T=()=>{if(!(e.poppable&&!e.show))if(d.value){const N=e.type==="single"?d.value:d.value[0];Ia(N)&&y(N)}else o.value||st(S)},O=()=>{e.poppable&&!e.show||(o.value||st(()=>{c=Math.floor(Oe(u).height)}),T())},C=(N=r())=>{d.value=N,T()},A=N=>{const{maxRange:ee,rangePrompt:fe,showRangePrompt:q}=e;return ee&&Up(N)>+ee?(q&&Pi(fe||xn("rangePrompt",ee)),t("overRange"),!1):!0},I=N=>{h.value=N,t("panelChange",{date:N})},P=()=>{var N;return t("confirm",(N=d.value)!=null?N:jc(d.value))},k=(N,ee)=>{const fe=q=>{d.value=q,t("select",jc(q))};if(ee&&e.type==="range"&&!A(N)){fe([N[0],ls(N[0],+e.maxRange-1)]);return}fe(N),ee&&!e.showConfirm&&P()},D=(N,ee,fe)=>{var q;return(q=N.find(ce=>pt(ee,ce.date)===-1&&pt(ce.date,fe)===-1))==null?void 0:q.date},j=B(()=>v.value.reduce((N,ee)=>{var fe,q;return N.push(...(q=(fe=ee.disabledDays)==null?void 0:fe.value)!=null?q:[]),N},[])),ae=N=>{if(e.readonly||!N.date)return;const{date:ee}=N,{type:fe}=e;if(fe==="range"){if(!d.value){k([ee]);return}const[q,ce]=d.value;if(q&&!ce){const _=pt(ee,q);if(_===1){const V=D(j.value,q,ee);if(V){const L=mr(V);pt(q,L)===-1?k([q,L]):k([ee])}else k([q,ee],!0)}else _===-1?k([ee]):e.allowSameDay&&k([ee,ee],!0)}else k([ee])}else if(fe==="multiple"){if(!d.value){k([ee]);return}const q=d.value,ce=q.findIndex(_=>pt(_,ee)===0);if(ce!==-1){const[_]=q.splice(ce,1);t("unselect",jo(_))}else e.maxRange&&q.length>=+e.maxRange?Pi(e.rangePrompt||xn("rangePrompt",e.maxRange)):k([...q,ee])}else k(ee,!0)},F=N=>t("clickOverlay",N),ne=N=>t("update:show",N),oe=(N,ee)=>{const fe=ee!==0||!e.showSubtitle;return f(Jp,Ce({ref:o.value?m:p(ee),date:N,currentDate:d.value,showMonthTitle:fe,firstDayOfWeek:b.value,lazyRender:o.value?!1:e.lazyRender,maxDate:i.value,minDate:a.value},$e(e,["type","color","showMark","formatter","rowHeight","showSubtitle","allowSameDay"]),{onClick:ae,onClickDisabledDate:q=>t("clickDisabledDate",q)}),$e(n,["top-info","bottom-info","month-title","text"]))},Te=()=>{if(n.footer)return n.footer();if(e.showConfirm){const N=n["confirm-text"],ee=g.value,fe=ee?e.confirmDisabledText:e.confirmText;return f(ut,{round:!0,block:!0,type:"primary",color:e.color,class:Ue("confirm"),disabled:ee,nativeType:"button",onClick:P},{default:()=>[N?N({disabled:ee}):fe||xn("confirm")]})}},Ee=()=>f("div",{class:[Ue("footer"),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[Te()]),re=()=>{var N,ee;return f("div",{class:Ue()},[f(ew,{date:(N=m.value)==null?void 0:N.date,maxDate:i.value,minDate:a.value,title:e.title,subtitle:(ee=m.value)==null?void 0:ee.getTitle(),showTitle:e.showTitle,showSubtitle:e.showSubtitle,switchMode:e.switchMode,firstDayOfWeek:b.value,onClickSubtitle:fe=>t("clickSubtitle",fe),onPanelChange:I},$e(n,["title","subtitle","prev-month","prev-year","next-month","next-year"])),f("div",{ref:u,class:Ue("body"),onScroll:o.value?void 0:S},[o.value?oe(h.value,0):w.value.map(oe)]),Ee()])};return te(()=>e.show,O),te(()=>[e.type,e.minDate,e.maxDate,e.switchMode],()=>C(r(d.value))),te(()=>e.defaultDate,N=>{C(N)}),_e({reset:C,scrollToDate:y,getSelectedDate:x}),Ko(O),()=>e.poppable?f(Ut,{show:e.show,class:Ue("popup"),round:e.round,position:e.position,closeable:e.showTitle||e.showSubtitle,teleport:e.teleport,closeOnPopstate:e.closeOnPopstate,safeAreaInsetTop:e.safeAreaInsetTop,closeOnClickOverlay:e.closeOnClickOverlay,onClickOverlay:F,"onUpdate:show":ne},{default:re}):re()}});const ow=Z(nw),[aw,So]=Y("image"),iw={src:String,alt:String,fit:String,position:String,round:Boolean,block:Boolean,width:G,height:G,radius:G,lazyLoad:Boolean,iconSize:G,showError:H,errorIcon:J("photo-fail"),iconPrefix:String,showLoading:H,loadingIcon:J("photo"),crossorigin:String,referrerpolicy:String,decoding:String};var lw=W({name:aw,props:iw,emits:["load","error"],setup(e,{emit:t,slots:n}){const o=M(!1),a=M(!0),i=M(),{$Lazyload:l}=jt().proxy,r=B(()=>{const b={width:we(e.width),height:we(e.height)};return ke(e.radius)&&(b.overflow="hidden",b.borderRadius=we(e.radius)),b});te(()=>e.src,()=>{o.value=!1,a.value=!0});const s=b=>{a.value&&(a.value=!1,t("load",b))},c=()=>{const b=new Event("load");Object.defineProperty(b,"target",{value:i.value,enumerable:!0}),s(b)},u=b=>{o.value=!0,a.value=!1,t("error",b)},d=(b,w,g)=>g?g():f(xe,{name:b,size:e.iconSize,class:w,classPrefix:e.iconPrefix},null),h=()=>{if(a.value&&e.showLoading)return f("div",{class:So("loading")},[d(e.loadingIcon,So("loading-icon"),n.loading)]);if(o.value&&e.showError)return f("div",{class:So("error")},[d(e.errorIcon,So("error-icon"),n.error)])},m=()=>{if(o.value||!e.src)return;const b={alt:e.alt,class:So("img"),decoding:e.decoding,style:{objectFit:e.fit,objectPosition:e.position},crossorigin:e.crossorigin,referrerpolicy:e.referrerpolicy};return e.lazyLoad?Je(f("img",Ce({ref:i},b),null),[[Um("lazy"),e.src]]):f("img",Ce({ref:i,src:e.src,onLoad:s,onError:u},b),null)},v=({el:b})=>{const w=()=>{b===i.value&&a.value&&c()};i.value?w():Se(w)},p=({el:b})=>{b===i.value&&!o.value&&u()};return l&&At&&(l.$on("loaded",v),l.$on("error",p),rn(()=>{l.$off("loaded",v),l.$off("error",p)})),We(()=>{Se(()=>{var b;(b=i.value)!=null&&b.complete&&!e.lazyLoad&&c()})}),()=>{var b;return f("div",{class:So({round:e.round,block:e.block}),style:r.value},[m(),h(),(b=n.default)==null?void 0:b.call(n)])}}});const il=Z(lw),[rw,ht]=Y("card"),sw={tag:String,num:G,desc:String,thumb:String,title:String,price:G,centered:Boolean,lazyLoad:Boolean,currency:J("¥"),thumbLink:String,originPrice:G};var cw=W({name:rw,props:sw,emits:["clickThumb"],setup(e,{slots:t,emit:n}){const o=()=>{if(t.title)return t.title();if(e.title)return f("div",{class:[ht("title"),"van-multi-ellipsis--l2"]},[e.title])},a=()=>{if(t.tag||e.tag)return f("div",{class:ht("tag")},[t.tag?t.tag():f(al,{mark:!0,type:"primary"},{default:()=>[e.tag]})])},i=()=>t.thumb?t.thumb():f(il,{src:e.thumb,fit:"cover",width:"100%",height:"100%",lazyLoad:e.lazyLoad},null),l=()=>{if(t.thumb||e.thumb)return f("a",{href:e.thumbLink,class:ht("thumb"),onClick:c=>n("clickThumb",c)},[i(),a()])},r=()=>{if(t.desc)return t.desc();if(e.desc)return f("div",{class:[ht("desc"),"van-ellipsis"]},[e.desc])},s=()=>{const c=e.price.toString().split(".");return f("div",null,[f("span",{class:ht("price-currency")},[e.currency]),f("span",{class:ht("price-integer")},[c[0]]),c.length>1&&f(qe,null,[Lr("."),f("span",{class:ht("price-decimal")},[c[1]])])])};return()=>{var c,u,d;const h=t.num||ke(e.num),m=t.price||ke(e.price),v=t["origin-price"]||ke(e.originPrice),p=h||m||v||t.bottom,b=m&&f("div",{class:ht("price")},[t.price?t.price():s()]),w=v&&f("div",{class:ht("origin-price")},[t["origin-price"]?t["origin-price"]():`${e.currency} ${e.originPrice}`]),g=h&&f("div",{class:ht("num")},[t.num?t.num():`x${e.num}`]),x=t.footer&&f("div",{class:ht("footer")},[t.footer()]),S=p&&f("div",{class:ht("bottom")},[(c=t["price-top"])==null?void 0:c.call(t),b,w,g,(u=t.bottom)==null?void 0:u.call(t)]);return f("div",{class:ht()},[f("div",{class:ht("header")},[l(),f("div",{class:ht("content",{centered:e.centered})},[f("div",null,[o(),r(),(d=t.tags)==null?void 0:d.call(t)]),S])]),x])}}});const uw=Z(cw),[dw,fn,fw]=Y("cascader"),hw={title:String,options:Ne(),closeable:H,swipeable:H,closeIcon:J("cross"),showHeader:H,modelValue:G,fieldNames:Object,placeholder:String,activeColor:String};var mw=W({name:dw,props:hw,emits:["close","change","finish","clickTab","update:modelValue"],setup(e,{slots:t,emit:n}){const o=M([]),a=M(0),[i,l]=Fa(),{text:r,value:s,children:c}=he({text:"text",value:"value",children:"children"},e.fieldNames),u=(y,T)=>{for(const O of y){if(O[s]===T)return[O];if(O[c]){const C=u(O[c],T);if(C)return[O,...C]}}},d=()=>{const{options:y,modelValue:T}=e;if(T!==void 0){const O=u(y,T);if(O){let C=y;o.value=O.map(A=>{const I={options:C,selected:A},P=C.find(k=>k[s]===A[s]);return P&&(C=P[c]),I}),C&&o.value.push({options:C,selected:null}),Se(()=>{a.value=o.value.length-1});return}}o.value=[{options:y,selected:null}]},h=(y,T)=>{if(y.disabled)return;if(o.value[T].selected=y,o.value.length>T+1&&(o.value=o.value.slice(0,T+1)),y[c]){const A={options:y[c],selected:null};o.value[T+1]?o.value[T+1]=A:o.value.push(A),Se(()=>{a.value++})}const O=o.value.map(A=>A.selected).filter(Boolean);n("update:modelValue",y[s]);const C={value:y[s],tabIndex:T,selectedOptions:O};n("change",C),y[c]||n("finish",C)},m=()=>n("close"),v=({name:y,title:T})=>n("clickTab",y,T),p=()=>e.showHeader?f("div",{class:fn("header")},[f("h2",{class:fn("title")},[t.title?t.title():e.title]),e.closeable?f(xe,{name:e.closeIcon,class:[fn("close-icon"),ct],onClick:m},null):null]):null,b=(y,T,O)=>{const{disabled:C}=y,A=!!(T&&y[s]===T[s]),I=y.color||(A?e.activeColor:void 0),P=t.option?t.option({option:y,selected:A}):f("span",null,[y[r]]);return f("li",{ref:A?l(O):void 0,role:"menuitemradio",class:[fn("option",{selected:A,disabled:C}),y.className],style:{color:I},tabindex:C?void 0:A?0:-1,"aria-checked":A,"aria-disabled":C||void 0,onClick:()=>h(y,O)},[P,A?f(xe,{name:"success",class:fn("selected-icon")},null):null])},w=(y,T,O)=>f("ul",{role:"menu",class:fn("options")},[y.map(C=>b(C,T,O))]),g=(y,T)=>{const{options:O,selected:C}=y,A=e.placeholder||fw("select"),I=C?C[r]:A;return f(Da,{title:I,titleClass:fn("tab",{unselected:!C})},{default:()=>{var P,k;return[(P=t["options-top"])==null?void 0:P.call(t,{tabIndex:T}),w(O,C,T),(k=t["options-bottom"])==null?void 0:k.call(t,{tabIndex:T})]}})},x=()=>f(el,{active:a.value,"onUpdate:active":y=>a.value=y,shrink:!0,animated:!0,class:fn("tabs"),color:e.activeColor,swipeable:e.swipeable,onClickTab:v},{default:()=>[o.value.map(g)]}),S=y=>{const T=y.parentElement;T&&(T.scrollTop=y.offsetTop-(T.offsetHeight-y.offsetHeight)/2)};return d(),te(a,y=>{const T=i.value[y];T&&S(T)}),te(()=>e.options,d,{deep:!0}),te(()=>e.modelValue,y=>{y!==void 0&&o.value.map(O=>{var C;return(C=O.selected)==null?void 0:C[s]}).includes(y)||d()}),()=>f("div",{class:fn()},[p(),x()])}});const vw=Z(mw),[gw,qc]=Y("cell-group"),bw={title:String,inset:Boolean,border:H};var yw=W({name:gw,inheritAttrs:!1,props:bw,setup(e,{slots:t,attrs:n}){const o=()=>{var i;return f("div",Ce({class:[qc({inset:e.inset}),{[Xi]:e.border&&!e.inset}]},n,Ei()),[(i=t.default)==null?void 0:i.call(t)])},a=()=>f("div",{class:qc("title",{inset:e.inset})},[t.title?t.title():e.title]);return()=>e.title||t.title?f(qe,null,[a(),o()]):o()}});const pw=Z(yw),[ww,Ja]=Y("circle");let xw=0;const Gc=e=>Math.min(Math.max(+e,0),100);function Sw(e,t){const n=e?1:0;return`M ${t/2} ${t/2} m 0, -500 a 500, 500 0 1, ${n} 0, 1000 a 500, 500 0 1, ${n} 0, -1000`}const Cw={text:String,size:G,fill:J("none"),rate:se(100),speed:se(0),color:[String,Object],clockwise:H,layerColor:String,currentRate:Ge(0),strokeWidth:se(40),strokeLinecap:String,startPosition:J("top")};var Tw=W({name:ww,props:Cw,emits:["update:currentRate"],setup(e,{emit:t,slots:n}){const o=`van-circle-${xw++}`,a=B(()=>+e.strokeWidth+1e3),i=B(()=>Sw(e.clockwise,a.value)),l=B(()=>{const h={top:0,right:90,bottom:180,left:270}[e.startPosition];if(h)return{transform:`rotate(${h}deg)`}});te(()=>e.rate,d=>{let h;const m=Date.now(),v=e.currentRate,p=Gc(d),b=Math.abs((v-p)*1e3/+e.speed),w=()=>{const g=Date.now(),S=Math.min((g-m)/b,1)*(p-v)+v;t("update:currentRate",Gc(parseFloat(S.toFixed(1)))),(p>v?S<p:S>p)&&(h=st(w))};e.speed?(h&&qi(h),h=st(w)):t("update:currentRate",p)},{immediate:!0});const r=()=>{const{strokeWidth:h,currentRate:m,strokeLinecap:v}=e,p=3140*m/100,b=Ht(e.color)?`url(#${o})`:e.color,w={stroke:b,strokeWidth:`${+h+1}px`,strokeLinecap:v,strokeDasharray:`${p}px 3140px`};return f("path",{d:i.value,style:w,class:Ja("hover"),stroke:b},null)},s=()=>{const d={fill:e.fill,stroke:e.layerColor,strokeWidth:`${e.strokeWidth}px`};return f("path",{class:Ja("layer"),style:d,d:i.value},null)},c=()=>{const{color:d}=e;if(!Ht(d))return;const h=Object.keys(d).sort((m,v)=>parseFloat(m)-parseFloat(v)).map((m,v)=>f("stop",{key:v,offset:m,"stop-color":d[m]},null));return f("defs",null,[f("linearGradient",{id:o,x1:"100%",y1:"0%",x2:"0%",y2:"0%"},[h])])},u=()=>{if(n.default)return n.default();if(e.text)return f("div",{class:Ja("text")},[e.text])};return()=>f("div",{class:Ja(),style:An(e.size)},[f("svg",{viewBox:`0 0 ${a.value} ${a.value}`,style:l.value},[c(),s(),r()]),u()])}});const _w=Z(Tw),[nh,kw]=Y("row"),oh=Symbol(nh),Ew={tag:J("div"),wrap:H,align:String,gutter:{type:[String,Number,Array],default:0},justify:String};var Pw=W({name:nh,props:Ew,setup(e,{slots:t}){const{children:n,linkChildren:o}=dt(oh),a=B(()=>{const r=[[]];let s=0;return n.forEach((c,u)=>{s+=Number(c.span),s>24?(r.push([u]),s-=24):r[r.length-1].push(u)}),r}),i=B(()=>{let r=0;Array.isArray(e.gutter)?r=Number(e.gutter[0])||0:r=Number(e.gutter);const s=[];return r&&a.value.forEach(c=>{const u=r*(c.length-1)/c.length;c.forEach((d,h)=>{if(h===0)s.push({right:u});else{const m=r-s[d-1].right,v=u-m;s.push({left:m,right:v})}})}),s}),l=B(()=>{const{gutter:r}=e,s=[];if(Array.isArray(r)&&r.length>1){const c=Number(r[1])||0;if(c<=0)return s;a.value.forEach((u,d)=>{d!==a.value.length-1&&u.forEach(()=>{s.push({bottom:c})})})}return s});return o({spaces:i,verticalSpaces:l}),()=>{const{tag:r,wrap:s,align:c,justify:u}=e;return f(r,{class:kw({[`align-${c}`]:c,[`justify-${u}`]:u,nowrap:!s})},{default:()=>{var d;return[(d=t.default)==null?void 0:d.call(t)]}})}}});const[$w,Aw]=Y("col"),Ow={tag:J("div"),span:se(0),offset:G};var Iw=W({name:$w,props:Ow,setup(e,{slots:t}){const{parent:n,index:o}=it(oh),a=B(()=>{if(!n)return;const{spaces:i,verticalSpaces:l}=n;let r={};if(i&&i.value&&i.value[o.value]){const{left:c,right:u}=i.value[o.value];r={paddingLeft:c?`${c}px`:null,paddingRight:u?`${u}px`:null}}const{bottom:s}=l.value[o.value]||{};return he(r,{marginBottom:s?`${s}px`:null})});return()=>{const{tag:i,span:l,offset:r}=e;return f(i,{style:a.value,class:Aw({[l]:l,[`offset-${r}`]:r})},{default:()=>{var s;return[(s=t.default)==null?void 0:s.call(t)]}})}}});const Rw=Z(Iw),[ah,Dw]=Y("collapse"),ih=Symbol(ah),Bw={border:H,accordion:Boolean,modelValue:{type:[String,Number,Array],default:""}};var Mw=W({name:ah,props:Bw,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:o,children:a}=dt(ih),i=c=>{t("change",c),t("update:modelValue",c)},l=(c,u)=>{const{accordion:d,modelValue:h}=e;i(d?c===h?"":c:u?h.concat(c):h.filter(m=>m!==c))},r=(c={})=>{if(e.accordion)return;typeof c=="boolean"&&(c={expanded:c});const{expanded:u,skipDisabled:d}=c,m=a.filter(v=>v.disabled&&d?v.expanded.value:u??!v.expanded.value).map(v=>v.itemName.value);i(m)},s=c=>{const{accordion:u,modelValue:d}=e;return u?d===c:d.includes(c)};return _e({toggleAll:r}),o({toggle:l,isExpanded:s}),()=>{var c;return f("div",{class:[Dw(),{[Xi]:e.border}]},[(c=n.default)==null?void 0:c.call(n)])}}});const Lw=Z(Mw),[Vw,Qa]=Y("collapse-item"),Fw=["icon","title","value","label","right-icon"],Nw=he({},ol,{name:G,isLink:H,disabled:Boolean,readonly:Boolean,lazyRender:H});var zw=W({name:Vw,props:Nw,setup(e,{slots:t}){const n=M(),o=M(),{parent:a,index:i}=it(ih);if(!a)return;const l=B(()=>{var p;return(p=e.name)!=null?p:i.value}),r=B(()=>a.isExpanded(l.value)),s=M(r.value),c=Gr(()=>s.value||!e.lazyRender),u=()=>{r.value?n.value&&(n.value.style.height=""):s.value=!1};te(r,(p,b)=>{if(b===null)return;p&&(s.value=!0),(p?Se:st)(()=>{if(!o.value||!n.value)return;const{offsetHeight:g}=o.value;if(g){const x=`${g}px`;n.value.style.height=p?"0":x,Wn(()=>{n.value&&(n.value.style.height=p?x:"0")})}else u()})});const d=(p=!r.value)=>{a.toggle(l.value,p)},h=()=>{!e.disabled&&!e.readonly&&d()},m=()=>{const{border:p,disabled:b,readonly:w}=e,g=$e(e,Object.keys(ol));return w&&(g.isLink=!1),(b||w)&&(g.clickable=!1),f(Yt,Ce({role:"button",class:Qa("title",{disabled:b,expanded:r.value,borderless:!p}),"aria-expanded":String(r.value),onClick:h},g),$e(t,Fw))},v=c(()=>{var p;return Je(f("div",{ref:n,class:Qa("wrapper"),onTransitionend:u},[f("div",{ref:o,class:Qa("content")},[(p=t.default)==null?void 0:p.call(t)])]),[[at,s.value]])});return _e({toggle:d,expanded:r,itemName:l}),()=>f("div",{class:[Qa({border:i.value&&e.border})]},[m(),v()])}});const Hw=Z(zw),jw=Z(ly),[Ww,Xc,Rl]=Y("contact-card"),Uw={tel:String,name:String,type:J("add"),addText:String,editable:H};var Yw=W({name:Ww,props:Uw,emits:["click"],setup(e,{emit:t}){const n=a=>{e.editable&&t("click",a)},o=()=>e.type==="add"?e.addText||Rl("addContact"):[f("div",null,[`${Rl("name")}：${e.name}`]),f("div",null,[`${Rl("tel")}：${e.tel}`])];return()=>f(Yt,{center:!0,icon:e.type==="edit"?"contact":"add-square",class:Xc([e.type]),border:!1,isLink:e.editable,titleClass:Xc("title"),onClick:n},{title:o})}});const Kw=Z(Yw),[qw,Co,Mn]=Y("contact-edit"),gr={tel:"",name:""},Gw={isEdit:Boolean,isSaving:Boolean,isDeleting:Boolean,showSetDefault:Boolean,setDefaultLabel:String,contactInfo:{type:Object,default:()=>he({},gr)},telValidator:{type:Function,default:Qd}};var Xw=W({name:qw,props:Gw,emits:["save","delete","changeDefault"],setup(e,{emit:t}){const n=je(he({},gr,e.contactInfo)),o=()=>{e.isSaving||t("save",n)},a=()=>t("delete",n),i=()=>f("div",{class:Co("buttons")},[f(ut,{block:!0,round:!0,type:"primary",text:Mn("save"),class:Co("button"),loading:e.isSaving,nativeType:"submit"},null),e.isEdit&&f(ut,{block:!0,round:!0,text:Mn("delete"),class:Co("button"),loading:e.isDeleting,onClick:a},null)]),l=()=>f(ts,{modelValue:n.isDefault,"onUpdate:modelValue":s=>n.isDefault=s,onChange:s=>t("changeDefault",s)},null),r=()=>{if(e.showSetDefault)return f(Yt,{title:e.setDefaultLabel,class:Co("switch-cell"),border:!1},{"right-icon":l})};return te(()=>e.contactInfo,s=>he(n,gr,s)),()=>f(Qr,{class:Co(),onSubmit:o},{default:()=>[f("div",{class:Co("fields")},[f(wn,{modelValue:n.name,"onUpdate:modelValue":s=>n.name=s,clearable:!0,label:Mn("name"),rules:[{required:!0,message:Mn("nameEmpty")}],maxlength:"30",placeholder:Mn("name")},null),f(wn,{modelValue:n.tel,"onUpdate:modelValue":s=>n.tel=s,clearable:!0,type:"tel",label:Mn("tel"),rules:[{validator:e.telValidator,message:Mn("telInvalid")}],placeholder:Mn("tel")},null)]),r(),i()]})}});const Zw=Z(Xw),[Jw,hn,Qw]=Y("contact-list"),ex={list:Array,addText:String,modelValue:ze,defaultTagText:String};var tx=W({name:Jw,props:ex,emits:["add","edit","select","update:modelValue"],setup(e,{emit:t}){const n=(o,a)=>{const i=()=>{t("update:modelValue",o.id),t("select",o,a)},l=()=>f(as,{class:hn("radio"),name:o.id,iconSize:18},null),r=()=>f(xe,{name:"edit",class:hn("edit"),onClick:c=>{c.stopPropagation(),t("edit",o,a)}},null),s=()=>{const c=[`${o.name}，${o.tel}`];return o.isDefault&&e.defaultTagText&&c.push(f(al,{type:"primary",round:!0,class:hn("item-tag")},{default:()=>[e.defaultTagText]})),c};return f(Yt,{key:o.id,isLink:!0,center:!0,class:hn("item"),titleClass:hn("item-title"),onClick:i},{icon:r,title:s,"right-icon":l})};return()=>f("div",{class:hn()},[f(ns,{modelValue:e.modelValue,class:hn("group")},{default:()=>[e.list&&e.list.map(n)]}),f("div",{class:[hn("bottom"),"van-safe-area-bottom"]},[f(ut,{round:!0,block:!0,type:"primary",class:hn("add"),text:e.addText||Qw("addContact"),onClick:()=>t("add")},null)])])}});const nx=Z(tx);function ox(e,t){const{days:n}=t;let{hours:o,minutes:a,seconds:i,milliseconds:l}=t;if(e.includes("DD")?e=e.replace("DD",Bt(n)):o+=n*24,e.includes("HH")?e=e.replace("HH",Bt(o)):a+=o*60,e.includes("mm")?e=e.replace("mm",Bt(a)):i+=a*60,e.includes("ss")?e=e.replace("ss",Bt(i)):l+=i*1e3,e.includes("S")){const r=Bt(l,3);e.includes("SSS")?e=e.replace("SSS",r):e.includes("SS")?e=e.replace("SS",r.slice(0,2)):e=e.replace("S",r.charAt(0))}return e}const[ax,ix]=Y("count-down"),lx={time:se(0),format:J("HH:mm:ss"),autoStart:H,millisecond:Boolean};var rx=W({name:ax,props:lx,emits:["change","finish"],setup(e,{emit:t,slots:n}){const{start:o,pause:a,reset:i,current:l}=Eb({time:+e.time,millisecond:e.millisecond,onChange:c=>t("change",c),onFinish:()=>t("finish")}),r=B(()=>ox(e.format,l.value)),s=()=>{i(+e.time),e.autoStart&&o()};return te(()=>e.time,s,{immediate:!0}),_e({start:o,pause:a,reset:s}),()=>f("div",{role:"timer",class:ix()},[n.default?n.default(l.value):r.value])}});const sx=Z(rx);function Zc(e){const t=new Date(e*1e3);return`${t.getFullYear()}.${Bt(t.getMonth()+1)}.${Bt(t.getDate())}`}const cx=e=>(e/10).toFixed(e%10===0?0:1),Jc=e=>(e/100).toFixed(e%100===0?0:e%10===0?1:2),[ux,Jt,Dl]=Y("coupon");var dx=W({name:ux,props:{chosen:Boolean,coupon:Ze(Object),disabled:Boolean,currency:J("¥")},setup(e){const t=B(()=>{const{startAt:a,endAt:i}=e.coupon;return`${Zc(a)} - ${Zc(i)}`}),n=B(()=>{const{coupon:a,currency:i}=e;if(a.valueDesc)return[a.valueDesc,f("span",null,[a.unitDesc||""])];if(a.denominations){const l=Jc(a.denominations);return[f("span",null,[i]),` ${l}`]}return a.discount?Dl("discount",cx(a.discount)):""}),o=B(()=>{const a=Jc(e.coupon.originCondition||0);return a==="0"?Dl("unlimited"):Dl("condition",a)});return()=>{const{chosen:a,coupon:i,disabled:l}=e,r=l&&i.reason||i.description;return f("div",{class:Jt({disabled:l})},[f("div",{class:Jt("content")},[f("div",{class:Jt("head")},[f("h2",{class:Jt("amount")},[n.value]),f("p",{class:Jt("condition")},[i.condition||o.value])]),f("div",{class:Jt("body")},[f("p",{class:Jt("name")},[i.name]),f("p",{class:Jt("valid")},[t.value]),!l&&f(is,{class:Jt("corner"),modelValue:a},null)])]),r&&f("p",{class:Jt("description")},[r])])}}});const br=Z(dx),[fx,Qc,yr]=Y("coupon-cell"),hx={title:String,border:H,editable:H,coupons:Ne(),currency:J("¥"),chosenCoupon:{type:[Number,Array],default:-1}},mx=e=>{const{value:t,denominations:n}=e;return ke(t)?t:ke(n)?n:0};function vx({coupons:e,chosenCoupon:t,currency:n}){let o=0,a=!1;return(Array.isArray(t)?t:[t]).forEach(i=>{const l=e[+i];l&&(a=!0,o+=mx(l))}),a?`-${n} ${(o/100).toFixed(2)}`:e.length===0?yr("noCoupon"):yr("count",e.length)}var gx=W({name:fx,props:hx,setup(e){return()=>{const t=Array.isArray(e.chosenCoupon)?e.chosenCoupon.length:e.coupons[+e.chosenCoupon];return f(Yt,{class:Qc(),value:vx(e),title:e.title||yr("title"),border:e.border,isLink:e.editable,valueClass:Qc("value",{selected:t})},null)}}});const bx=Z(gx),[yx,ei]=Y("empty"),px={image:J("default"),imageSize:[Number,String,Array],description:String};var xx=W({name:yx,props:px,setup(e,{slots:t}){const n=()=>{const w=t.description?t.description():e.description;if(w)return f("p",{class:ei("description")},[w])},o=()=>{if(t.default)return f("div",{class:ei("bottom")},[t.default()])},a=Xo(),i=w=>`${a}-${w}`,l=w=>`url(#${i(w)})`,r=(w,g,x)=>f("stop",{"stop-color":w,offset:`${g}%`,"stop-opacity":x},null),s=(w,g)=>[r(w,0),r(g,100)],c=w=>[f("defs",null,[f("radialGradient",{id:i(w),cx:"50%",cy:"54%",fx:"50%",fy:"54%",r:"297%",gradientTransform:"matrix(-.16 0 0 -.33 .58 .72)","data-allow-mismatch":"attribute"},[r("#EBEDF0",0),r("#F2F3F5",100,.3)])]),f("ellipse",{fill:l(w),opacity:".8",cx:"80",cy:"140",rx:"46",ry:"8","data-allow-mismatch":"attribute"},null)],u=()=>[f("defs",null,[f("linearGradient",{id:i("a"),x1:"64%",y1:"100%",x2:"64%","data-allow-mismatch":"attribute"},[r("#FFF",0,.5),r("#F2F3F5",100)])]),f("g",{opacity:".8","data-allow-mismatch":"children"},[f("path",{d:"M36 131V53H16v20H2v58h34z",fill:l("a")},null),f("path",{d:"M123 15h22v14h9v77h-31V15z",fill:l("a")},null)])],d=()=>[f("defs",null,[f("linearGradient",{id:i("b"),x1:"64%",y1:"97%",x2:"64%",y2:"0%","data-allow-mismatch":"attribute"},[r("#F2F3F5",0,.3),r("#F2F3F5",100)])]),f("g",{opacity:".8","data-allow-mismatch":"children"},[f("path",{d:"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z",fill:l("b")},null),f("path",{d:"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z",fill:l("b")},null)])],h=()=>f("svg",{viewBox:"0 0 160 160"},[f("defs",{"data-allow-mismatch":"children"},[f("linearGradient",{id:i(1),x1:"64%",y1:"100%",x2:"64%"},[r("#FFF",0,.5),r("#F2F3F5",100)]),f("linearGradient",{id:i(2),x1:"50%",x2:"50%",y2:"84%"},[r("#EBEDF0",0),r("#DCDEE0",100,0)]),f("linearGradient",{id:i(3),x1:"100%",x2:"100%",y2:"100%"},[s("#EAEDF0","#DCDEE0")]),f("radialGradient",{id:i(4),cx:"50%",cy:"0%",fx:"50%",fy:"0%",r:"100%",gradientTransform:"matrix(0 1 -.54 0 .5 -.5)"},[r("#EBEDF0",0),r("#FFF",100,0)])]),f("g",{fill:"none"},[u(),f("path",{fill:l(4),d:"M0 139h160v21H0z","data-allow-mismatch":"attribute"},null),f("path",{d:"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z",fill:l(2),"data-allow-mismatch":"attribute"},null),f("g",{opacity:".6","stroke-linecap":"round","stroke-width":"7","data-allow-mismatch":"children"},[f("path",{d:"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13",stroke:l(3)},null),f("path",{d:"M53 36a34 34 0 0 0 0 48",stroke:l(3)},null),f("path",{d:"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13",stroke:l(3)},null),f("path",{d:"M106 84a34 34 0 0 0 0-48",stroke:l(3)},null)]),f("g",{transform:"translate(31 105)"},[f("rect",{fill:"#EBEDF0",width:"98",height:"34",rx:"2"},null),f("rect",{fill:"#FFF",x:"9",y:"8",width:"80",height:"18",rx:"1.1"},null),f("rect",{fill:"#EBEDF0",x:"15",y:"12",width:"18",height:"6",rx:"1.1"},null)])])]),m=()=>f("svg",{viewBox:"0 0 160 160"},[f("defs",{"data-allow-mismatch":"children"},[f("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:i(5)},[s("#F2F3F5","#DCDEE0")]),f("linearGradient",{x1:"95%",y1:"48%",x2:"5.5%",y2:"51%",id:i(6)},[s("#EAEDF1","#DCDEE0")]),f("linearGradient",{y1:"45%",x2:"100%",y2:"54%",id:i(7)},[s("#EAEDF1","#DCDEE0")])]),u(),d(),f("g",{transform:"translate(36 50)",fill:"none"},[f("g",{transform:"translate(8)"},[f("rect",{fill:"#EBEDF0",opacity:".6",x:"38",y:"13",width:"36",height:"53",rx:"2"},null),f("rect",{fill:l(5),width:"64",height:"66",rx:"2","data-allow-mismatch":"attribute"},null),f("rect",{fill:"#FFF",x:"6",y:"6",width:"52",height:"55",rx:"1"},null),f("g",{transform:"translate(15 17)",fill:l(6),"data-allow-mismatch":"attribute"},[f("rect",{width:"34",height:"6",rx:"1"},null),f("path",{d:"M0 14h34v6H0z"},null),f("rect",{y:"28",width:"34",height:"6",rx:"1"},null)])]),f("rect",{fill:l(7),y:"61",width:"88",height:"28",rx:"1","data-allow-mismatch":"attribute"},null),f("rect",{fill:"#F7F8FA",x:"29",y:"72",width:"30",height:"6",rx:"1"},null)])]),v=()=>f("svg",{viewBox:"0 0 160 160"},[f("defs",null,[f("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:i(8),"data-allow-mismatch":"attribute"},[s("#EAEDF1","#DCDEE0")])]),u(),d(),c("c"),f("path",{d:"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z",fill:l(8),"data-allow-mismatch":"attribute"},null)]),p=()=>f("svg",{viewBox:"0 0 160 160"},[f("defs",{"data-allow-mismatch":"children"},[f("linearGradient",{x1:"50%",y1:"100%",x2:"50%",id:i(9)},[s("#EEE","#D8D8D8")]),f("linearGradient",{x1:"100%",y1:"50%",y2:"50%",id:i(10)},[s("#F2F3F5","#DCDEE0")]),f("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:i(11)},[s("#F2F3F5","#DCDEE0")]),f("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:i(12)},[s("#FFF","#F7F8FA")])]),u(),d(),c("d"),f("g",{transform:"rotate(-45 113 -4)",fill:"none","data-allow-mismatch":"children"},[f("rect",{fill:l(9),x:"24",y:"52.8",width:"5.8",height:"19",rx:"1"},null),f("rect",{fill:l(10),x:"22.1",y:"67.3",width:"9.9",height:"28",rx:"1"},null),f("circle",{stroke:l(11),"stroke-width":"8",cx:"27",cy:"27",r:"27"},null),f("circle",{fill:l(12),cx:"27",cy:"27",r:"16"},null),f("path",{d:"M37 7c-8 0-15 5-16 12",stroke:l(11),"stroke-width":"3",opacity:".5","stroke-linecap":"round",transform:"rotate(45 29 13)"},null)])]),b=()=>{var w;if(t.image)return t.image();const g={error:v,search:p,network:h,default:m};return((w=g[e.image])==null?void 0:w.call(g))||f("img",{src:e.image},null)};return()=>f("div",{class:ei()},[f("div",{class:ei("image"),style:An(e.imageSize)},[b()]),n(),o()])}});const lh=Z(xx),[Sx,Qt,To]=Y("coupon-list"),Cx={code:J(""),coupons:Ne(),currency:J("¥"),showCount:H,emptyImage:String,enabledTitle:String,disabledTitle:String,disabledCoupons:Ne(),showExchangeBar:H,showCloseButton:H,closeButtonText:String,inputPlaceholder:String,exchangeMinLength:Ge(1),exchangeButtonText:String,displayedCouponIndex:Ge(-1),exchangeButtonLoading:Boolean,exchangeButtonDisabled:Boolean,chosenCoupon:{type:[Number,Array],default:-1}};var Tx=W({name:Sx,props:Cx,emits:["change","exchange","update:code"],setup(e,{emit:t,slots:n}){const[o,a]=Fa(),i=M(),l=M(),r=M(0),s=M(0),c=M(e.code),u=B(()=>!e.exchangeButtonLoading&&(e.exchangeButtonDisabled||!c.value||c.value.length<e.exchangeMinLength)),d=()=>{const x=Oe(i).height,S=Oe(l).height+44;s.value=(x>S?x:Pt.value)-S},h=()=>{t("exchange",c.value),e.code||(c.value="")},m=g=>{Se(()=>{var x;return(x=o.value[g])==null?void 0:x.scrollIntoView()})},v=()=>f(lh,{image:e.emptyImage},{default:()=>[f("p",{class:Qt("empty-tip")},[To("noCoupon")])]}),p=()=>{if(e.showExchangeBar)return f("div",{ref:l,class:Qt("exchange-bar")},[f(wn,{modelValue:c.value,"onUpdate:modelValue":g=>c.value=g,clearable:!0,border:!1,class:Qt("field"),placeholder:e.inputPlaceholder||To("placeholder"),maxlength:"20"},null),f(ut,{plain:!0,type:"primary",class:Qt("exchange"),text:e.exchangeButtonText||To("exchange"),loading:e.exchangeButtonLoading,disabled:u.value,onClick:h},null)])},b=()=>{const{coupons:g,chosenCoupon:x}=e,S=e.showCount?` (${g.length})`:"",y=(e.enabledTitle||To("enable"))+S,T=(O=[],C=0)=>O.includes(C)?O.filter(A=>A!==C):[...O,C];return f(Da,{title:y},{default:()=>{var O;return[f("div",{class:Qt("list",{"with-bottom":e.showCloseButton}),style:{height:`${s.value}px`}},[g.map((C,A)=>f(br,{key:C.id,ref:a(A),coupon:C,chosen:Array.isArray(x)?x.includes(A):A===x,currency:e.currency,onClick:()=>t("change",Array.isArray(x)?T(x,A):A)},null)),!g.length&&v(),(O=n["list-footer"])==null?void 0:O.call(n)])]}})},w=()=>{const{disabledCoupons:g}=e,x=e.showCount?` (${g.length})`:"",S=(e.disabledTitle||To("disabled"))+x;return f(Da,{title:S},{default:()=>{var y;return[f("div",{class:Qt("list",{"with-bottom":e.showCloseButton}),style:{height:`${s.value}px`}},[g.map(T=>f(br,{disabled:!0,key:T.id,coupon:T,currency:e.currency},null)),!g.length&&v(),(y=n["disabled-list-footer"])==null?void 0:y.call(n)])]}})};return te(()=>e.code,g=>{c.value=g}),te(Pt,d),te(c,g=>t("update:code",g)),te(()=>e.displayedCouponIndex,m),We(()=>{d(),m(e.displayedCouponIndex)}),()=>f("div",{ref:i,class:Qt()},[p(),f(el,{active:r.value,"onUpdate:active":g=>r.value=g,class:Qt("tab")},{default:()=>[b(),w()]}),f("div",{class:Qt("bottom")},[n["list-button"]?n["list-button"]():Je(f(ut,{round:!0,block:!0,type:"primary",class:Qt("close"),text:e.closeButtonText||To("close"),onClick:()=>t("change",Array.isArray(e.chosenCoupon)?[]:-1)},null),[[at,e.showCloseButton]])])])}});const _x=Z(Tx),eu=new Date().getFullYear(),[kx]=Y("date-picker"),Ex=he({},Jf,{columnsType:{type:Array,default:()=>["year","month","day"]},minDate:{type:Date,default:()=>new Date(eu-10,0,1),validator:Ia},maxDate:{type:Date,default:()=>new Date(eu+10,11,31),validator:Ia}});var Px=W({name:kx,props:Ex,emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=M(e.modelValue),a=M(!1),i=M(),l=B(()=>a.value?e.modelValue:o.value),r=y=>y===e.minDate.getFullYear(),s=y=>y===e.maxDate.getFullYear(),c=y=>y===e.minDate.getMonth()+1,u=y=>y===e.maxDate.getMonth()+1,d=y=>{const{minDate:T,columnsType:O}=e,C=O.indexOf(y),A=l.value[C];if(A)return+A;switch(y){case"year":return T.getFullYear();case"month":return T.getMonth()+1;case"day":return T.getDate()}},h=()=>{const y=e.minDate.getFullYear(),T=e.maxDate.getFullYear();return Vo(y,T,"year",e.formatter,e.filter,l.value)},m=()=>{const y=d("year"),T=r(y)?e.minDate.getMonth()+1:1,O=s(y)?e.maxDate.getMonth()+1:12;return Vo(T,O,"month",e.formatter,e.filter,l.value)},v=()=>{const y=d("year"),T=d("month"),O=r(y)&&c(T)?e.minDate.getDate():1,C=s(y)&&u(T)?e.maxDate.getDate():eh(y,T);return Vo(O,C,"day",e.formatter,e.filter,l.value)},p=()=>{var y;return(y=i.value)==null?void 0:y.confirm()},b=()=>o.value,w=B(()=>e.columnsType.map(y=>{switch(y){case"year":return h();case"month":return m();case"day":return v();default:return[]}}));te(o,y=>{on(y,e.modelValue)||t("update:modelValue",y)}),te(()=>e.modelValue,(y,T)=>{a.value=on(T,o.value),y=th(y,w.value),on(y,o.value)||(o.value=y),a.value=!1},{immediate:!0});const g=(...y)=>t("change",...y),x=(...y)=>t("cancel",...y),S=(...y)=>t("confirm",...y);return _e({confirm:p,getSelectedDate:b}),()=>f(nl,Ce({ref:i,modelValue:o.value,"onUpdate:modelValue":y=>o.value=y,columns:w.value,onChange:g,onCancel:x,onConfirm:S},$e(e,Qf)),n)}});const $x=Z(Px),[Ax,It,ti]=Y("dialog"),Ox=he({},Go,{title:String,theme:String,width:G,message:[String,Function],callback:Function,allowHtml:Boolean,className:ze,transition:J("van-dialog-bounce"),messageAlign:String,closeOnPopstate:H,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:H,closeOnClickOverlay:Boolean,keyboardEnabled:H,destroyOnClose:Boolean}),Ix=[...qr,"transition","closeOnPopstate","destroyOnClose"];var Rx=W({name:Ax,props:Ox,emits:["confirm","cancel","keydown","update:show"],setup(e,{emit:t,slots:n}){const o=M(),a=je({confirm:!1,cancel:!1}),i=w=>t("update:show",w),l=w=>{var g;i(!1),(g=e.callback)==null||g.call(e,w)},r=w=>()=>{e.show&&(t(w),e.beforeClose?(a[w]=!0,Zn(e.beforeClose,{args:[w],done(){l(w),a[w]=!1},canceled(){a[w]=!1}})):l(w))},s=r("cancel"),c=r("confirm"),u=sg(w=>{var g,x;if(!e.keyboardEnabled||w.target!==((x=(g=o.value)==null?void 0:g.popupRef)==null?void 0:x.value))return;({Enter:e.showConfirmButton?c:lr,Escape:e.showCancelButton?s:lr})[w.key](),t("keydown",w)},["enter","esc"]),d=()=>{const w=n.title?n.title():e.title;if(w)return f("div",{class:It("header",{isolated:!e.message&&!n.default})},[w])},h=w=>{const{message:g,allowHtml:x,messageAlign:S}=e,y=It("message",{"has-title":w,[S]:S}),T=Ho(g)?g():g;return x&&typeof T=="string"?f("div",{class:y,innerHTML:T},null):f("div",{class:y},[T])},m=()=>{if(n.default)return f("div",{class:It("content")},[n.default()]);const{title:w,message:g,allowHtml:x}=e;if(g){const S=!!(w||n.title);return f("div",{key:x?1:0,class:It("content",{isolated:!S})},[h(S)])}},v=()=>f("div",{class:[cf,It("footer")]},[e.showCancelButton&&f(ut,{size:"large",text:e.cancelButtonText||ti("cancel"),class:It("cancel"),style:{color:e.cancelButtonColor},loading:a.cancel,disabled:e.cancelButtonDisabled,onClick:s},null),e.showConfirmButton&&f(ut,{size:"large",text:e.confirmButtonText||ti("confirm"),class:[It("confirm"),{[uf]:e.showCancelButton}],style:{color:e.confirmButtonColor},loading:a.confirm,disabled:e.confirmButtonDisabled,onClick:c},null)]),p=()=>f(gf,{class:It("footer")},{default:()=>[e.showCancelButton&&f(fr,{type:"warning",text:e.cancelButtonText||ti("cancel"),class:It("cancel"),color:e.cancelButtonColor,loading:a.cancel,disabled:e.cancelButtonDisabled,onClick:s},null),e.showConfirmButton&&f(fr,{type:"danger",text:e.confirmButtonText||ti("confirm"),class:It("confirm"),color:e.confirmButtonColor,loading:a.confirm,disabled:e.confirmButtonDisabled,onClick:c},null)]}),b=()=>n.footer?n.footer():e.theme==="round-button"?p():v();return()=>{const{width:w,title:g,theme:x,message:S,className:y}=e;return f(Ut,Ce({ref:o,role:"dialog",class:[It([x]),y],style:{width:we(w)},tabindex:0,"aria-labelledby":g||S,onKeydown:u,"onUpdate:show":i},$e(e,Ix)),{default:()=>[d(),m(),b()]})}}});const Dx=Z(Rx),[Bx,Mx]=Y("divider"),Lx={dashed:Boolean,hairline:H,vertical:Boolean,contentPosition:J("center")};var Vx=W({name:Bx,props:Lx,setup(e,{slots:t}){return()=>{var n;return f("div",{role:"separator",class:Mx({dashed:e.dashed,hairline:e.hairline,vertical:e.vertical,[`content-${e.contentPosition}`]:!!t.default&&!e.vertical})},[!e.vertical&&((n=t.default)==null?void 0:n.call(t))])}}});const Fx=Z(Vx),[rh,ni]=Y("dropdown-menu"),Nx={overlay:H,zIndex:G,duration:se(.2),direction:J("down"),activeColor:String,autoLocate:Boolean,closeOnClickOutside:H,closeOnClickOverlay:H,swipeThreshold:G},sh=Symbol(rh);var zx=W({name:rh,props:Nx,setup(e,{slots:t}){const n=Xo(),o=M(),a=M(),i=M(0),{children:l,linkChildren:r}=dt(sh),s=qo(o),c=B(()=>l.some(g=>g.state.showWrapper)),u=B(()=>e.swipeThreshold&&l.length>+e.swipeThreshold),d=B(()=>{if(c.value&&ke(e.zIndex))return{zIndex:+e.zIndex+1}}),h=()=>{l.forEach(g=>{g.toggle(!1)})},m=()=>{e.closeOnClickOutside&&h()},v=()=>{if(a.value){const g=Oe(a);e.direction==="down"?i.value=g.bottom:i.value=Pt.value-g.top}},p=()=>{c.value&&v()},b=g=>{l.forEach((x,S)=>{S===g?x.toggle():x.state.showPopup&&x.toggle(!1,{immediate:!0})})},w=(g,x)=>{const{showPopup:S}=g.state,{disabled:y,titleClass:T}=g;return f("div",{id:`${n}-${x}`,role:"button",tabindex:y?void 0:0,"data-allow-mismatch":"attribute",class:[ni("item",{disabled:y,grow:u.value}),{[ct]:!y}],onClick:()=>{y||b(x)}},[f("span",{class:[ni("title",{down:S===(e.direction==="down"),active:S}),T],style:{color:S?e.activeColor:""}},[f("div",{class:"van-ellipsis"},[g.renderTitle()])])])};return _e({close:h,opened:c}),r({id:n,props:e,offset:i,opened:c,updateOffset:v}),Gi(o,m),Ye("scroll",p,{target:s,passive:!0}),()=>{var g;return f("div",{ref:o,class:ni()},[f("div",{ref:a,style:d.value,class:ni("bar",{opened:c.value,scrollable:u.value})},[l.map(w)]),(g=t.default)==null?void 0:g.call(t)])}}});const[Hx,oi]=Y("dropdown-item"),jx={title:String,options:Ne(),disabled:Boolean,teleport:[String,Object],lazyRender:H,modelValue:ze,titleClass:ze};var Wx=W({name:Hx,inheritAttrs:!1,props:jx,emits:["open","opened","close","closed","change","update:modelValue"],setup(e,{emit:t,slots:n,attrs:o}){const a=je({showPopup:!1,transition:!0,showWrapper:!1}),i=M(),{parent:l,index:r}=it(sh);if(!l)return;const s=g=>()=>t(g),c=s("open"),u=s("close"),d=s("opened"),h=()=>{a.showWrapper=!1,t("closed")},m=g=>{e.teleport&&g.stopPropagation()},v=(g=!a.showPopup,x={})=>{g!==a.showPopup&&(a.showPopup=g,a.transition=!x.immediate,g&&(l.updateOffset(),a.showWrapper=!0))},p=()=>{if(n.title)return n.title();if(e.title)return e.title;const g=e.options.find(x=>x.value===e.modelValue);return g?g.text:""},b=g=>{const{activeColor:x}=l.props,{disabled:S}=g,y=g.value===e.modelValue,T=()=>{S||(a.showPopup=!1,g.value!==e.modelValue&&(t("update:modelValue",g.value),t("change",g.value)))},O=()=>{if(y)return f(xe,{class:oi("icon"),color:S?void 0:x,name:"success"},null)};return f(Yt,{role:"menuitem",key:String(g.value),icon:g.icon,title:g.text,class:oi("option",{active:y,disabled:S}),style:{color:y?x:""},tabindex:y?0:-1,clickable:!S,onClick:T},{value:O})},w=()=>{const{offset:g}=l,{autoLocate:x,zIndex:S,overlay:y,duration:T,direction:O,closeOnClickOverlay:C}=l.props,A=On(S);let I=g.value;if(x&&i.value){const P=Db(i.value);P&&(I-=Oe(P).top)}return O==="down"?A.top=`${I}px`:A.bottom=`${I}px`,Je(f("div",Ce({ref:i,style:A,class:oi([O]),onClick:m},o),[f(Ut,{show:a.showPopup,"onUpdate:show":P=>a.showPopup=P,role:"menu",class:oi("content"),overlay:y,overlayProps:{duration:a.transition&&!l.opened.value?T:0},position:O==="down"?"top":"bottom",duration:a.transition?T:0,lazyRender:e.lazyRender,overlayStyle:{position:"absolute"},"aria-labelledby":`${l.id}-${r.value}`,"data-allow-mismatch":"attribute",closeOnClickOverlay:C,onOpen:c,onClose:u,onOpened:d,onClosed:h},{default:()=>{var P;return[e.options.map(b),(P=n.default)==null?void 0:P.call(n)]}})]),[[at,a.showWrapper]])};return _e({state:a,toggle:v,renderTitle:p}),()=>e.teleport?f(ho,{to:e.teleport},{default:()=>[w()]}):w()}});const Ux=Z(Wx),Yx=Z(zx),Kx={gap:{type:[Number,Object],default:24},icon:String,axis:J("y"),magnetic:String,offset:{type:Object,default:()=>({x:-1,y:-1})},teleport:{type:[String,Object],default:"body"}},[qx,tu]=Y("floating-bubble");var Gx=W({name:qx,inheritAttrs:!1,props:Kx,emits:["click","update:offset","offsetChange"],setup(e,{slots:t,emit:n,attrs:o}){const a=M(),i=M({x:0,y:0,width:0,height:0}),l=B(()=>Ht(e.gap)?e.gap.x:e.gap),r=B(()=>Ht(e.gap)?e.gap.y:e.gap),s=B(()=>({top:r.value,right:Vt.value-i.value.width-l.value,bottom:Pt.value-i.value.height-r.value,left:l.value})),c=M(!1);let u=!1;const d=B(()=>{const y={},T=we(i.value.x),O=we(i.value.y);return y.transform=`translate3d(${T}, ${O}, 0)`,(c.value||!u)&&(y.transition="none"),y}),h=()=>{if(!S.value)return;const{width:y,height:T}=Oe(a.value),{offset:O}=e;i.value={x:O.x>-1?O.x:Vt.value-y-l.value,y:O.y>-1?O.y:Pt.value-T-r.value,width:y,height:T}},m=Ot();let v=0,p=0;const b=y=>{m.start(y),c.value=!0,v=i.value.x,p=i.value.y};Ye("touchmove",y=>{if(y.preventDefault(),m.move(y),e.axis!=="lock"&&!m.isTap.value){if(e.axis==="x"||e.axis==="xy"){let O=v+m.deltaX.value;O<s.value.left&&(O=s.value.left),O>s.value.right&&(O=s.value.right),i.value.x=O}if(e.axis==="y"||e.axis==="xy"){let O=p+m.deltaY.value;O<s.value.top&&(O=s.value.top),O>s.value.bottom&&(O=s.value.bottom),i.value.y=O}const T=$e(i.value,["x","y"]);n("update:offset",T)}},{target:a});const g=()=>{c.value=!1,Se(()=>{if(e.magnetic==="x"){const y=ki([s.value.left,s.value.right],i.value.x);i.value.x=y}if(e.magnetic==="y"){const y=ki([s.value.top,s.value.bottom],i.value.y);i.value.y=y}if(!m.isTap.value){const y=$e(i.value,["x","y"]);n("update:offset",y),(v!==y.x||p!==y.y)&&n("offsetChange",y)}})},x=y=>{m.isTap.value?n("click",y):y.stopPropagation()};We(()=>{h(),Se(()=>{u=!0})}),te([Vt,Pt,l,r,()=>e.offset],h,{deep:!0});const S=M(!0);return an(()=>{S.value=!0}),ln(()=>{e.teleport&&(S.value=!1)}),()=>{const y=Je(f("div",Ce({class:tu(),ref:a,onTouchstartPassive:b,onTouchend:g,onTouchcancel:g,onClickCapture:x,style:d.value},o),[t.default?t.default():f(dy,{name:e.icon,class:tu("icon")},null)]),[[at,S.value]]);return e.teleport?f(ho,{to:e.teleport},{default:()=>[y]}):y}}});const Xx=Z(Gx),Zx={height:se(0),anchors:Ne(),duration:se(.3),contentDraggable:H,lockScroll:Boolean,safeAreaInsetBottom:H},[Jx,ai]=Y("floating-panel");var Qx=W({name:Jx,props:Zx,emits:["heightChange","update:height"],setup(e,{emit:t,slots:n}){const a=M(),i=M(),l=Xr(()=>+e.height,x=>t("update:height",x)),r=B(()=>{var x,S;return{min:(x=e.anchors[0])!=null?x:100,max:(S=e.anchors[e.anchors.length-1])!=null?S:Math.round(Pt.value*.6)}}),s=B(()=>e.anchors.length>=2?e.anchors:[r.value.min,r.value.max]),c=M(!1),u=B(()=>({height:we(r.value.max),transform:`translateY(calc(100% + ${we(-l.value)}))`,transition:c.value?"none":`transform ${e.duration}s cubic-bezier(0.18, 0.89, 0.32, 1.28)`})),d=x=>{const S=Math.abs(x),{min:y,max:T}=r.value;return S>T?-(T+(S-T)*.2):S<y?-(y-(y-S)*.2):x};let h,m=-1;const v=Ot(),p=x=>{v.start(x),c.value=!0,h=-l.value,m=-1},b=x=>{var S;v.move(x);const y=x.target;if(i.value===y||(S=i.value)!=null&&S.contains(y)){const{scrollTop:O}=i.value;if(m=Math.max(m,O),!e.contentDraggable)return;if(-h<r.value.max)Ve(x,!0);else if(!(O<=0&&v.deltaY.value>0)||m>0)return}const T=v.deltaY.value+h;l.value=-d(T)},w=()=>{m=-1,c.value=!1,l.value=ki(s.value,l.value),l.value!==-h&&t("heightChange",{height:l.value})};te(r,()=>{l.value=ki(s.value,l.value)},{immediate:!0}),xf(a,()=>e.lockScroll||c.value),Ye("touchmove",b,{target:a});const g=()=>n.header?n.header():f("div",{class:ai("header")},[f("div",{class:ai("header-bar")},null)]);return()=>{var x;return f("div",{class:[ai(),{"van-safe-area-bottom":e.safeAreaInsetBottom}],ref:a,style:u.value,onTouchstartPassive:p,onTouchend:w,onTouchcancel:w},[g(),f("div",{class:ai("content"),ref:i},[(x=n.default)==null?void 0:x.call(n)])])}}});const eS=Z(Qx),[ch,tS]=Y("grid"),nS={square:Boolean,center:H,border:H,gutter:G,reverse:Boolean,iconSize:G,direction:String,clickable:Boolean,columnNum:se(4)},uh=Symbol(ch);var oS=W({name:ch,props:nS,setup(e,{slots:t}){const{linkChildren:n}=dt(uh);return n({props:e}),()=>{var o;return f("div",{style:{paddingLeft:we(e.gutter)},class:[tS(),{[cf]:e.border&&!e.gutter}]},[(o=t.default)==null?void 0:o.call(t)])}}});const aS=Z(oS),[iS,ii]=Y("grid-item"),lS=he({},Jn,{dot:Boolean,text:String,icon:String,badge:G,iconColor:String,iconPrefix:String,badgeProps:Object});var rS=W({name:iS,props:lS,setup(e,{slots:t}){const{parent:n,index:o}=it(uh),a=mo();if(!n)return;const i=B(()=>{const{square:u,gutter:d,columnNum:h}=n.props,m=`${100/+h}%`,v={flexBasis:m};if(u)v.paddingTop=m;else if(d){const p=we(d);v.paddingRight=p,o.value>=+h&&(v.marginTop=p)}return v}),l=B(()=>{const{square:u,gutter:d}=n.props;if(u&&d){const h=we(d);return{right:h,bottom:h,height:"auto"}}}),r=()=>{if(t.icon)return f(vo,Ce({dot:e.dot,content:e.badge},e.badgeProps),{default:t.icon});if(e.icon)return f(xe,{dot:e.dot,name:e.icon,size:n.props.iconSize,badge:e.badge,class:ii("icon"),color:e.iconColor,badgeProps:e.badgeProps,classPrefix:e.iconPrefix},null)},s=()=>{if(t.text)return t.text();if(e.text)return f("span",{class:ii("text")},[e.text])},c=()=>t.default?t.default():[r(),s()];return()=>{const{center:u,border:d,square:h,gutter:m,reverse:v,direction:p,clickable:b}=n.props,w=[ii("content",[p,{center:u,square:h,reverse:v,clickable:b,surround:d&&m}]),{[In]:d}];return f("div",{class:[ii({square:h})],style:i.value},[f("div",{role:b?"button":void 0,class:w,style:l.value,tabindex:b?0:void 0,onClick:a},[c()])])}}});const sS=Z(rS),[cS,nu]=Y("highlight"),uS={autoEscape:H,caseSensitive:Boolean,highlightClass:String,highlightTag:J("span"),keywords:Ze([String,Array]),sourceString:J(""),tag:J("div"),unhighlightClass:String,unhighlightTag:J("span")};var dS=W({name:cS,props:uS,setup(e){const t=B(()=>{const{autoEscape:o,caseSensitive:a,keywords:i,sourceString:l}=e,r=a?"g":"gi";let c=(Array.isArray(i)?i:[i]).filter(d=>d).reduce((d,h)=>{o&&(h=h.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"));const m=new RegExp(h,r);let v;for(;v=m.exec(l);){const p=v.index,b=m.lastIndex;if(p>=b){m.lastIndex++;continue}d.push({start:p,end:b,highlight:!0})}return d},[]);c=c.sort((d,h)=>d.start-h.start).reduce((d,h)=>{const m=d[d.length-1];if(!m||h.start>m.end){const v=m?m.end:0,p=h.start;v!==p&&d.push({start:v,end:p,highlight:!1}),d.push(h)}else m.end=Math.max(m.end,h.end);return d},[]);const u=c[c.length-1];return u||c.push({start:0,end:l.length,highlight:!1}),u&&u.end<l.length&&c.push({start:u.end,end:l.length,highlight:!1}),c}),n=()=>{const{sourceString:o,highlightClass:a,unhighlightClass:i,highlightTag:l,unhighlightTag:r}=e;return t.value.map(s=>{const{start:c,end:u,highlight:d}=s,h=o.slice(c,u);return d?f(l,{class:[nu("tag"),a]},{default:()=>[h]}):f(r,{class:i},{default:()=>[h]})})};return()=>{const{tag:o}=e;return f(o,{class:nu()},{default:()=>[n()]})}}});const fS=Z(dS),ou=e=>Math.sqrt((e[0].clientX-e[1].clientX)**2+(e[0].clientY-e[1].clientY)**2),hS=e=>({x:(e[0].clientX+e[1].clientX)/2,y:(e[0].clientY+e[1].clientY)/2}),Bl=Y("image-preview")[1],au=2.6,mS={src:String,show:Boolean,active:Number,minZoom:Ze(G),maxZoom:Ze(G),rootWidth:Ze(Number),rootHeight:Ze(Number),disableZoom:Boolean,doubleScale:Boolean,closeOnClickImage:Boolean,closeOnClickOverlay:Boolean,vertical:Boolean};var vS=W({props:mS,emits:["scale","close","longPress"],setup(e,{emit:t,slots:n}){const o=je({scale:1,moveX:0,moveY:0,moving:!1,zooming:!1,initializing:!1,imageRatio:0}),a=Ot(),i=M(),l=M(),r=M(!1),s=M(!1);let c=0;const u=B(()=>{const{scale:F,moveX:ne,moveY:oe,moving:Te,zooming:Ee,initializing:re}=o,N={transitionDuration:Ee||Te||re?"0s":".3s"};return(F!==1||s.value)&&(N.transform=`matrix(${F}, 0, 0, ${F}, ${ne}, ${oe})`),N}),d=B(()=>{if(o.imageRatio){const{rootWidth:F,rootHeight:ne}=e,oe=r.value?ne/o.imageRatio:F;return Math.max(0,(o.scale*oe-F)/2)}return 0}),h=B(()=>{if(o.imageRatio){const{rootWidth:F,rootHeight:ne}=e,oe=r.value?ne:F*o.imageRatio;return Math.max(0,(o.scale*oe-ne)/2)}return 0}),m=(F,ne)=>{var oe;if(F=Xe(F,+e.minZoom,+e.maxZoom+1),F!==o.scale){const Te=F/o.scale;if(o.scale=F,ne){const Ee=Oe((oe=i.value)==null?void 0:oe.$el),re={x:Ee.width*.5,y:Ee.height*.5},N=o.moveX-(ne.x-Ee.left-re.x)*(Te-1),ee=o.moveY-(ne.y-Ee.top-re.y)*(Te-1);o.moveX=Xe(N,-d.value,d.value),o.moveY=Xe(ee,-h.value,h.value)}else o.moveX=0,o.moveY=s.value?c:0;t("scale",{scale:F,index:e.active})}},v=()=>{m(1)},p=()=>{const F=o.scale>1?1:2;m(F,F===2||s.value?{x:a.startX.value,y:a.startY.value}:void 0)};let b,w,g,x,S,y,T,O,C=!1;const A=F=>{const{touches:ne}=F;if(b=ne.length,b===2&&e.disableZoom)return;const{offsetX:oe}=a;a.start(F),w=o.moveX,g=o.moveY,O=Date.now(),C=!1,o.moving=b===1&&(o.scale!==1||s.value),o.zooming=b===2&&!oe.value,o.zooming&&(x=o.scale,S=ou(ne))},I=F=>{const{touches:ne}=F;if(a.move(F),o.moving){const{deltaX:oe,deltaY:Te}=a,Ee=oe.value+w,re=Te.value+g;if((e.vertical?a.isVertical()&&Math.abs(re)>h.value:a.isHorizontal()&&Math.abs(Ee)>d.value)&&!C){o.moving=!1;return}C=!0,Ve(F,!0),o.moveX=Xe(Ee,-d.value,d.value),o.moveY=Xe(re,-h.value,h.value)}if(o.zooming&&(Ve(F,!0),ne.length===2)){const oe=ou(ne),Te=x*oe/S;y=hS(ne),m(Te,y)}},P=F=>{var ne;const oe=(ne=l.value)==null?void 0:ne.$el;if(!oe)return;const Te=oe.firstElementChild,Ee=F.target===oe,re=Te==null?void 0:Te.contains(F.target);!e.closeOnClickImage&&re||!e.closeOnClickOverlay&&Ee||t("close")},k=F=>{if(b>1)return;const ne=Date.now()-O,oe=250;a.isTap.value&&(ne<oe?e.doubleScale?T?(clearTimeout(T),T=null,p()):T=setTimeout(()=>{P(F),T=null},oe):P(F):ne>ff&&t("longPress"))},D=F=>{let ne=!1;if((o.moving||o.zooming)&&(ne=!0,o.moving&&w===o.moveX&&g===o.moveY&&(ne=!1),!F.touches.length)){o.zooming&&(o.moveX=Xe(o.moveX,-d.value,d.value),o.moveY=Xe(o.moveY,-h.value,h.value),o.zooming=!1),o.moving=!1,w=0,g=0,x=1,o.scale<1&&v();const oe=+e.maxZoom;o.scale>oe&&m(oe,y)}Ve(F,ne),k(F),a.reset()},j=()=>{const{rootWidth:F,rootHeight:ne}=e,oe=ne/F,{imageRatio:Te}=o;r.value=o.imageRatio>oe&&Te<au,s.value=o.imageRatio>oe&&Te>=au,s.value&&(c=(Te*F-ne)/2,o.moveY=c,o.initializing=!0,st(()=>{o.initializing=!1})),v()},ae=F=>{const{naturalWidth:ne,naturalHeight:oe}=F.target;o.imageRatio=oe/ne,j()};return te(()=>e.active,v),te(()=>e.show,F=>{F||v()}),te(()=>[e.rootWidth,e.rootHeight],j),Ye("touchmove",I,{target:B(()=>{var F;return(F=l.value)==null?void 0:F.$el})}),_e({resetScale:v}),()=>{const F={loading:()=>f(Wt,{type:"spinner"},null)};return f(Jr,{ref:l,class:Bl("swipe-item"),onTouchstartPassive:A,onTouchend:D,onTouchcancel:D},{default:()=>[n.image?f("div",{class:Bl("image-wrap")},[n.image({src:e.src,onLoad:ae,style:u.value})]):f(il,{ref:i,src:e.src,fit:"contain",class:Bl("image",{vertical:r.value}),style:u.value,onLoad:ae},F)]})}}});const[gS,_o]=Y("image-preview"),bS=["show","teleport","transition","overlayStyle","closeOnPopstate"],yS={show:Boolean,loop:H,images:Ne(),minZoom:se(1/3),maxZoom:se(3),overlay:H,vertical:Boolean,closeable:Boolean,showIndex:H,className:ze,closeIcon:J("clear"),transition:String,beforeClose:Function,doubleScale:H,overlayClass:ze,overlayStyle:Object,swipeDuration:se(300),startPosition:se(0),showIndicators:Boolean,closeOnPopstate:H,closeOnClickImage:H,closeOnClickOverlay:H,closeIconPosition:J("top-right"),teleport:[String,Object]};var dh=W({name:gS,props:yS,emits:["scale","close","closed","change","longPress","update:show"],setup(e,{emit:t,slots:n}){const o=M(),a=M(),i=je({active:0,rootWidth:0,rootHeight:0,disableZoom:!1}),l=()=>{if(o.value){const x=Oe(o.value.$el);i.rootWidth=x.width,i.rootHeight=x.height,o.value.resize()}},r=x=>t("scale",x),s=x=>t("update:show",x),c=()=>{Zn(e.beforeClose,{args:[i.active],done:()=>s(!1)})},u=x=>{x!==i.active&&(i.active=x,t("change",x))},d=()=>{if(e.showIndex)return f("div",{class:_o("index")},[n.index?n.index({index:i.active}):`${i.active+1} / ${e.images.length}`])},h=()=>{if(n.cover)return f("div",{class:_o("cover")},[n.cover()])},m=()=>{i.disableZoom=!0},v=()=>{i.disableZoom=!1},p=()=>f(Zr,{ref:o,lazyRender:!0,loop:e.loop,class:_o("swipe"),vertical:e.vertical,duration:e.swipeDuration,initialSwipe:e.startPosition,showIndicators:e.showIndicators,indicatorColor:"white",onChange:u,onDragEnd:v,onDragStart:m},{default:()=>[e.images.map((x,S)=>f(vS,{ref:y=>{S===i.active&&(a.value=y)},src:x,show:e.show,active:i.active,maxZoom:e.maxZoom,minZoom:e.minZoom,rootWidth:i.rootWidth,rootHeight:i.rootHeight,disableZoom:i.disableZoom,doubleScale:e.doubleScale,closeOnClickImage:e.closeOnClickImage,closeOnClickOverlay:e.closeOnClickOverlay,vertical:e.vertical,onScale:r,onClose:c,onLongPress:()=>t("longPress",{index:S})},{image:n.image}))]}),b=()=>{if(e.closeable)return f(xe,{role:"button",name:e.closeIcon,class:[_o("close-icon",e.closeIconPosition),ct],onClick:c},null)},w=()=>t("closed"),g=(x,S)=>{var y;return(y=o.value)==null?void 0:y.swipeTo(x,S)};return _e({resetScale:()=>{var x;(x=a.value)==null||x.resetScale()},swipeTo:g}),We(l),te([Vt,Pt],l),te(()=>e.startPosition,x=>u(+x)),te(()=>e.show,x=>{const{images:S,startPosition:y}=e;x?(u(+y),Se(()=>{l(),g(+y,{immediate:!0})})):t("close",{index:i.active,url:S[i.active]})}),()=>f(Ut,Ce({class:[_o(),e.className],overlayClass:[_o("overlay"),e.overlayClass],onClosed:w,"onUpdate:show":s},$e(e,bS)),{default:()=>[b(),p(),d(),h()]})}});let mi;const pS={loop:!0,images:[],maxZoom:3,minZoom:1/3,onScale:void 0,onClose:void 0,onChange:void 0,vertical:!1,teleport:"body",className:"",showIndex:!0,closeable:!1,closeIcon:"clear",transition:void 0,beforeClose:void 0,doubleScale:!0,overlayStyle:void 0,overlayClass:void 0,startPosition:0,swipeDuration:300,showIndicators:!1,closeOnPopstate:!0,closeOnClickOverlay:!0,closeIconPosition:"top-right"};function wS(){({instance:mi}=Hf({setup(){const{state:e,toggle:t}=zf(),n=()=>{e.images=[]};return()=>f(dh,Ce(e,{onClosed:n,"onUpdate:show":t}),null)}}))}const xS=(e,t=0)=>{if(At)return mi||wS(),e=Array.isArray(e)?{images:e,startPosition:t}:e,mi.open(he({},pS,e)),mi},SS=Z(dh);function CS(){return Array(26).fill("").map((n,o)=>String.fromCharCode(65+o))}const[fh,Ml]=Y("index-bar"),TS={sticky:H,zIndex:G,teleport:[String,Object],highlightColor:String,stickyOffsetTop:Ge(0),indexList:{type:Array,default:CS}},hh=Symbol(fh);var _S=W({name:fh,props:TS,emits:["select","change"],setup(e,{emit:t,slots:n}){const o=M(),a=M(),i=M(""),l=Ot(),r=qo(o),{children:s,linkChildren:c}=dt(hh);let u;c({props:e});const d=B(()=>{if(ke(e.zIndex))return{zIndex:+e.zIndex+1}}),h=B(()=>{if(e.highlightColor)return{color:e.highlightColor}}),m=(C,A)=>{for(let I=s.length-1;I>=0;I--){const P=I>0?A[I-1].height:0,k=e.sticky?P+e.stickyOffsetTop:0;if(C+k>=A[I].top)return I}return-1},v=C=>s.find(A=>String(A.index)===C),p=()=>{if(fo(o))return;const{sticky:C,indexList:A}=e,I=Tn(r.value),P=Oe(r),k=s.map(j=>j.getRect(r.value,P));let D=-1;if(u){const j=v(u);if(j){const ae=j.getRect(r.value,P);e.sticky&&e.stickyOffsetTop?D=m(ae.top-e.stickyOffsetTop,k):D=m(ae.top,k)}}else D=m(I,k);i.value=A[D],C&&s.forEach((j,ae)=>{const{state:F,$el:ne}=j;if(ae===D||ae===D-1){const oe=ne.getBoundingClientRect();F.left=oe.left,F.width=oe.width}else F.left=null,F.width=null;if(ae===D)F.active=!0,F.top=Math.max(e.stickyOffsetTop,k[ae].top-I)+P.top;else if(ae===D-1&&u===""){const oe=k[D].top-I;F.active=oe>0,F.top=oe+P.top-k[ae].height}else F.active=!1}),u=""},b=()=>{Se(p)};Ye("scroll",p,{target:r,passive:!0}),We(b),te(()=>e.indexList,b),te(i,C=>{C&&t("change",C)});const w=()=>e.indexList.map(C=>{const A=C===i.value;return f("span",{class:Ml("index",{active:A}),style:A?h.value:void 0,"data-index":C},[C])}),g=C=>{u=String(C);const A=v(u);if(A){const I=Tn(r.value),P=Oe(r),{offsetHeight:k}=document.documentElement;if(A.$el.scrollIntoView(),I===k-P.height){p();return}e.sticky&&e.stickyOffsetTop&&(co()===k-P.height?Ra(co()):Ra(co()-e.stickyOffsetTop)),t("select",A.index)}},x=C=>{const{index:A}=C.dataset;A&&g(A)},S=C=>{x(C.target)};let y;const T=C=>{if(l.move(C),l.isVertical()){Ve(C);const{clientX:A,clientY:I}=C.touches[0],P=document.elementFromPoint(A,I);if(P){const{index:k}=P.dataset;k&&y!==k&&(y=k,x(P))}}},O=()=>f("div",{ref:a,class:Ml("sidebar"),style:d.value,onClick:S,onTouchstartPassive:l.start},[w()]);return _e({scrollTo:g}),Ye("touchmove",T,{target:a}),()=>{var C;return f("div",{ref:o,class:Ml()},[e.teleport?f(ho,{to:e.teleport},{default:()=>[O()]}):O(),(C=n.default)==null?void 0:C.call(n)])}}});const[kS,ES]=Y("index-anchor"),PS={index:G};var $S=W({name:kS,props:PS,setup(e,{slots:t}){const n=je({top:0,left:null,rect:{top:0,height:0},width:null,active:!1}),o=M(),{parent:a}=it(hh);if(!a)return;const i=()=>n.active&&a.props.sticky,l=B(()=>{const{zIndex:s,highlightColor:c}=a.props;if(i())return he(On(s),{left:n.left?`${n.left}px`:void 0,width:n.width?`${n.width}px`:void 0,transform:n.top?`translate3d(0, ${n.top}px, 0)`:void 0,color:c})});return _e({state:n,getRect:(s,c)=>{const u=Oe(o);return n.rect.height=u.height,s===window||s===document.body?n.rect.top=u.top+co():n.rect.top=u.top+Tn(s)-c.top,n.rect}}),()=>{const s=i();return f("div",{ref:o,style:{height:s?`${n.rect.height}px`:void 0}},[f("div",{style:l.value,class:[ES({sticky:s}),{[Yr]:s}]},[t.default?t.default():e.index])])}}});const AS=Z($S),OS=Z(_S),[IS,ko,RS]=Y("list"),DS={error:Boolean,offset:se(300),loading:Boolean,disabled:Boolean,finished:Boolean,scroller:Object,errorText:String,direction:J("down"),loadingText:String,finishedText:String,immediateCheck:H};var BS=W({name:IS,props:DS,emits:["load","update:error","update:loading"],setup(e,{emit:t,slots:n}){const o=M(e.loading),a=M(),i=M(),l=c0(),r=qo(a),s=B(()=>e.scroller||r.value),c=()=>{Se(()=>{if(o.value||e.finished||e.disabled||e.error||(l==null?void 0:l.value)===!1)return;const{direction:v}=e,p=+e.offset,b=Oe(s);if(!b.height||fo(a))return;let w=!1;const g=Oe(i);v==="up"?w=b.top-g.top<=p:w=g.bottom-b.bottom<=p,w&&(o.value=!0,t("update:loading",!0),t("load"))})},u=()=>{if(e.finished){const v=n.finished?n.finished():e.finishedText;if(v)return f("div",{class:ko("finished-text")},[v])}},d=()=>{t("update:error",!1),c()},h=()=>{if(e.error){const v=n.error?n.error():e.errorText;if(v)return f("div",{role:"button",class:ko("error-text"),tabindex:0,onClick:d},[v])}},m=()=>{if(o.value&&!e.finished&&!e.disabled)return f("div",{class:ko("loading")},[n.loading?n.loading():f(Wt,{class:ko("loading-icon")},{default:()=>[e.loadingText||RS("loading")]})])};return te(()=>[e.loading,e.finished,e.error],c),l&&te(l,v=>{v&&c()}),hd(()=>{o.value=e.loading}),We(()=>{e.immediateCheck&&c()}),_e({check:c}),Ye("scroll",c,{target:s,passive:!0}),()=>{var v;const p=(v=n.default)==null?void 0:v.call(n),b=f("div",{ref:i,class:ko("placeholder")},null);return f("div",{ref:a,role:"feed",class:ko(),"aria-busy":o.value},[e.direction==="down"?p:b,m(),u(),h(),e.direction==="up"?p:b])}}});const MS=Z(BS),[LS,mn]=Y("nav-bar"),VS={title:String,fixed:Boolean,zIndex:G,border:H,leftText:String,rightText:String,leftDisabled:Boolean,rightDisabled:Boolean,leftArrow:Boolean,placeholder:Boolean,safeAreaInsetTop:Boolean,clickable:H};var FS=W({name:LS,props:VS,emits:["clickLeft","clickRight"],setup(e,{emit:t,slots:n}){const o=M(),a=Ji(o,mn),i=u=>{e.leftDisabled||t("clickLeft",u)},l=u=>{e.rightDisabled||t("clickRight",u)},r=()=>n.left?n.left():[e.leftArrow&&f(xe,{class:mn("arrow"),name:"arrow-left"},null),e.leftText&&f("span",{class:mn("text")},[e.leftText])],s=()=>n.right?n.right():f("span",{class:mn("text")},[e.rightText]),c=()=>{const{title:u,fixed:d,border:h,zIndex:m}=e,v=On(m),p=e.leftArrow||e.leftText||n.left,b=e.rightText||n.right;return f("div",{ref:o,style:v,class:[mn({fixed:d}),{[Yr]:h,"van-safe-area-top":e.safeAreaInsetTop}]},[f("div",{class:mn("content")},[p&&f("div",{class:[mn("left",{disabled:e.leftDisabled}),e.clickable&&!e.leftDisabled?ct:""],onClick:i},[r()]),f("div",{class:[mn("title"),"van-ellipsis"]},[n.title?n.title():u]),b&&f("div",{class:[mn("right",{disabled:e.rightDisabled}),e.clickable&&!e.rightDisabled?ct:""],onClick:l},[s()])])])};return()=>e.fixed&&e.placeholder?a(c):c()}});const NS=Z(FS),[zS,aa]=Y("notice-bar"),HS={text:String,mode:String,color:String,delay:se(1),speed:se(60),leftIcon:String,wrapable:Boolean,background:String,scrollable:{type:Boolean,default:null}};var jS=W({name:zS,props:HS,emits:["close","replay"],setup(e,{emit:t,slots:n}){let o=0,a=0,i;const l=M(),r=M(),s=je({show:!0,offset:0,duration:0}),c=()=>{if(n["left-icon"])return n["left-icon"]();if(e.leftIcon)return f(xe,{class:aa("left-icon"),name:e.leftIcon},null)},u=()=>{if(e.mode==="closeable")return"cross";if(e.mode==="link")return"arrow"},d=b=>{e.mode==="closeable"&&(s.show=!1,t("close",b))},h=()=>{if(n["right-icon"])return n["right-icon"]();const b=u();if(b)return f(xe,{name:b,class:aa("right-icon"),onClick:d},null)},m=()=>{s.offset=o,s.duration=0,st(()=>{Wn(()=>{s.offset=-a,s.duration=(a+o)/+e.speed,t("replay")})})},v=()=>{const b=e.scrollable===!1&&!e.wrapable,w={transform:s.offset?`translateX(${s.offset}px)`:"",transitionDuration:`${s.duration}s`};return f("div",{ref:l,role:"marquee",class:aa("wrap")},[f("div",{ref:r,style:w,class:[aa("content"),{"van-ellipsis":b}],onTransitionend:m},[n.default?n.default():e.text])])},p=()=>{const{delay:b,speed:w,scrollable:g}=e,x=ke(b)?+b*1e3:0;o=0,a=0,s.offset=0,s.duration=0,clearTimeout(i),i=setTimeout(()=>{if(!l.value||!r.value||g===!1)return;const S=Oe(l).width,y=Oe(r).width;(g||y>S)&&Wn(()=>{o=S,a=y,s.offset=-a,s.duration=a/+w})},x)};return Zi(p),Ko(p),Ye("pageshow",p),_e({reset:p}),te(()=>[e.text,e.scrollable],p),()=>{const{color:b,wrapable:w,background:g}=e;return Je(f("div",{role:"alert",class:aa({wrapable:w}),style:{color:b,background:g}},[c(),v(),h()]),[[at,s.show]])}}});const WS=Z(jS),[US,YS]=Y("notify"),KS=["lockScroll","position","show","teleport","zIndex"],qS=he({},Go,{type:J("danger"),color:String,message:G,position:J("top"),className:ze,background:String,lockScroll:Boolean});var GS=W({name:US,props:qS,emits:["update:show"],setup(e,{emit:t,slots:n}){const o=a=>t("update:show",a);return()=>f(Ut,Ce({class:[YS([e.type]),e.className],style:{color:e.color,background:e.background},overlay:!1,duration:.2,"onUpdate:show":o},$e(e,KS)),{default:()=>[n.default?n.default():e.message]})}});const XS=Z(GS),[ZS,xa]=Y("key"),JS=f("svg",{class:xa("collapse-icon"),viewBox:"0 0 30 24"},[f("path",{d:"M26 13h-2v2h2v-2zm-8-3h2V8h-2v2zm2-4h2V4h-2v2zm2 4h4V4h-2v4h-2v2zm-7 14 3-3h-6l3 3zM6 13H4v2h2v-2zm16 0H8v2h14v-2zm-12-3h2V8h-2v2zM28 0l1 1 1 1v15l-1 2H1l-1-2V2l1-1 1-1zm0 2H2v15h26V2zM6 4v2H4V4zm10 2h2V4h-2v2zM8 9v1H4V8zm8 0v1h-2V8zm-6-5v2H8V4zm4 0v2h-2V4z",fill:"currentColor"},null)]),QS=f("svg",{class:xa("delete-icon"),viewBox:"0 0 32 22"},[f("path",{d:"M28 0a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H10.4a2 2 0 0 1-1.4-.6L1 13.1c-.6-.5-.9-1.3-.9-2 0-1 .3-1.7.9-2.2L9 .6a2 2 0 0 1 1.4-.6zm0 2H10.4l-8.2 8.3a1 1 0 0 0-.3.7c0 .3.1.5.3.7l8.2 8.4H28a2 2 0 0 0 2-2V4c0-1.1-.9-2-2-2zm-5 4a1 1 0 0 1 .7.3 1 1 0 0 1 0 1.4L20.4 11l3.3 3.3c.2.2.3.5.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3L19 12.4l-3.4 3.3a1 1 0 0 1-.6.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.2.1-.5.3-.7l3.3-3.3-3.3-3.3A1 1 0 0 1 14 7c0-.3.1-.5.3-.7A1 1 0 0 1 15 6a1 1 0 0 1 .6.3L19 9.6l3.3-3.3A1 1 0 0 1 23 6z",fill:"currentColor"},null)]);var Ll=W({name:ZS,props:{type:String,text:G,color:String,wider:Boolean,large:Boolean,loading:Boolean},emits:["press"],setup(e,{emit:t,slots:n}){const o=M(!1),a=Ot(),i=c=>{a.start(c),o.value=!0},l=c=>{a.move(c),a.direction.value&&(o.value=!1)},r=c=>{o.value&&(n.default||Ve(c),o.value=!1,t("press",e.text,e.type))},s=()=>{if(e.loading)return f(Wt,{class:xa("loading-icon")},null);const c=n.default?n.default():e.text;switch(e.type){case"delete":return c||QS;case"extra":return c||JS;default:return c}};return()=>f("div",{class:xa("wrapper",{wider:e.wider}),onTouchstartPassive:i,onTouchmovePassive:l,onTouchend:r,onTouchcancel:r},[f("div",{role:"button",tabindex:0,class:xa([e.color,{large:e.large,active:o.value,delete:e.type==="delete"}])},[s()])])}});const[eC,Ln]=Y("number-keyboard"),tC={show:Boolean,title:String,theme:J("default"),zIndex:G,teleport:[String,Object],maxlength:se(1/0),modelValue:J(""),transition:H,blurOnClose:H,showDeleteKey:H,randomKeyOrder:Boolean,closeButtonText:String,deleteButtonText:String,closeButtonLoading:Boolean,hideOnClickOutside:H,safeAreaInsetBottom:H,extraKey:{type:[String,Array],default:""}};function nC(e){for(let t=e.length-1;t>0;t--){const n=Math.floor(Math.random()*(t+1)),o=e[t];e[t]=e[n],e[n]=o}return e}var oC=W({name:eC,inheritAttrs:!1,props:tC,emits:["show","hide","blur","input","close","delete","update:modelValue"],setup(e,{emit:t,slots:n,attrs:o}){const a=M(),i=()=>{const b=Array(9).fill("").map((w,g)=>({text:g+1}));return e.randomKeyOrder&&nC(b),b},l=()=>[...i(),{text:e.extraKey,type:"extra"},{text:0},{text:e.showDeleteKey?e.deleteButtonText:"",type:e.showDeleteKey?"delete":""}],r=()=>{const b=i(),{extraKey:w}=e,g=Array.isArray(w)?w:[w];return g.length===0?b.push({text:0,wider:!0}):g.length===1?b.push({text:0,wider:!0},{text:g[0],type:"extra"}):g.length===2&&b.push({text:g[0],type:"extra"},{text:0},{text:g[1],type:"extra"}),b},s=B(()=>e.theme==="custom"?r():l()),c=()=>{e.show&&t("blur")},u=()=>{t("close"),e.blurOnClose&&c()},d=()=>t(e.show?"show":"hide"),h=(b,w)=>{if(b===""){w==="extra"&&c();return}const g=e.modelValue;w==="delete"?(t("delete"),t("update:modelValue",g.slice(0,g.length-1))):w==="close"?u():g.length<+e.maxlength&&(t("input",b),t("update:modelValue",g+b))},m=()=>{const{title:b,theme:w,closeButtonText:g}=e,x=n["title-left"],S=g&&w==="default";if(b||S||x)return f("div",{class:Ln("header")},[x&&f("span",{class:Ln("title-left")},[x()]),b&&f("h2",{class:Ln("title")},[b]),S&&f("button",{type:"button",class:[Ln("close"),ct],onClick:u},[g])])},v=()=>s.value.map(b=>{const w={};return b.type==="delete"&&(w.default=n.delete),b.type==="extra"&&(w.default=n["extra-key"]),f(Ll,{key:b.text,text:b.text,type:b.type,wider:b.wider,color:b.color,onPress:h},w)}),p=()=>{if(e.theme==="custom")return f("div",{class:Ln("sidebar")},[e.showDeleteKey&&f(Ll,{large:!0,text:e.deleteButtonText,type:"delete",onPress:h},{default:n.delete}),f(Ll,{large:!0,text:e.closeButtonText,type:"close",color:"blue",loading:e.closeButtonLoading,onPress:h},null)])};return te(()=>e.show,b=>{e.transition||t(b?"show":"hide")}),e.hideOnClickOutside&&Gi(a,c,{eventName:"touchstart"}),()=>{const b=m(),w=f(Ui,{name:e.transition?"van-slide-up":""},{default:()=>[Je(f("div",Ce({ref:a,style:On(e.zIndex),class:Ln({unfit:!e.safeAreaInsetBottom,"with-title":!!b}),onAnimationend:d,onTouchstartPassive:Wr},o),[b,f("div",{class:Ln("body")},[f("div",{class:Ln("keys")},[v()]),p()])]),[[at,e.show]])]});return e.teleport?f(ho,{to:e.teleport},{default:()=>[w]}):w}}});const aC=Z(oC),[iC,Eo,iu]=Y("pagination"),Vl=(e,t,n)=>({number:e,text:t,active:n}),lC={mode:J("multi"),prevText:String,nextText:String,pageCount:se(0),modelValue:Ge(0),totalItems:se(0),showPageSize:se(5),itemsPerPage:se(10),forceEllipses:Boolean,showPrevButton:H,showNextButton:H};var rC=W({name:iC,props:lC,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=B(()=>{const{pageCount:u,totalItems:d,itemsPerPage:h}=e,m=+u||Math.ceil(+d/+h);return Math.max(1,m)}),a=B(()=>{const u=[],d=o.value,h=+e.showPageSize,{modelValue:m,forceEllipses:v}=e;let p=1,b=d;const w=h<d;w&&(p=Math.max(m-Math.floor(h/2),1),b=p+h-1,b>d&&(b=d,p=b-h+1));for(let g=p;g<=b;g++){const x=Vl(g,g,g===m);u.push(x)}if(w&&h>0&&v){if(p>1){const g=Vl(p-1,"...");u.unshift(g)}if(b<d){const g=Vl(b+1,"...");u.push(g)}}return u}),i=(u,d)=>{u=Xe(u,1,o.value),e.modelValue!==u&&(t("update:modelValue",u),d&&t("change",u))};Yo(()=>i(e.modelValue));const l=()=>f("li",{class:Eo("page-desc")},[n.pageDesc?n.pageDesc():`${e.modelValue}/${o.value}`]),r=()=>{const{mode:u,modelValue:d,showPrevButton:h}=e;if(!h)return;const m=n["prev-text"],v=d===1;return f("li",{class:[Eo("item",{disabled:v,border:u==="simple",prev:!0}),pa]},[f("button",{type:"button",disabled:v,onClick:()=>i(d-1,!0)},[m?m():e.prevText||iu("prev")])])},s=()=>{const{mode:u,modelValue:d,showNextButton:h}=e;if(!h)return;const m=n["next-text"],v=d===o.value;return f("li",{class:[Eo("item",{disabled:v,border:u==="simple",next:!0}),pa]},[f("button",{type:"button",disabled:v,onClick:()=>i(d+1,!0)},[m?m():e.nextText||iu("next")])])},c=()=>a.value.map(u=>f("li",{class:[Eo("item",{active:u.active,page:!0}),pa]},[f("button",{type:"button","aria-current":u.active||void 0,onClick:()=>i(u.number,!0)},[n.page?n.page(u):u.text])]));return()=>f("nav",{role:"navigation",class:Eo()},[f("ul",{class:Eo("items")},[r(),e.mode==="simple"?l():c(),s()])])}});const sC=Z(rC),[cC,ia]=Y("password-input"),uC={info:String,mask:H,value:J(""),gutter:G,length:se(6),focused:Boolean,errorInfo:String};var dC=W({name:cC,props:uC,emits:["focus"],setup(e,{emit:t}){const n=a=>{a.stopPropagation(),t("focus",a)},o=()=>{const a=[],{mask:i,value:l,gutter:r,focused:s}=e,c=+e.length;for(let u=0;u<c;u++){const d=l[u],h=u!==0&&!r,m=s&&u===l.length;let v;u!==0&&r&&(v={marginLeft:we(r)}),a.push(f("li",{class:[{[uf]:h},ia("item",{focus:m})],style:v},[i?f("i",{style:{visibility:d?"visible":"hidden"}},null):d,m&&f("div",{class:ia("cursor")},null)]))}return a};return()=>{const a=e.errorInfo||e.info;return f("div",{class:ia()},[f("ul",{class:[ia("security"),{[pa]:!e.gutter}],onTouchstartPassive:n},[o()]),a&&f("div",{class:ia(e.errorInfo?"error-info":"info")},[a])])}}});const fC=Z(dC),hC=Z(p0);function Kt(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function ss(e){var t=Kt(e).Element;return e instanceof t||e instanceof Element}function Ft(e){var t=Kt(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function mh(e){if(typeof ShadowRoot>"u")return!1;var t=Kt(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var Wo=Math.round;function pr(){var e=navigator.userAgentData;return e!=null&&e.brands?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function mC(){return!/^((?!chrome|android).)*safari/i.test(pr())}function $i(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),a=1,i=1;t&&Ft(e)&&(a=e.offsetWidth>0&&Wo(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Wo(o.height)/e.offsetHeight||1);var l=ss(e)?Kt(e):window,r=l.visualViewport,s=!mC()&&n,c=(o.left+(s&&r?r.offsetLeft:0))/a,u=(o.top+(s&&r?r.offsetTop:0))/i,d=o.width/a,h=o.height/i;return{width:d,height:h,top:u,right:c+d,bottom:u+h,left:c,x:c,y:u}}function vh(e){var t=Kt(e),n=t.pageXOffset,o=t.pageYOffset;return{scrollLeft:n,scrollTop:o}}function vC(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function gC(e){return e===Kt(e)||!Ft(e)?vh(e):vC(e)}function _n(e){return e?(e.nodeName||"").toLowerCase():null}function ll(e){return((ss(e)?e.ownerDocument:e.document)||window.document).documentElement}function bC(e){return $i(ll(e)).left+vh(e).scrollLeft}function kn(e){return Kt(e).getComputedStyle(e)}function cs(e){var t=kn(e),n=t.overflow,o=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+o)}function yC(e){var t=e.getBoundingClientRect(),n=Wo(t.width)/e.offsetWidth||1,o=Wo(t.height)/e.offsetHeight||1;return n!==1||o!==1}function pC(e,t,n){n===void 0&&(n=!1);var o=Ft(t),a=Ft(t)&&yC(t),i=ll(t),l=$i(e,a,n),r={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(o||!o&&!n)&&((_n(t)!=="body"||cs(i))&&(r=gC(t)),Ft(t)?(s=$i(t,!0),s.x+=t.clientLeft,s.y+=t.clientTop):i&&(s.x=bC(i))),{x:l.left+r.scrollLeft-s.x,y:l.top+r.scrollTop-s.y,width:l.width,height:l.height}}function wC(e){var t=$i(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function us(e){return _n(e)==="html"?e:e.assignedSlot||e.parentNode||(mh(e)?e.host:null)||ll(e)}function gh(e){return["html","body","#document"].indexOf(_n(e))>=0?e.ownerDocument.body:Ft(e)&&cs(e)?e:gh(us(e))}function vi(e,t){var n;t===void 0&&(t=[]);var o=gh(e),a=o===((n=e.ownerDocument)==null?void 0:n.body),i=Kt(o),l=a?[i].concat(i.visualViewport||[],cs(o)?o:[]):o,r=t.concat(l);return a?r:r.concat(vi(us(l)))}function xC(e){return["table","td","th"].indexOf(_n(e))>=0}function lu(e){return!Ft(e)||kn(e).position==="fixed"?null:e.offsetParent}function SC(e){var t=/firefox/i.test(pr()),n=/Trident/i.test(pr());if(n&&Ft(e)){var o=kn(e);if(o.position==="fixed")return null}var a=us(e);for(mh(a)&&(a=a.host);Ft(a)&&["html","body"].indexOf(_n(a))<0;){var i=kn(a);if(i.transform!=="none"||i.perspective!=="none"||i.contain==="paint"||["transform","perspective"].indexOf(i.willChange)!==-1||t&&i.willChange==="filter"||t&&i.filter&&i.filter!=="none")return a;a=a.parentNode}return null}function bh(e){for(var t=Kt(e),n=lu(e);n&&xC(n)&&kn(n).position==="static";)n=lu(n);return n&&(_n(n)==="html"||_n(n)==="body"&&kn(n).position==="static")?t:n||SC(e)||t}var Fo="top",Ai="bottom",Ba="right",uo="left",yh="auto",CC=[Fo,Ai,Ba,uo],ph="start",Oi="end",TC=[].concat(CC,[yh]).reduce(function(e,t){return e.concat([t,t+"-"+ph,t+"-"+Oi])},[]),_C="beforeRead",kC="read",EC="afterRead",PC="beforeMain",$C="main",AC="afterMain",OC="beforeWrite",IC="write",RC="afterWrite",wr=[_C,kC,EC,PC,$C,AC,OC,IC,RC];function DC(e){var t=new Map,n=new Set,o=[];e.forEach(function(i){t.set(i.name,i)});function a(i){n.add(i.name);var l=[].concat(i.requires||[],i.requiresIfExists||[]);l.forEach(function(r){if(!n.has(r)){var s=t.get(r);s&&a(s)}}),o.push(i)}return e.forEach(function(i){n.has(i.name)||a(i)}),o}function BC(e){var t=DC(e);return wr.reduce(function(n,o){return n.concat(t.filter(function(a){return a.phase===o}))},[])}function MC(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Vn(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return[].concat(n).reduce(function(a,i){return a.replace(/%s/,i)},e)}var oo='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',LC='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',ru=["name","enabled","phase","fn","effect","requires","options"];function VC(e){e.forEach(function(t){[].concat(Object.keys(t),ru).filter(function(n,o,a){return a.indexOf(n)===o}).forEach(function(n){switch(n){case"name":typeof t.name!="string"&&console.error(Vn(oo,String(t.name),'"name"','"string"','"'+String(t.name)+'"'));break;case"enabled":typeof t.enabled!="boolean"&&console.error(Vn(oo,t.name,'"enabled"','"boolean"','"'+String(t.enabled)+'"'));break;case"phase":wr.indexOf(t.phase)<0&&console.error(Vn(oo,t.name,'"phase"',"either "+wr.join(", "),'"'+String(t.phase)+'"'));break;case"fn":typeof t.fn!="function"&&console.error(Vn(oo,t.name,'"fn"','"function"','"'+String(t.fn)+'"'));break;case"effect":t.effect!=null&&typeof t.effect!="function"&&console.error(Vn(oo,t.name,'"effect"','"function"','"'+String(t.fn)+'"'));break;case"requires":t.requires!=null&&!Array.isArray(t.requires)&&console.error(Vn(oo,t.name,'"requires"','"array"','"'+String(t.requires)+'"'));break;case"requiresIfExists":Array.isArray(t.requiresIfExists)||console.error(Vn(oo,t.name,'"requiresIfExists"','"array"','"'+String(t.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+t.name+'" modifier, valid properties are '+ru.map(function(o){return'"'+o+'"'}).join(", ")+'; but "'+n+'" was provided.')}t.requires&&t.requires.forEach(function(o){e.find(function(a){return a.name===o})==null&&console.error(Vn(LC,String(t.name),o,o))})})})}function FC(e,t){var n=new Set;return e.filter(function(o){var a=t(o);if(!n.has(a))return n.add(a),!0})}function rl(e){return e.split("-")[0]}function NC(e){var t=e.reduce(function(n,o){var a=n[o.name];return n[o.name]=a?Object.assign({},a,o,{options:Object.assign({},a.options,o.options),data:Object.assign({},a.data,o.data)}):o,n},{});return Object.keys(t).map(function(n){return t[n]})}function wh(e){return e.split("-")[1]}function zC(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function HC(e){var t=e.reference,n=e.element,o=e.placement,a=o?rl(o):null,i=o?wh(o):null,l=t.x+t.width/2-n.width/2,r=t.y+t.height/2-n.height/2,s;switch(a){case Fo:s={x:l,y:t.y-n.height};break;case Ai:s={x:l,y:t.y+t.height};break;case Ba:s={x:t.x+t.width,y:r};break;case uo:s={x:t.x-n.width,y:r};break;default:s={x:t.x,y:t.y}}var c=a?zC(a):null;if(c!=null){var u=c==="y"?"height":"width";switch(i){case ph:s[c]=s[c]-(t[u]/2-n[u]/2);break;case Oi:s[c]=s[c]+(t[u]/2-n[u]/2);break}}return s}var su="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",jC="Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.",cu={placement:"bottom",modifiers:[],strategy:"absolute"};function uu(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function WC(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,a=t.defaultOptions,i=a===void 0?cu:a;return function(r,s,c){c===void 0&&(c=i);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},cu,i),modifiersData:{},elements:{reference:r,popper:s},attributes:{},styles:{}},d=[],h=!1,m={state:u,setOptions:function(w){var g=typeof w=="function"?w(u.options):w;p(),u.options=Object.assign({},i,u.options,g),u.scrollParents={reference:ss(r)?vi(r):r.contextElement?vi(r.contextElement):[],popper:vi(s)};var x=BC(NC([].concat(o,u.options.modifiers)));u.orderedModifiers=x.filter(function(P){return P.enabled});{var S=FC([].concat(x,u.options.modifiers),function(P){var k=P.name;return k});if(VC(S),rl(u.options.placement)===yh){var y=u.orderedModifiers.find(function(P){var k=P.name;return k==="flip"});y||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" "))}var T=kn(s),O=T.marginTop,C=T.marginRight,A=T.marginBottom,I=T.marginLeft;[O,C,A,I].some(function(P){return parseFloat(P)})&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" "))}return v(),m.update()},forceUpdate:function(){if(!h){var w=u.elements,g=w.reference,x=w.popper;if(!uu(g,x)){console.error(su);return}u.rects={reference:pC(g,bh(x),u.options.strategy==="fixed"),popper:wC(x)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(P){return u.modifiersData[P.name]=Object.assign({},P.data)});for(var S=0,y=0;y<u.orderedModifiers.length;y++){if(S+=1,S>100){console.error(jC);break}if(u.reset===!0){u.reset=!1,y=-1;continue}var T=u.orderedModifiers[y],O=T.fn,C=T.options,A=C===void 0?{}:C,I=T.name;typeof O=="function"&&(u=O({state:u,options:A,name:I,instance:m})||u)}}},update:MC(function(){return new Promise(function(b){m.forceUpdate(),b(u)})}),destroy:function(){p(),h=!0}};if(!uu(r,s))return console.error(su),m;m.setOptions(c).then(function(b){!h&&c.onFirstUpdate&&c.onFirstUpdate(b)});function v(){u.orderedModifiers.forEach(function(b){var w=b.name,g=b.options,x=g===void 0?{}:g,S=b.effect;if(typeof S=="function"){var y=S({state:u,name:w,instance:m,options:x}),T=function(){};d.push(y||T)}})}function p(){d.forEach(function(b){return b()}),d=[]}return m}}var li={passive:!0};function UC(e){var t=e.state,n=e.instance,o=e.options,a=o.scroll,i=a===void 0?!0:a,l=o.resize,r=l===void 0?!0:l,s=Kt(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(u){u.addEventListener("scroll",n.update,li)}),r&&s.addEventListener("resize",n.update,li),function(){i&&c.forEach(function(u){u.removeEventListener("scroll",n.update,li)}),r&&s.removeEventListener("resize",n.update,li)}}var YC={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:UC,data:{}};function KC(e){var t=e.state,n=e.name;t.modifiersData[n]=HC({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}var qC={name:"popperOffsets",enabled:!0,phase:"read",fn:KC,data:{}},GC={top:"auto",right:"auto",bottom:"auto",left:"auto"};function XC(e){var t=e.x,n=e.y,o=window,a=o.devicePixelRatio||1;return{x:Wo(t*a)/a||0,y:Wo(n*a)/a||0}}function du(e){var t,n=e.popper,o=e.popperRect,a=e.placement,i=e.variation,l=e.offsets,r=e.position,s=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,h=l.x,m=h===void 0?0:h,v=l.y,p=v===void 0?0:v,b=typeof u=="function"?u({x:m,y:p}):{x:m,y:p};m=b.x,p=b.y;var w=l.hasOwnProperty("x"),g=l.hasOwnProperty("y"),x=uo,S=Fo,y=window;if(c){var T=bh(n),O="clientHeight",C="clientWidth";if(T===Kt(n)&&(T=ll(n),kn(T).position!=="static"&&r==="absolute"&&(O="scrollHeight",C="scrollWidth")),T=T,a===Fo||(a===uo||a===Ba)&&i===Oi){S=Ai;var A=d&&T===y&&y.visualViewport?y.visualViewport.height:T[O];p-=A-o.height,p*=s?1:-1}if(a===uo||(a===Fo||a===Ai)&&i===Oi){x=Ba;var I=d&&T===y&&y.visualViewport?y.visualViewport.width:T[C];m-=I-o.width,m*=s?1:-1}}var P=Object.assign({position:r},c&&GC),k=u===!0?XC({x:m,y:p}):{x:m,y:p};if(m=k.x,p=k.y,s){var D;return Object.assign({},P,(D={},D[S]=g?"0":"",D[x]=w?"0":"",D.transform=(y.devicePixelRatio||1)<=1?"translate("+m+"px, "+p+"px)":"translate3d("+m+"px, "+p+"px, 0)",D))}return Object.assign({},P,(t={},t[S]=g?p+"px":"",t[x]=w?m+"px":"",t.transform="",t))}function ZC(e){var t=e.state,n=e.options,o=n.gpuAcceleration,a=o===void 0?!0:o,i=n.adaptive,l=i===void 0?!0:i,r=n.roundOffsets,s=r===void 0?!0:r;{var c=kn(t.elements.popper).transitionProperty||"";l&&["transform","top","right","bottom","left"].some(function(d){return c.indexOf(d)>=0})&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',`

`,'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.",`

`,"We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" "))}var u={placement:rl(t.placement),variation:wh(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,du(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:l,roundOffsets:s})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,du(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var JC={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:ZC,data:{}};function QC(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},a=t.attributes[n]||{},i=t.elements[n];!Ft(i)||!_n(i)||(Object.assign(i.style,o),Object.keys(a).forEach(function(l){var r=a[l];r===!1?i.removeAttribute(l):i.setAttribute(l,r===!0?"":r)}))})}function e1(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var a=t.elements[o],i=t.attributes[o]||{},l=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]),r=l.reduce(function(s,c){return s[c]="",s},{});!Ft(a)||!_n(a)||(Object.assign(a.style,r),Object.keys(i).forEach(function(s){a.removeAttribute(s)}))})}}var t1={name:"applyStyles",enabled:!0,phase:"write",fn:QC,effect:e1,requires:["computeStyles"]},n1=[YC,qC,JC,t1],o1=WC({defaultModifiers:n1});function a1(e,t,n){var o=rl(e),a=[uo,Fo].indexOf(o)>=0?-1:1,i=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,l=i[0],r=i[1];return l=l||0,r=(r||0)*a,[uo,Ba].indexOf(o)>=0?{x:r,y:l}:{x:l,y:r}}function i1(e){var t=e.state,n=e.options,o=e.name,a=n.offset,i=a===void 0?[0,0]:a,l=TC.reduce(function(u,d){return u[d]=a1(d,t.rects,i),u},{}),r=l[t.placement],s=r.x,c=r.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=c),t.modifiersData[o]=l}var l1={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:i1};const[r1,ao]=Y("popover"),s1=["overlay","duration","teleport","overlayStyle","overlayClass","closeOnClickOverlay"],c1={show:Boolean,theme:J("light"),overlay:Boolean,actions:Ne(),actionsDirection:J("vertical"),trigger:J("click"),duration:G,showArrow:H,placement:J("bottom"),iconPrefix:String,overlayClass:ze,overlayStyle:Object,closeOnClickAction:H,closeOnClickOverlay:H,closeOnClickOutside:H,offset:{type:Array,default:()=>[0,8]},teleport:{type:[String,Object],default:"body"}};var u1=W({name:r1,props:c1,emits:["select","touchstart","update:show"],setup(e,{emit:t,slots:n,attrs:o}){let a;const i=M(),l=M(),r=M(),s=Xr(()=>e.show,g=>t("update:show",g)),c=()=>({placement:e.placement,modifiers:[{name:"computeStyles",options:{adaptive:!1,gpuAcceleration:!1}},he({},l1,{options:{offset:e.offset}})]}),u=()=>l.value&&r.value?o1(l.value,r.value.popupRef.value,c()):null,d=()=>{Se(()=>{s.value&&(a?a.setOptions(c()):(a=u(),At&&(window.addEventListener("animationend",d),window.addEventListener("transitionend",d))))})},h=g=>{s.value=g},m=()=>{e.trigger==="click"&&(s.value=!s.value)},v=(g,x)=>{g.disabled||(t("select",g,x),e.closeOnClickAction&&(s.value=!1))},p=()=>{s.value&&e.closeOnClickOutside&&(!e.overlay||e.closeOnClickOverlay)&&(s.value=!1)},b=(g,x)=>n.action?n.action({action:g,index:x}):[g.icon&&f(xe,{name:g.icon,classPrefix:e.iconPrefix,class:ao("action-icon")},null),f("div",{class:[ao("action-text"),{[Yr]:e.actionsDirection==="vertical"}]},[g.text])],w=(g,x)=>{const{icon:S,color:y,disabled:T,className:O}=g;return f("div",{role:"menuitem",class:[ao("action",{disabled:T,"with-icon":S}),{[Kb]:e.actionsDirection==="horizontal"},O],style:{color:y},tabindex:T?void 0:0,"aria-disabled":T||void 0,onClick:()=>v(g,x)},[b(g,x)])};return We(()=>{d(),Yo(()=>{var g;i.value=(g=r.value)==null?void 0:g.popupRef.value})}),rn(()=>{a&&(At&&(window.removeEventListener("animationend",d),window.removeEventListener("transitionend",d)),a.destroy(),a=null)}),te(()=>[s.value,e.offset,e.placement],d),Gi([l,i],p,{eventName:"touchstart"}),()=>{var g;return f(qe,null,[f("span",{ref:l,class:ao("wrapper"),onClick:m},[(g=n.reference)==null?void 0:g.call(n)]),f(Ut,Ce({ref:r,show:s.value,class:ao([e.theme]),position:"",transition:"van-popover-zoom",lockScroll:!1,"onUpdate:show":h},o,Ei(),$e(e,s1)),{default:()=>[e.showArrow&&f("div",{class:ao("arrow")},null),f("div",{role:"menu",class:ao("content",e.actionsDirection)},[n.default?n.default():e.actions.map(w)])]})])}}});const d1=Z(u1),[f1,Fl]=Y("progress"),h1={color:String,inactive:Boolean,pivotText:String,textColor:String,showPivot:H,pivotColor:String,trackColor:String,strokeWidth:G,percentage:{type:G,default:0,validator:e=>+e>=0&&+e<=100}};var m1=W({name:f1,props:h1,setup(e){const t=B(()=>e.inactive?void 0:e.color),n=()=>{const{textColor:o,pivotText:a,pivotColor:i,percentage:l}=e,r=a??`${l}%`;if(e.showPivot&&r){const s={color:o,left:`${+l}%`,transform:`translate(-${+l}%,-50%)`,background:i||t.value};return f("span",{style:s,class:Fl("pivot",{inactive:e.inactive})},[r])}};return()=>{const{trackColor:o,percentage:a,strokeWidth:i}=e,l={background:o,height:we(i)},r={width:`${a}%`,background:t.value};return f("div",{class:Fl(),style:l},[f("span",{class:Fl("portion",{inactive:e.inactive}),style:r},null),n()])}}});const v1=Z(m1),[g1,la,b1]=Y("pull-refresh"),xh=50,y1=["pulling","loosing","success"],p1={disabled:Boolean,modelValue:Boolean,headHeight:se(xh),successText:String,pullingText:String,loosingText:String,loadingText:String,pullDistance:G,successDuration:se(500),animationDuration:se(300)};var w1=W({name:g1,props:p1,emits:["change","refresh","update:modelValue"],setup(e,{emit:t,slots:n}){let o;const a=M(),i=M(),l=qo(a),r=je({status:"normal",distance:0,duration:0}),s=Ot(),c=()=>{if(e.headHeight!==xh)return{height:`${e.headHeight}px`}},u=()=>r.status!=="loading"&&r.status!=="success"&&!e.disabled,d=S=>{const y=+(e.pullDistance||e.headHeight);return S>y&&(S<y*2?S=y+(S-y)/2:S=y*1.5+(S-y*2)/4),Math.round(S)},h=(S,y)=>{const T=+(e.pullDistance||e.headHeight);r.distance=S,y?r.status="loading":S===0?r.status="normal":S<T?r.status="pulling":r.status="loosing",t("change",{status:r.status,distance:S})},m=()=>{const{status:S}=r;return S==="normal"?"":e[`${S}Text`]||b1(S)},v=()=>{const{status:S,distance:y}=r;if(n[S])return n[S]({distance:y});const T=[];return y1.includes(S)&&T.push(f("div",{class:la("text")},[m()])),S==="loading"&&T.push(f(Wt,{class:la("loading")},{default:m})),T},p=()=>{r.status="success",setTimeout(()=>{h(0)},+e.successDuration)},b=S=>{o=Tn(l.value)===0,o&&(r.duration=0,s.start(S))},w=S=>{u()&&b(S)},g=S=>{if(u()){o||b(S);const{deltaY:y}=s;s.move(S),o&&y.value>=0&&s.isVertical()&&(Ve(S),h(d(y.value)))}},x=()=>{o&&s.deltaY.value&&u()&&(r.duration=+e.animationDuration,r.status==="loosing"?(h(+e.headHeight,!0),t("update:modelValue",!0),Se(()=>t("refresh"))):h(0))};return te(()=>e.modelValue,S=>{r.duration=+e.animationDuration,S?h(+e.headHeight,!0):n.success||e.successText?p():h(0,!1)}),Ye("touchmove",g,{target:i}),()=>{var S;const y={transitionDuration:`${r.duration}ms`,transform:r.distance?`translate3d(0,${r.distance}px, 0)`:""};return f("div",{ref:a,class:la()},[f("div",{ref:i,class:la("track"),style:y,onTouchstartPassive:w,onTouchend:x,onTouchcancel:x},[f("div",{class:la("head"),style:c()},[v()]),(S=n.default)==null?void 0:S.call(n)])])}}});const x1=Z(w1),[S1,ri]=Y("rate");function C1(e,t,n,o){return e>=t?{status:"full",value:1}:e+.5>=t&&n&&!o?{status:"half",value:.5}:e+1>=t&&n&&o?{status:"half",value:Math.round((e-t+1)*1e10)/1e10}:{status:"void",value:0}}const T1={size:G,icon:J("star"),color:String,count:se(5),gutter:G,clearable:Boolean,readonly:Boolean,disabled:Boolean,voidIcon:J("star-o"),allowHalf:Boolean,voidColor:String,touchable:H,iconPrefix:String,modelValue:Ge(0),disabledColor:String};var _1=W({name:S1,props:T1,emits:["change","update:modelValue"],setup(e,{emit:t}){const n=Ot(),[o,a]=Fa(),i=M(),l=B(()=>e.readonly||e.disabled),r=B(()=>l.value||!e.touchable),s=B(()=>Array(+e.count).fill("").map((x,S)=>C1(e.modelValue,S+1,e.allowHalf,e.readonly)));let c,u,d=Number.MAX_SAFE_INTEGER,h=Number.MIN_SAFE_INTEGER;const m=()=>{u=Oe(i);const x=o.value.map(Oe);c=[],x.forEach((S,y)=>{d=Math.min(S.top,d),h=Math.max(S.top,h),e.allowHalf?c.push({score:y+.5,left:S.left,top:S.top,height:S.height},{score:y+1,left:S.left+S.width/2,top:S.top,height:S.height}):c.push({score:y+1,left:S.left,top:S.top,height:S.height})})},v=(x,S)=>{for(let y=c.length-1;y>0;y--)if(S>=u.top&&S<=u.bottom){if(x>c[y].left&&S>=c[y].top&&S<=c[y].top+c[y].height)return c[y].score}else{const T=S<u.top?d:h;if(x>c[y].left&&c[y].top===T)return c[y].score}return e.allowHalf?.5:1},p=x=>{l.value||x===e.modelValue||(t("update:modelValue",x),t("change",x))},b=x=>{r.value||(n.start(x),m())},w=x=>{if(!r.value&&(n.move(x),n.isHorizontal()&&!n.isTap.value)){const{clientX:S,clientY:y}=x.touches[0];Ve(x),p(v(S,y))}},g=(x,S)=>{const{icon:y,size:T,color:O,count:C,gutter:A,voidIcon:I,disabled:P,voidColor:k,allowHalf:D,iconPrefix:j,disabledColor:ae}=e,F=S+1,ne=x.status==="full",oe=x.status==="void",Te=D&&x.value>0&&x.value<1;let Ee;A&&F!==+C&&(Ee={paddingRight:we(A)});const re=N=>{m();let ee=D?v(N.clientX,N.clientY):F;e.clearable&&n.isTap.value&&ee===e.modelValue&&(ee=0),p(ee)};return f("div",{key:S,ref:a(S),role:"radio",style:Ee,class:ri("item"),tabindex:P?void 0:0,"aria-setsize":C,"aria-posinset":F,"aria-checked":!oe,onClick:re},[f(xe,{size:T,name:ne?y:I,class:ri("icon",{disabled:P,full:ne}),color:P?ae:ne?O:k,classPrefix:j},null),Te&&f(xe,{size:T,style:{width:x.value+"em"},name:oe?I:y,class:ri("icon",["half",{disabled:P,full:!oe}]),color:P?ae:oe?k:O,classPrefix:j},null)])};return Xn(()=>e.modelValue),Ye("touchmove",w,{target:i}),()=>f("div",{ref:i,role:"radiogroup",class:ri({readonly:e.readonly,disabled:e.disabled}),tabindex:e.disabled?void 0:0,"aria-disabled":e.disabled,"aria-readonly":e.readonly,onTouchstartPassive:b},[s.value.map(g)])}});const k1=Z(_1),E1={figureArr:Ne(),delay:Number,duration:Ge(2),isStart:Boolean,direction:J("down"),height:Ge(40)},[P1,Nl]=Y("rolling-text-item");var $1=W({name:P1,props:E1,setup(e){const t=B(()=>e.direction==="down"?e.figureArr.slice().reverse():e.figureArr),n=B(()=>`-${e.height*(e.figureArr.length-1)}px`),o=B(()=>({lineHeight:we(e.height)})),a=B(()=>({height:we(e.height),"--van-translate":n.value,"--van-duration":e.duration+"s","--van-delay":e.delay+"s"}));return()=>f("div",{class:Nl([e.direction]),style:a.value},[f("div",{class:Nl("box",{animate:e.isStart})},[Array.isArray(t.value)&&t.value.map(i=>f("div",{class:Nl("item"),style:o.value},[i]))])])}});const[A1,O1]=Y("rolling-text"),I1={startNum:Ge(0),targetNum:Number,textList:Ne(),duration:Ge(2),autoStart:H,direction:J("down"),stopOrder:J("ltr"),height:Ge(40)},R1=2;var D1=W({name:A1,props:I1,setup(e){const t=B(()=>Array.isArray(e.textList)&&e.textList.length),n=B(()=>t.value?e.textList[0].length:`${Math.max(e.startNum,e.targetNum)}`.length),o=d=>{const h=[];for(let m=0;m<e.textList.length;m++)h.push(e.textList[m][d]);return h},a=B(()=>t.value?new Array(n.value).fill(""):Bt(e.targetNum,n.value).split("")),i=B(()=>Bt(e.startNum,n.value).split("")),l=d=>{const h=+i.value[d],m=+a.value[d],v=[];for(let p=h;p<=9;p++)v.push(p);for(let p=0;p<=R1;p++)for(let b=0;b<=9;b++)v.push(b);for(let p=0;p<=m;p++)v.push(p);return v},r=(d,h)=>e.stopOrder==="ltr"?.2*d:.2*(h-1-d),s=M(e.autoStart),c=()=>{s.value=!0},u=()=>{s.value=!1,e.autoStart&&st(()=>c())};return te(()=>e.autoStart,d=>{d&&c()}),_e({start:c,reset:u}),()=>f("div",{class:O1()},[a.value.map((d,h)=>f($1,{figureArr:t.value?o(h):l(h),duration:e.duration,direction:e.direction,isStart:s.value,height:e.height,delay:r(h,n.value)},null))])}});const B1=Z(D1),M1=Z(Pw),[L1,ra,V1]=Y("search"),F1=he({},es,{label:String,shape:J("square"),leftIcon:J("search"),clearable:H,actionText:String,background:String,showAction:Boolean});var N1=W({name:L1,props:F1,emits:["blur","focus","clear","search","cancel","clickInput","clickLeftIcon","clickRightIcon","update:modelValue"],setup(e,{emit:t,slots:n,attrs:o}){const a=Xo(),i=M(),l=()=>{n.action||(t("update:modelValue",""),t("cancel"))},r=y=>{y.keyCode===13&&(Ve(y),t("search",e.modelValue))},s=()=>e.id||`${a}-input`,c=()=>{if(n.label||e.label)return f("label",{class:ra("label"),for:s(),"data-allow-mismatch":"attribute"},[n.label?n.label():e.label])},u=()=>{if(e.showAction){const y=e.actionText||V1("cancel");return f("div",{class:ra("action"),role:"button",tabindex:0,onClick:l},[n.action?n.action():y])}},d=()=>{var y;return(y=i.value)==null?void 0:y.blur()},h=()=>{var y;return(y=i.value)==null?void 0:y.focus()},m=y=>t("blur",y),v=y=>t("focus",y),p=y=>t("clear",y),b=y=>t("clickInput",y),w=y=>t("clickLeftIcon",y),g=y=>t("clickRightIcon",y),x=Object.keys(es),S=()=>{const y=he({},o,$e(e,x),{id:s()}),T=O=>t("update:modelValue",O);return f(wn,Ce({ref:i,type:"search",class:ra("field",{"with-message":y.errorMessage}),border:!1,onBlur:m,onFocus:v,onClear:p,onKeypress:r,onClickInput:b,onClickLeftIcon:w,onClickRightIcon:g,"onUpdate:modelValue":T},y),$e(n,["left-icon","right-icon"]))};return _e({focus:h,blur:d}),()=>{var y;return f("div",{class:ra({"show-action":e.showAction}),style:{background:e.background}},[(y=n.left)==null?void 0:y.call(n),f("div",{class:ra("content",e.shape)},[c(),S()]),u()])}}});const z1=Z(N1),H1=e=>e==null?void 0:e.includes("/"),j1=[...qr,"round","closeOnPopstate","safeAreaInsetBottom"],W1={qq:"qq",link:"link-o",weibo:"weibo",qrcode:"qr",poster:"photo-o",wechat:"wechat","weapp-qrcode":"miniprogram-o","wechat-moments":"wechat-moments"},[U1,Rt,Y1]=Y("share-sheet"),K1=he({},Go,{title:String,round:H,options:Ne(),cancelText:String,description:String,closeOnPopstate:H,safeAreaInsetBottom:H});var q1=W({name:U1,props:K1,emits:["cancel","select","update:show"],setup(e,{emit:t,slots:n}){const o=h=>t("update:show",h),a=()=>{o(!1),t("cancel")},i=(h,m)=>t("select",h,m),l=()=>{const h=n.title?n.title():e.title,m=n.description?n.description():e.description;if(h||m)return f("div",{class:Rt("header")},[h&&f("h2",{class:Rt("title")},[h]),m&&f("span",{class:Rt("description")},[m])])},r=h=>H1(h)?f("img",{src:h,class:Rt("image-icon")},null):f("div",{class:Rt("icon",[h])},[f(xe,{name:W1[h]||h},null)]),s=(h,m)=>{const{name:v,icon:p,className:b,description:w}=h;return f("div",{role:"button",tabindex:0,class:[Rt("option"),b,ct],onClick:()=>i(h,m)},[r(p),v&&f("span",{class:Rt("name")},[v]),w&&f("span",{class:Rt("option-description")},[w])])},c=(h,m)=>f("div",{class:Rt("options",{border:m})},[h.map(s)]),u=()=>{const{options:h}=e;return Array.isArray(h[0])?h.map((m,v)=>c(m,v!==0)):c(h)},d=()=>{var h;const m=(h=e.cancelText)!=null?h:Y1("cancel");if(n.cancel||m)return f("button",{type:"button",class:Rt("cancel"),onClick:a},[n.cancel?n.cancel():m])};return()=>f(Ut,Ce({class:Rt(),position:"bottom","onUpdate:show":o},$e(e,j1)),{default:()=>[l(),u(),d()]})}});const G1=Z(q1),[Sh,X1]=Y("sidebar"),Ch=Symbol(Sh),Z1={modelValue:se(0)};var J1=W({name:Sh,props:Z1,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:o}=dt(Ch),a=()=>+e.modelValue;return o({getActive:a,setActive:l=>{l!==a()&&(t("update:modelValue",l),t("change",l))}}),()=>{var l;return f("div",{role:"tablist",class:X1()},[(l=n.default)==null?void 0:l.call(n)])}}});const Th=Z(J1),[Q1,fu]=Y("sidebar-item"),eT=he({},Jn,{dot:Boolean,title:String,badge:G,disabled:Boolean,badgeProps:Object});var tT=W({name:Q1,props:eT,emits:["click"],setup(e,{emit:t,slots:n}){const o=mo(),{parent:a,index:i}=it(Ch);if(!a)return;const l=()=>{e.disabled||(t("click",i.value),a.setActive(i.value),o())};return()=>{const{dot:r,badge:s,title:c,disabled:u}=e,d=i.value===a.getActive();return f("div",{role:"tab",class:fu({select:d,disabled:u}),tabindex:u?void 0:0,"aria-selected":d,onClick:l},[f(vo,Ce({dot:r,class:fu("text"),content:s},e.badgeProps),{default:()=>[n.title?n.title():c]})])}}});const _h=Z(tT),[nT,zl,hu]=Y("signature"),oT={tips:String,type:J("png"),penColor:J("#000"),lineWidth:Ge(3),clearButtonText:String,backgroundColor:J(""),confirmButtonText:String},aT=()=>{var e;const t=document.createElement("canvas");return!!((e=t.getContext)!=null&&e.call(t,"2d"))};var iT=W({name:nT,props:oT,emits:["submit","clear","start","end","signing"],setup(e,{emit:t,slots:n}){const o=M(),a=M(),i=B(()=>o.value?o.value.getContext("2d"):null),l=At?aT():!0;let r=0,s=0,c;const u=()=>{if(!i.value)return!1;i.value.beginPath(),i.value.lineWidth=e.lineWidth,i.value.strokeStyle=e.penColor,c=Oe(o),t("start")},d=x=>{if(!i.value)return!1;Ve(x);const S=x.touches[0],y=S.clientX-((c==null?void 0:c.left)||0),T=S.clientY-((c==null?void 0:c.top)||0);i.value.lineCap="round",i.value.lineJoin="round",i.value.lineTo(y,T),i.value.stroke(),t("signing",x)},h=x=>{Ve(x),t("end")},m=x=>{const S=document.createElement("canvas");if(S.width=x.width,S.height=x.height,e.backgroundColor){const y=S.getContext("2d");v(y)}return x.toDataURL()===S.toDataURL()},v=x=>{x&&e.backgroundColor&&(x.fillStyle=e.backgroundColor,x.fillRect(0,0,r,s))},p=()=>{var x,S;const y=o.value;if(!y)return;const O=m(y)?"":((S=(x={jpg:()=>y.toDataURL("image/jpeg",.8),jpeg:()=>y.toDataURL("image/jpeg",.8)})[e.type])==null?void 0:S.call(x))||y.toDataURL(`image/${e.type}`);t("submit",{image:O,canvas:y})},b=()=>{i.value&&(i.value.clearRect(0,0,r,s),i.value.closePath(),v(i.value)),t("clear")},w=()=>{var x,S,y;if(l&&o.value){const T=o.value,O=At?window.devicePixelRatio:1;r=T.width=(((x=a.value)==null?void 0:x.offsetWidth)||0)*O,s=T.height=(((S=a.value)==null?void 0:S.offsetHeight)||0)*O,(y=i.value)==null||y.scale(O,O),v(i.value)}},g=()=>{if(i.value){const x=i.value.getImageData(0,0,r,s);w(),i.value.putImageData(x,0,0)}};return te(Vt,g),We(w),_e({resize:g,clear:b,submit:p}),()=>f("div",{class:zl()},[f("div",{class:zl("content"),ref:a},[l?f("canvas",{ref:o,onTouchstartPassive:u,onTouchmove:d,onTouchend:h},null):n.tips?n.tips():f("p",null,[e.tips])]),f("div",{class:zl("footer")},[f(ut,{size:"small",onClick:b},{default:()=>[e.clearButtonText||hu("clear")]}),f(ut,{type:"primary",size:"small",onClick:p},{default:()=>[e.confirmButtonText||hu("confirm")]})])])}});const lT=Z(iT),[rT,sT]=Y("skeleton-title"),cT={round:Boolean,titleWidth:G};var uT=W({name:rT,props:cT,setup(e){return()=>f("h3",{class:sT([{round:e.round}]),style:{width:we(e.titleWidth)}},null)}});const kh=Z(uT);var dT=kh;const[fT,hT]=Y("skeleton-avatar"),mT={avatarSize:G,avatarShape:J("round")};var vT=W({name:fT,props:mT,setup(e){return()=>f("div",{class:hT([e.avatarShape]),style:An(e.avatarSize)},null)}});const Eh=Z(vT);var gT=Eh;const ds="100%",bT={round:Boolean,rowWidth:{type:G,default:ds}},[yT,pT]=Y("skeleton-paragraph");var wT=W({name:yT,props:bT,setup(e){return()=>f("div",{class:pT([{round:e.round}]),style:{width:e.rowWidth}},null)}});const Ph=Z(wT);var xT=Ph;const[ST,mu]=Y("skeleton"),CT="60%",TT={row:se(0),round:Boolean,title:Boolean,titleWidth:G,avatar:Boolean,avatarSize:G,avatarShape:J("round"),loading:H,animate:H,rowWidth:{type:[Number,String,Array],default:ds}};var _T=W({name:ST,inheritAttrs:!1,props:TT,setup(e,{slots:t,attrs:n}){const o=()=>{if(e.avatar)return f(gT,{avatarShape:e.avatarShape,avatarSize:e.avatarSize},null)},a=()=>{if(e.title)return f(dT,{round:e.round,titleWidth:e.titleWidth},null)},i=s=>{const{rowWidth:c}=e;return c===ds&&s===+e.row-1?CT:Array.isArray(c)?c[s]:c},l=()=>Array(+e.row).fill("").map((s,c)=>f(xT,{key:c,round:e.round,rowWidth:we(i(c))},null)),r=()=>t.template?t.template():f(qe,null,[o(),f("div",{class:mu("content")},[a(),l()])]);return()=>{var s;return e.loading?f("div",Ce({class:mu({animate:e.animate,round:e.round})},n),[r()]):(s=t.default)==null?void 0:s.call(t)}}});const kT=Z(_T),[ET,vu]=Y("skeleton-image"),PT={imageSize:G,imageShape:J("square")};var $T=W({name:ET,props:PT,setup(e){return()=>f("div",{class:vu([e.imageShape]),style:An(e.imageSize)},[f(xe,{name:"photo",class:vu("icon")},null)])}});const AT=Z($T),[OT,sa]=Y("slider"),IT={min:se(0),max:se(100),step:se(1),range:Boolean,reverse:Boolean,disabled:Boolean,readonly:Boolean,vertical:Boolean,barHeight:G,buttonSize:G,activeColor:String,inactiveColor:String,modelValue:{type:[Number,Array],default:0}};var RT=W({name:OT,props:IT,emits:["change","dragEnd","dragStart","update:modelValue"],setup(e,{emit:t,slots:n}){let o,a,i;const l=M(),r=[M(),M()],s=M(),c=Ot(),u=B(()=>Number(e.max)-Number(e.min)),d=B(()=>{const P=e.vertical?"width":"height";return{background:e.inactiveColor,[P]:we(e.barHeight)}}),h=P=>e.range&&Array.isArray(P),m=()=>{const{modelValue:P,min:k}=e;return h(P)?`${(P[1]-P[0])*100/u.value}%`:`${(P-Number(k))*100/u.value}%`},v=()=>{const{modelValue:P,min:k}=e;return h(P)?`${(P[0]-Number(k))*100/u.value}%`:"0%"},p=B(()=>{const k={[e.vertical?"height":"width"]:m(),background:e.activeColor};s.value&&(k.transition="none");const D=()=>e.vertical?e.reverse?"bottom":"top":e.reverse?"right":"left";return k[D()]=v(),k}),b=P=>{const k=+e.min,D=+e.max,j=+e.step;P=Xe(P,k,D);const ae=Math.round((P-k)/j)*j;return lf(k,ae)},w=()=>{const P=e.modelValue;h(P)?i=P.map(b):i=b(P)},g=P=>{var k,D;const j=(k=P[0])!=null?k:Number(e.min),ae=(D=P[1])!=null?D:Number(e.max);return j>ae?[ae,j]:[j,ae]},x=(P,k)=>{h(P)?P=g(P).map(b):P=b(P),on(P,e.modelValue)||t("update:modelValue",P),k&&!on(P,i)&&t("change",P)},S=P=>{if(P.stopPropagation(),e.disabled||e.readonly)return;w();const{min:k,reverse:D,vertical:j,modelValue:ae}=e,F=Oe(l),ne=()=>j?D?F.bottom-P.clientY:P.clientY-F.top:D?F.right-P.clientX:P.clientX-F.left,oe=j?F.height:F.width,Te=Number(k)+ne()/oe*u.value;if(h(ae)){const[Ee,re]=ae,N=(Ee+re)/2;Te<=N?x([Te,re],!0):x([Ee,Te],!0)}else x(Te,!0)},y=P=>{e.disabled||e.readonly||(c.start(P),a=e.modelValue,w(),s.value="start")},T=P=>{if(e.disabled||e.readonly)return;s.value==="start"&&t("dragStart",P),Ve(P,!0),c.move(P),s.value="dragging";const k=Oe(l),D=e.vertical?c.deltaY.value:c.deltaX.value,j=e.vertical?k.height:k.width;let ae=D/j*u.value;if(e.reverse&&(ae=-ae),h(i)){const F=e.reverse?1-o:o;a[F]=i[F]+ae}else a=i+ae;x(a)},O=P=>{e.disabled||e.readonly||(s.value==="dragging"&&(x(a,!0),t("dragEnd",P)),s.value="")},C=P=>typeof P=="number"?sa("button-wrapper",["left","right"][P]):sa("button-wrapper",e.reverse?"left":"right"),A=(P,k)=>{const D=s.value==="dragging";if(typeof k=="number"){const j=n[k===0?"left-button":"right-button"];let ae;if(D&&Array.isArray(a)&&(ae=a[0]>a[1]?o^1:o),j)return j({value:P,dragging:D,dragIndex:ae})}return n.button?n.button({value:P,dragging:D}):f("div",{class:sa("button"),style:An(e.buttonSize)},null)},I=P=>{const k=typeof P=="number"?e.modelValue[P]:e.modelValue;return f("div",{ref:r[P??0],role:"slider",class:C(P),tabindex:e.disabled?void 0:0,"aria-valuemin":e.min,"aria-valuenow":k,"aria-valuemax":e.max,"aria-disabled":e.disabled||void 0,"aria-readonly":e.readonly||void 0,"aria-orientation":e.vertical?"vertical":"horizontal",onTouchstartPassive:D=>{typeof P=="number"&&(o=P),y(D)},onTouchend:O,onTouchcancel:O,onClick:Wr},[A(k,P)])};return x(e.modelValue),Xn(()=>e.modelValue),r.forEach(P=>{Ye("touchmove",T,{target:P})}),()=>f("div",{ref:l,style:d.value,class:sa({vertical:e.vertical,disabled:e.disabled}),onClick:S},[f("div",{class:sa("bar"),style:p.value},[e.range?[I(0),I(1)]:I()])])}});const DT=Z(RT),[gu,BT]=Y("space"),MT={align:String,direction:{type:String,default:"horizontal"},size:{type:[Number,String,Array],default:8},wrap:Boolean,fill:Boolean};function $h(e=[]){const t=[];return e.forEach(n=>{Array.isArray(n)?t.push(...n):n.type===qe?t.push(...$h(n.children)):t.push(n)}),t.filter(n=>{var o;return!(n&&(n.type===tt||n.type===qe&&((o=n.children)==null?void 0:o.length)===0||n.type===La&&n.children.trim()===""))})}var LT=W({name:gu,props:MT,setup(e,{slots:t}){const n=B(()=>{var i;return(i=e.align)!=null?i:e.direction==="horizontal"?"center":""}),o=i=>typeof i=="number"?i+"px":i,a=i=>{const l={},r=`${o(Array.isArray(e.size)?e.size[0]:e.size)}`,s=`${o(Array.isArray(e.size)?e.size[1]:e.size)}`;return i?e.wrap?{marginBottom:s}:{}:(e.direction==="horizontal"&&(l.marginRight=r),(e.direction==="vertical"||e.wrap)&&(l.marginBottom=s),l)};return()=>{var i;const l=$h((i=t.default)==null?void 0:i.call(t));return f("div",{class:[BT({[e.direction]:e.direction,[`align-${n.value}`]:n.value,wrap:e.wrap,fill:e.fill})]},[l.map((r,s)=>f("div",{key:`item-${s}`,class:`${gu}-item`,style:a(s===l.length-1)},[r]))])}}});const VT=Z(LT),[Ah,bu]=Y("steps"),FT={active:se(0),direction:J("horizontal"),activeIcon:J("checked"),iconPrefix:String,finishIcon:String,activeColor:String,inactiveIcon:String,inactiveColor:String},Oh=Symbol(Ah);var NT=W({name:Ah,props:FT,emits:["clickStep"],setup(e,{emit:t,slots:n}){const{linkChildren:o}=dt(Oh);return o({props:e,onClickStep:i=>t("clickStep",i)}),()=>{var i;return f("div",{class:bu([e.direction])},[f("div",{class:bu("items")},[(i=n.default)==null?void 0:i.call(n)])])}}});const[zT,Fn]=Y("step");var HT=W({name:zT,setup(e,{slots:t}){const{parent:n,index:o}=it(Oh);if(!n)return;const a=n.props,i=()=>{const d=+a.active;return o.value<d?"finish":o.value===d?"process":"waiting"},l=()=>i()==="process",r=B(()=>({background:i()==="finish"?a.activeColor:a.inactiveColor})),s=B(()=>{if(l())return{color:a.activeColor};if(i()==="waiting")return{color:a.inactiveColor}}),c=()=>n.onClickStep(o.value),u=()=>{const{iconPrefix:d,finishIcon:h,activeIcon:m,activeColor:v,inactiveIcon:p}=a;return l()?t["active-icon"]?t["active-icon"]():f(xe,{class:Fn("icon","active"),name:m,color:v,classPrefix:d},null):i()==="finish"&&(h||t["finish-icon"])?t["finish-icon"]?t["finish-icon"]():f(xe,{class:Fn("icon","finish"),name:h,color:v,classPrefix:d},null):t["inactive-icon"]?t["inactive-icon"]():p?f(xe,{class:Fn("icon"),name:p,classPrefix:d},null):f("i",{class:Fn("circle"),style:r.value},null)};return()=>{var d;const h=i();return f("div",{class:[In,Fn([a.direction,{[h]:h}])]},[f("div",{class:Fn("title",{active:l()}),style:s.value,onClick:c},[(d=t.default)==null?void 0:d.call(t)]),f("div",{class:Fn("circle-container"),onClick:c},[u()]),f("div",{class:Fn("line"),style:r.value},null)])}}});const jT=Z(HT),[WT,si]=Y("stepper"),UT=200,ci=(e,t)=>String(e)===String(t),YT={min:se(1),max:se(1/0),name:se(""),step:se(1),theme:String,integer:Boolean,disabled:Boolean,showPlus:H,showMinus:H,showInput:H,longPress:H,autoFixed:H,allowEmpty:Boolean,modelValue:G,inputWidth:G,buttonSize:G,placeholder:String,disablePlus:Boolean,disableMinus:Boolean,disableInput:Boolean,beforeChange:Function,defaultValue:se(1),decimalLength:G};var KT=W({name:WT,props:YT,emits:["plus","blur","minus","focus","change","overlimit","update:modelValue"],setup(e,{emit:t}){const n=(C,A=!0)=>{const{min:I,max:P,allowEmpty:k,decimalLength:D}=e;return k&&C===""||(C=ur(String(C),!e.integer),C=C===""?0:+C,C=Number.isNaN(C)?+I:C,C=A?Math.max(Math.min(+P,C),+I):C,ke(D)&&(C=C.toFixed(+D))),C},o=()=>{var C;const A=(C=e.modelValue)!=null?C:e.defaultValue,I=n(A);return ci(I,e.modelValue)||t("update:modelValue",I),I};let a;const i=M(),l=M(o()),r=B(()=>e.disabled||e.disableMinus||+l.value<=+e.min),s=B(()=>e.disabled||e.disablePlus||+l.value>=+e.max),c=B(()=>({width:we(e.inputWidth),height:we(e.buttonSize)})),u=B(()=>An(e.buttonSize)),d=()=>{const C=n(l.value);ci(C,l.value)||(l.value=C)},h=C=>{e.beforeChange?Zn(e.beforeChange,{args:[C],done(){l.value=C}}):l.value=C},m=()=>{if(a==="plus"&&s.value||a==="minus"&&r.value){t("overlimit",a);return}const C=a==="minus"?-e.step:+e.step,A=n(lf(+l.value,C));h(A),t(a)},v=C=>{const A=C.target,{value:I}=A,{decimalLength:P}=e;let k=ur(String(I),!e.integer);if(ke(P)&&k.includes(".")){const j=k.split(".");k=`${j[0]}.${j[1].slice(0,+P)}`}e.beforeChange?A.value=String(l.value):ci(I,k)||(A.value=k);const D=k===String(+k);h(D?+k:k)},p=C=>{var A;e.disableInput?(A=i.value)==null||A.blur():t("focus",C)},b=C=>{const A=C.target,I=n(A.value,e.autoFixed);A.value=String(I),l.value=I,Se(()=>{t("blur",C),of()})};let w,g;const x=()=>{g=setTimeout(()=>{m(),x()},UT)},S=()=>{e.longPress&&(w=!1,clearTimeout(g),g=setTimeout(()=>{w=!0,m(),x()},ff))},y=C=>{e.longPress&&(clearTimeout(g),w&&Ve(C))},T=C=>{e.disableInput&&Ve(C)},O=C=>({onClick:A=>{Ve(A),a=C,m()},onTouchstartPassive:()=>{a=C,S()},onTouchend:y,onTouchcancel:y});return te(()=>[e.max,e.min,e.integer,e.decimalLength],d),te(()=>e.modelValue,C=>{ci(C,l.value)||(l.value=n(C))}),te(l,C=>{t("update:modelValue",C),t("change",C,{name:e.name})}),Xn(()=>e.modelValue),()=>f("div",{role:"group",class:si([e.theme])},[Je(f("button",Ce({type:"button",style:u.value,class:[si("minus",{disabled:r.value}),{[ct]:!r.value}],"aria-disabled":r.value||void 0},O("minus")),null),[[at,e.showMinus]]),Je(f("input",{ref:i,type:e.integer?"tel":"text",role:"spinbutton",class:si("input"),value:l.value,style:c.value,disabled:e.disabled,readonly:e.disableInput,inputmode:e.integer?"numeric":"decimal",placeholder:e.placeholder,autocomplete:"off","aria-valuemax":e.max,"aria-valuemin":e.min,"aria-valuenow":l.value,onBlur:b,onInput:v,onFocus:p,onMousedown:T},null),[[at,e.showInput]]),Je(f("button",Ce({type:"button",style:u.value,class:[si("plus",{disabled:s.value}),{[ct]:!s.value}],"aria-disabled":s.value||void 0},O("plus")),null),[[at,e.showPlus]])])}});const qT=Z(KT),GT=Z(NT),[XT,Dt,ZT]=Y("submit-bar"),JT={tip:String,label:String,price:Number,tipIcon:String,loading:Boolean,currency:J("¥"),disabled:Boolean,textAlign:String,buttonText:String,buttonType:J("danger"),buttonColor:String,suffixLabel:String,placeholder:Boolean,decimalLength:se(2),safeAreaInsetBottom:H};var QT=W({name:XT,props:JT,emits:["submit"],setup(e,{emit:t,slots:n}){const o=M(),a=Ji(o,Dt),i=()=>{const{price:u,label:d,currency:h,textAlign:m,suffixLabel:v,decimalLength:p}=e;if(typeof u=="number"){const b=(u/100).toFixed(+p).split("."),w=p?`.${b[1]}`:"";return f("div",{class:Dt("text"),style:{textAlign:m}},[f("span",null,[d||ZT("label")]),f("span",{class:Dt("price")},[h,f("span",{class:Dt("price-integer")},[b[0]]),w]),v&&f("span",{class:Dt("suffix-label")},[v])])}},l=()=>{var u;const{tip:d,tipIcon:h}=e;if(n.tip||d)return f("div",{class:Dt("tip")},[h&&f(xe,{class:Dt("tip-icon"),name:h},null),d&&f("span",{class:Dt("tip-text")},[d]),(u=n.tip)==null?void 0:u.call(n)])},r=()=>t("submit"),s=()=>n.button?n.button():f(ut,{round:!0,type:e.buttonType,text:e.buttonText,class:Dt("button",e.buttonType),color:e.buttonColor,loading:e.loading,disabled:e.disabled,onClick:r},null),c=()=>{var u,d;return f("div",{ref:o,class:[Dt(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(u=n.top)==null?void 0:u.call(n),l(),f("div",{class:Dt("bar")},[(d=n.default)==null?void 0:d.call(n),i(),s()])])};return()=>e.placeholder?a(c):c()}});const e_=Z(QT),[t_,Hl]=Y("swipe-cell"),n_={name:se(""),disabled:Boolean,leftWidth:G,rightWidth:G,beforeClose:Function,stopPropagation:Boolean};var o_=W({name:t_,props:n_,emits:["open","close","click"],setup(e,{emit:t,slots:n}){let o,a,i,l;const r=M(),s=M(),c=M(),u=je({offset:0,dragging:!1}),d=Ot(),h=C=>C.value?Oe(C).width:0,m=B(()=>ke(e.leftWidth)?+e.leftWidth:h(s)),v=B(()=>ke(e.rightWidth)?+e.rightWidth:h(c)),p=C=>{u.offset=C==="left"?m.value:-v.value,o||(o=!0,t("open",{name:e.name,position:C}))},b=C=>{u.offset=0,o&&(o=!1,t("close",{name:e.name,position:C}))},w=C=>{const A=Math.abs(u.offset),I=.15,P=o?1-I:I,k=C==="left"?m.value:v.value;k&&A>k*P?p(C):b(C)},g=C=>{e.disabled||(i=u.offset,d.start(C))},x=C=>{if(e.disabled)return;const{deltaX:A}=d;d.move(C),d.isHorizontal()&&(a=!0,u.dragging=!0,(!o||A.value*i<0)&&Ve(C,e.stopPropagation),u.offset=Xe(A.value+i,-v.value,m.value))},S=()=>{u.dragging&&(u.dragging=!1,w(u.offset>0?"left":"right"),setTimeout(()=>{a=!1},0))},y=(C="outside",A)=>{l||(t("click",C),o&&!a&&(l=!0,Zn(e.beforeClose,{args:[{event:A,name:e.name,position:C}],done:()=>{l=!1,b(C)},canceled:()=>l=!1,error:()=>l=!1})))},T=C=>A=>{(a||o)&&A.stopPropagation(),!a&&y(C,A)},O=(C,A)=>{const I=n[C];if(I)return f("div",{ref:A,class:Hl(C),onClick:T(C)},[I()])};return _e({open:p,close:b}),Gi(r,C=>y("outside",C),{eventName:"touchstart"}),Ye("touchmove",x,{target:r}),()=>{var C;const A={transform:`translate3d(${u.offset}px, 0, 0)`,transitionDuration:u.dragging?"0s":".6s"};return f("div",{ref:r,class:Hl(),onClick:T("cell"),onTouchstartPassive:g,onTouchend:S,onTouchcancel:S},[f("div",{class:Hl("wrapper"),style:A},[O("left",s),(C=n.default)==null?void 0:C.call(n),O("right",c)])])}}});const a_=Z(o_),[Ih,yu]=Y("tabbar"),i_={route:Boolean,fixed:H,border:H,zIndex:G,placeholder:Boolean,activeColor:String,beforeChange:Function,inactiveColor:String,modelValue:se(0),safeAreaInsetBottom:{type:Boolean,default:null}},Rh=Symbol(Ih);var l_=W({name:Ih,props:i_,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=M(),{linkChildren:a}=dt(Rh),i=Ji(o,yu),l=()=>{var c;return(c=e.safeAreaInsetBottom)!=null?c:e.fixed},r=()=>{var c;const{fixed:u,zIndex:d,border:h}=e;return f("div",{ref:o,role:"tablist",style:On(d),class:[yu({fixed:u}),{[Xi]:h,"van-safe-area-bottom":l()}]},[(c=n.default)==null?void 0:c.call(n)])};return a({props:e,setActive:(c,u)=>{Zn(e.beforeChange,{args:[c],done(){t("update:modelValue",c),t("change",c),u()}})}}),()=>e.fixed&&e.placeholder?i(r):r()}});const r_=Z(l_),[s_,jl]=Y("tabbar-item"),c_=he({},Jn,{dot:Boolean,icon:String,name:G,badge:G,badgeProps:Object,iconPrefix:String});var u_=W({name:s_,props:c_,emits:["click"],setup(e,{emit:t,slots:n}){const o=mo(),a=jt().proxy,{parent:i,index:l}=it(Rh);if(!i)return;const r=B(()=>{var u;const{route:d,modelValue:h}=i.props;if(d&&"$route"in a){const{$route:m}=a,{to:v}=e,p=Ht(v)?v:{path:v};return m.matched.some(b=>{const w="path"in p&&p.path===b.path,g="name"in p&&p.name===b.name;return w||g})}return((u=e.name)!=null?u:l.value)===h}),s=u=>{var d;r.value||i.setActive((d=e.name)!=null?d:l.value,o),t("click",u)},c=()=>{if(n.icon)return n.icon({active:r.value});if(e.icon)return f(xe,{name:e.icon,classPrefix:e.iconPrefix},null)};return()=>{var u;const{dot:d,badge:h}=e,{activeColor:m,inactiveColor:v}=i.props,p=r.value?m:v;return f("div",{role:"tab",class:jl({active:r.value}),style:{color:p},tabindex:0,"aria-selected":r.value,onClick:s},[f(vo,Ce({dot:d,class:jl("icon"),content:h},e.badgeProps),{default:c}),f("div",{class:jl("text")},[(u=n.default)==null?void 0:u.call(n,{active:r.value})])])}}});const d_=Z(u_),[f_,pu]=Y("text-ellipsis"),h_={rows:se(1),dots:J("..."),content:J(""),expandText:J(""),collapseText:J(""),position:J("end")};var m_=W({name:f_,props:h_,emits:["clickAction"],setup(e,{emit:t,slots:n}){const o=M(e.content),a=M(!1),i=M(!1),l=M(),r=M();let s=!1;const c=B(()=>a.value?e.collapseText:e.expandText),u=w=>{if(!w)return 0;const g=w.match(/^\d*(\.\d*)?/);return g?Number(g[0]):0},d=()=>{if(!l.value||!l.value.isConnected)return;const w=window.getComputedStyle(l.value),g=document.createElement("div");return Array.prototype.slice.apply(w).forEach(S=>{g.style.setProperty(S,w.getPropertyValue(S))}),g.style.position="fixed",g.style.zIndex="-9999",g.style.top="-9999px",g.style.height="auto",g.style.minHeight="auto",g.style.maxHeight="auto",g.innerText=e.content,document.body.appendChild(g),g},h=(w,g)=>{var x,S;const{content:y,position:T,dots:O}=e,C=y.length,A=0+C>>1,I=n.action?(S=(x=r.value)==null?void 0:x.outerHTML)!=null?S:"":e.expandText,P=()=>{const D=(j,ae)=>{if(ae-j<=1)return T==="end"?y.slice(0,j)+O:O+y.slice(ae,C);const F=Math.round((j+ae)/2);return T==="end"?w.innerText=y.slice(0,F)+O:w.innerText=O+y.slice(F,C),w.innerHTML+=I,w.offsetHeight>g?T==="end"?D(j,F):D(F,ae):T==="end"?D(F,ae):D(j,F)};return D(0,C)},k=(D,j)=>{if(D[1]-D[0]<=1&&j[1]-j[0]<=1)return y.slice(0,D[0])+O+y.slice(j[1],C);const ae=Math.floor((D[0]+D[1])/2),F=Math.ceil((j[0]+j[1])/2);return w.innerText=e.content.slice(0,ae)+e.dots+e.content.slice(F,C),w.innerHTML+=I,w.offsetHeight>=g?k([D[0],ae],[F,j[1]]):k([ae,D[1]],[j[0],F])};return e.position==="middle"?k([0,A],[A,C]):P()},m=()=>{const w=d();if(!w){s=!0;return}const{paddingBottom:g,paddingTop:x,lineHeight:S}=w.style,y=Math.ceil((Number(e.rows)+.5)*u(S)+u(x)+u(g));y<w.offsetHeight?(i.value=!0,o.value=h(w,y)):(i.value=!1,o.value=e.content),document.body.removeChild(w)},v=(w=!a.value)=>{a.value=w},p=w=>{v(),t("clickAction",w)},b=()=>{const w=n.action?n.action({expanded:a.value}):c.value;return f("span",{ref:r,class:pu("action"),onClick:p},[w])};return We(()=>{m(),n.action&&Se(m)}),an(()=>{s&&(s=!1,m())}),te([Vt,()=>[e.content,e.rows,e.position]],m),_e({toggle:v}),()=>f("div",{ref:l,class:pu()},[a.value?e.content:o.value,i.value?b():null])}});const v_=Z(m_),[g_]=Y("time-picker"),wu=e=>/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/.test(e),b_=["hour","minute","second"],y_=he({},Jf,{minHour:se(0),maxHour:se(23),minMinute:se(0),maxMinute:se(59),minSecond:se(0),maxSecond:se(59),minTime:{type:String,validator:wu},maxTime:{type:String,validator:wu},columnsType:{type:Array,default:()=>["hour","minute"]}});var p_=W({name:g_,props:y_,emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=M(e.modelValue),a=M(),i=h=>{const m=h.split(":");return b_.map((v,p)=>e.columnsType.includes(v)?m[p]:"00")},l=()=>{var h;return(h=a.value)==null?void 0:h.confirm()},r=()=>o.value,s=B(()=>{let{minHour:h,maxHour:m,minMinute:v,maxMinute:p,minSecond:b,maxSecond:w}=e;if(e.minTime||e.maxTime){const g={hour:0,minute:0,second:0};e.columnsType.forEach((y,T)=>{var O;g[y]=(O=o.value[T])!=null?O:0});const{hour:x,minute:S}=g;if(e.minTime){const[y,T,O]=i(e.minTime);h=y,v=+x<=+h?T:"00",b=+x<=+h&&+S<=+v?O:"00"}if(e.maxTime){const[y,T,O]=i(e.maxTime);m=y,p=+x>=+m?T:"59",w=+x>=+m&&+S>=+p?O:"59"}}return e.columnsType.map(g=>{const{filter:x,formatter:S}=e;switch(g){case"hour":return Vo(+h,+m,g,S,x,o.value);case"minute":return Vo(+v,+p,g,S,x,o.value);case"second":return Vo(+b,+w,g,S,x,o.value);default:return[]}})});te(o,h=>{on(h,e.modelValue)||t("update:modelValue",h)}),te(()=>e.modelValue,h=>{h=th(h,s.value),on(h,o.value)||(o.value=h)},{immediate:!0});const c=(...h)=>t("change",...h),u=(...h)=>t("cancel",...h),d=(...h)=>t("confirm",...h);return _e({confirm:l,getSelectedTime:r}),()=>f(nl,Ce({ref:a,modelValue:o.value,"onUpdate:modelValue":h=>o.value=h,columns:s.value,onChange:c,onCancel:u,onConfirm:d},$e(e,Qf)),n)}});const w_=Z(p_),[x_,Po]=Y("tree-select"),S_={max:se(1/0),items:Ne(),height:se(300),selectedIcon:J("success"),mainActiveIndex:se(0),activeId:{type:[Number,String,Array],default:0}};var C_=W({name:x_,props:S_,emits:["clickNav","clickItem","update:activeId","update:mainActiveIndex"],setup(e,{emit:t,slots:n}){const o=c=>Array.isArray(e.activeId)?e.activeId.includes(c):e.activeId===c,a=c=>{const u=()=>{if(c.disabled)return;let d;if(Array.isArray(e.activeId)){d=e.activeId.slice();const h=d.indexOf(c.id);h!==-1?d.splice(h,1):d.length<+e.max&&d.push(c.id)}else d=c.id;t("update:activeId",d),t("clickItem",c)};return f("div",{key:c.id,class:["van-ellipsis",Po("item",{active:o(c.id),disabled:c.disabled})],onClick:u},[c.text,o(c.id)&&f(xe,{name:e.selectedIcon,class:Po("selected")},null)])},i=c=>{t("update:mainActiveIndex",c)},l=c=>t("clickNav",c),r=()=>{const c=e.items.map(u=>f(_h,{dot:u.dot,badge:u.badge,class:[Po("nav-item"),u.className],disabled:u.disabled,onClick:l},{title:()=>n["nav-text"]?n["nav-text"](u):u.text}));return f(Th,{class:Po("nav"),modelValue:e.mainActiveIndex,onChange:i},{default:()=>[c]})},s=()=>{if(n.content)return n.content();const c=e.items[+e.mainActiveIndex]||{};if(c.children)return c.children.map(a)};return()=>f("div",{class:Po(),style:{height:we(e.height)}},[r(),f("div",{class:Po("content")},[s()])])}});const T_=Z(C_),[__,Ke,k_]=Y("uploader");function xu(e,t){return new Promise(n=>{if(t==="file"){n();return}const o=new FileReader;o.onload=a=>{n(a.target.result)},t==="dataUrl"?o.readAsDataURL(e):t==="text"&&o.readAsText(e)})}function Dh(e,t){return Ti(e).some(n=>n.file?Ho(t)?t(n.file):n.file.size>+t:!1)}function E_(e,t){const n=[],o=[];return e.forEach(a=>{Dh(a,t)?o.push(a):n.push(a)}),{valid:n,invalid:o}}const P_=/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg|avif)/i,$_=e=>P_.test(e);function Bh(e){return e.isImage?!0:e.file&&e.file.type?e.file.type.indexOf("image")===0:e.url?$_(e.url):typeof e.content=="string"?e.content.indexOf("data:image")===0:!1}var A_=W({props:{name:G,item:Ze(Object),index:Number,imageFit:String,lazyLoad:Boolean,deletable:Boolean,reupload:Boolean,previewSize:[Number,String,Array],beforeDelete:Function},emits:["delete","preview","reupload"],setup(e,{emit:t,slots:n}){const o=()=>{const{status:u,message:d}=e.item;if(u==="uploading"||u==="failed"){const h=u==="failed"?f(xe,{name:"close",class:Ke("mask-icon")},null):f(Wt,{class:Ke("loading")},null),m=ke(d)&&d!=="";return f("div",{class:Ke("mask")},[h,m&&f("div",{class:Ke("mask-message")},[d])])}},a=u=>{const{name:d,item:h,index:m,beforeDelete:v}=e;u.stopPropagation(),Zn(v,{args:[h,{name:d,index:m}],done:()=>t("delete")})},i=()=>t("preview"),l=()=>t("reupload"),r=()=>{if(e.deletable&&e.item.status!=="uploading"){const u=n["preview-delete"];return f("div",{role:"button",class:Ke("preview-delete",{shadow:!u}),tabindex:0,"aria-label":k_("delete"),onClick:a},[u?u():f(xe,{name:"cross",class:Ke("preview-delete-icon")},null)])}},s=()=>{if(n["preview-cover"]){const{index:u,item:d}=e;return f("div",{class:Ke("preview-cover")},[n["preview-cover"](he({index:u},d))])}},c=()=>{const{item:u,lazyLoad:d,imageFit:h,previewSize:m,reupload:v}=e;return Bh(u)?f(il,{fit:h,src:u.objectUrl||u.content||u.url,class:Ke("preview-image"),width:Array.isArray(m)?m[0]:m,height:Array.isArray(m)?m[1]:m,lazyLoad:d,onClick:v?l:i},{default:s}):f("div",{class:Ke("file"),style:An(e.previewSize)},[f(xe,{class:Ke("file-icon"),name:"description"},null),f("div",{class:[Ke("file-name"),"van-ellipsis"]},[u.file?u.file.name:u.url]),s()])};return()=>f("div",{class:Ke("preview")},[c(),o(),r()])}});const O_={name:se(""),accept:J("image/*"),capture:String,multiple:Boolean,disabled:Boolean,readonly:Boolean,lazyLoad:Boolean,maxCount:se(1/0),imageFit:J("cover"),resultType:J("dataUrl"),uploadIcon:J("photograph"),uploadText:String,deletable:H,reupload:Boolean,afterRead:Function,showUpload:H,modelValue:Ne(),beforeRead:Function,beforeDelete:Function,previewSize:[Number,String,Array],previewImage:H,previewOptions:Object,previewFullImage:H,maxSize:{type:[Number,String,Function],default:1/0}};var I_=W({name:__,props:O_,emits:["delete","oversize","clickUpload","closePreview","clickPreview","clickReupload","update:modelValue"],setup(e,{emit:t,slots:n}){const o=M(),a=[],i=M(-1),l=M(!1),r=(C=e.modelValue.length)=>({name:e.name,index:C}),s=()=>{o.value&&(o.value.value="")},c=C=>{if(s(),Dh(C,e.maxSize))if(Array.isArray(C)){const A=E_(C,e.maxSize);if(C=A.valid,t("oversize",A.invalid,r()),!C.length)return}else{t("oversize",C,r());return}if(C=je(C),i.value>-1){const A=[...e.modelValue];A.splice(i.value,1,C),t("update:modelValue",A),i.value=-1}else t("update:modelValue",[...e.modelValue,...Ti(C)]);e.afterRead&&e.afterRead(C,r())},u=C=>{const{maxCount:A,modelValue:I,resultType:P}=e;if(Array.isArray(C)){const k=+A-I.length;C.length>k&&(C=C.slice(0,k)),Promise.all(C.map(D=>xu(D,P))).then(D=>{const j=C.map((ae,F)=>{const ne={file:ae,status:"",message:"",objectUrl:URL.createObjectURL(ae)};return D[F]&&(ne.content=D[F]),ne});c(j)})}else xu(C,P).then(k=>{const D={file:C,status:"",message:"",objectUrl:URL.createObjectURL(C)};k&&(D.content=k),c(D)})},d=C=>{const{files:A}=C.target;if(e.disabled||!A||!A.length)return;const I=A.length===1?A[0]:[].slice.call(A);if(e.beforeRead){const P=e.beforeRead(I,r());if(!P){s();return}if(Hr(P)){P.then(k=>{u(k||I)}).catch(s);return}}u(I)};let h;const m=()=>t("closePreview"),v=C=>{if(e.previewFullImage){const A=e.modelValue.filter(Bh),I=A.map(P=>(P.objectUrl&&!P.url&&P.status!=="failed"&&(P.url=P.objectUrl,a.push(P.url)),P.url)).filter(Boolean);h=xS(he({images:I,startPosition:A.indexOf(C),onClose:m},e.previewOptions))}},p=()=>{h&&h.close()},b=(C,A)=>{const I=e.modelValue.slice(0);I.splice(A,1),t("update:modelValue",I),t("delete",C,r(A))},w=C=>{l.value=!0,i.value=C,Se(()=>O())},g=()=>{l.value||(i.value=-1),l.value=!1},x=(C,A)=>{const I=["imageFit","deletable","reupload","previewSize","beforeDelete"],P=he($e(e,I),$e(C,I,!0));return f(A_,Ce({item:C,index:A,onClick:()=>t(e.reupload?"clickReupload":"clickPreview",C,r(A)),onDelete:()=>b(C,A),onPreview:()=>v(C),onReupload:()=>w(A)},$e(e,["name","lazyLoad"]),P),$e(n,["preview-cover","preview-delete"]))},S=()=>{if(e.previewImage)return e.modelValue.map(x)},y=C=>t("clickUpload",C),T=()=>{const C=e.modelValue.length<+e.maxCount,A=e.readonly?null:f("input",{ref:o,type:"file",class:Ke("input"),accept:e.accept,capture:e.capture,multiple:e.multiple&&i.value===-1,disabled:e.disabled,onChange:d,onClick:g},null);return n.default?Je(f("div",{class:Ke("input-wrapper"),onClick:y},[n.default(),A]),[[at,C]]):Je(f("div",{class:Ke("upload",{readonly:e.readonly}),style:An(e.previewSize),onClick:y},[f(xe,{name:e.uploadIcon,class:Ke("upload-icon")},null),e.uploadText&&f("span",{class:Ke("upload-text")},[e.uploadText]),A]),[[at,e.showUpload&&C]])},O=()=>{o.value&&!e.disabled&&o.value.click()};return rn(()=>{a.forEach(C=>URL.revokeObjectURL(C))}),_e({chooseFile:O,reuploadFile:w,closeImagePreview:p}),Xn(()=>e.modelValue),()=>f("div",{class:Ke()},[f("div",{class:Ke("wrapper",{disabled:e.disabled})},[S(),T()])])}});const R_=Z(I_),[D_,Su]=Y("watermark"),B_={gapX:Ge(0),gapY:Ge(0),image:String,width:Ge(100),height:Ge(100),rotate:se(-22),zIndex:G,content:String,opacity:G,fullPage:H,textColor:J("#dcdee0")};var M_=W({name:D_,props:B_,setup(e,{slots:t}){const n=M(),o=M(""),a=M(""),i=()=>{const u={transformOrigin:"center",transform:`rotate(${e.rotate}deg)`},d=()=>e.image&&!t.content?f("image",{href:a.value,"xlink:href":a.value,x:"0",y:"0",width:e.width,height:e.height,style:u},null):f("foreignObject",{x:"0",y:"0",width:e.width,height:e.height},[f("div",{xmlns:"http://www.w3.org/1999/xhtml",style:u},[t.content?t.content():f("span",{style:{color:e.textColor}},[e.content])])]),h=e.width+e.gapX,m=e.height+e.gapY;return f("svg",{viewBox:`0 0 ${h} ${m}`,width:h,height:m,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",style:{padding:`0 ${e.gapX}px ${e.gapY}px 0`,opacity:e.opacity}},[d()])},l=u=>{const d=document.createElement("canvas"),h=new Image;h.crossOrigin="anonymous",h.referrerPolicy="no-referrer",h.onload=()=>{d.width=h.naturalWidth,d.height=h.naturalHeight;const m=d.getContext("2d");m==null||m.drawImage(h,0,0),a.value=d.toDataURL()},h.src=u},r=u=>{const d=new Blob([u],{type:"image/svg+xml"});return URL.createObjectURL(d)},s=()=>{o.value&&URL.revokeObjectURL(o.value)},c=()=>{n.value&&(s(),o.value=r(n.value.innerHTML))};return Yo(()=>{e.image&&l(e.image)}),te(()=>[e.content,e.textColor,e.height,e.width,e.rotate,e.gapX,e.gapY],c),te(a,()=>{Se(c)}),We(c),Uo(s),()=>{const u=he({backgroundImage:`url(${o.value})`},On(e.zIndex));return f("div",{class:Su({full:e.fullPage}),style:u},[f("div",{class:Su("wrapper"),ref:n},[i()])])}}});const L_=Z(M_),V_="4.9.20";function F_(e){[gf,fr,Ey,Ny,sp,Ip,Vf,Lp,vo,Hp,ut,ow,uw,vw,Yt,pw,is,Gf,_w,Rw,Lw,Hw,jw,Kw,Zw,nx,sx,br,bx,_x,$x,Dx,Fx,Ux,Yx,lh,wn,Xx,eS,Qr,aS,sS,fS,xe,il,SS,AS,OS,MS,Wt,sf,NS,WS,XS,aC,Sf,sC,fC,nl,hC,d1,Ut,v1,x1,as,ns,k1,B1,M1,z1,G1,Th,_h,lT,kT,Eh,AT,Ph,kh,DT,VT,jT,qT,GT,$f,e_,Zr,a_,Jr,ts,Da,r_,d_,el,al,v_,w_,Q0,T_,R_,L_].forEach(n=>{n.install?e.use(n):n.name&&e.component(n.name,n)})}var N_={install:F_,version:V_};const Mh=()=>navigator.userAgent.toLowerCase().includes("micromessenger"),z_=()=>{const e=navigator.userAgent.toLowerCase();return e.includes("iphone")||e.includes("ipad")?"ios":e.includes("android")?"android":"other"},Wl={disablePullToRefresh(){document.addEventListener("touchstart",function(t){t.touches.length>1&&t.preventDefault()});let e=0;document.addEventListener("touchend",function(t){const n=new Date().getTime();n-e<=300&&t.preventDefault(),e=n},!1)},preventFontScale(){typeof WeixinJSBridge=="object"&&typeof WeixinJSBridge.invoke=="function"&&(WeixinJSBridge.invoke("setFontSizeCallback",{fontSize:0}),WeixinJSBridge.on("menu:setfont",function(){WeixinJSBridge.invoke("setFontSizeCallback",{fontSize:0})}))},setWechatShare(e){typeof wx<"u"&&wx.ready(()=>{wx.onMenuShareTimeline({title:e.title||"阿泰会议服务",link:e.link||window.location.href,imgUrl:e.imgUrl||"",success:e.success||(()=>{}),cancel:e.cancel||(()=>{})}),wx.onMenuShareAppMessage({title:e.title||"阿泰会议服务",desc:e.desc||"快速提交会议服务需求",link:e.link||window.location.href,imgUrl:e.imgUrl||"",success:e.success||(()=>{}),cancel:e.cancel||(()=>{})})})},fixIOSInputBug(){z_()==="ios"&&Mh()&&(document.addEventListener("focusin",()=>{setTimeout(()=>{document.body.scrollTop=document.body.scrollHeight},100)}),document.addEventListener("focusout",()=>{setTimeout(()=>{window.scrollTo(0,0)},100)}))}},Cu={lazyLoadImages(){if("IntersectionObserver"in window){const e=new IntersectionObserver(t=>{t.forEach(n=>{if(n.isIntersecting){const o=n.target;o.src=o.dataset.src,o.classList.remove("lazy"),e.unobserve(o)}})});document.querySelectorAll("img[data-src]").forEach(t=>{e.observe(t)})}},preloadCriticalResources(){console.log("预加载功能已禁用，避免404错误")},debounce(e,t){let n;return function(...a){const i=()=>{clearTimeout(n),e(...a)};clearTimeout(n),n=setTimeout(i,t)}},throttle(e,t){let n;return function(){const o=arguments,a=this;n||(e.apply(a,o),n=!0,setTimeout(()=>n=!1,t))}}},H_={addHapticFeedback(e="light"){if(navigator.vibrate){const t={light:[10],medium:[20],heavy:[30]};navigator.vibrate(t[e]||t.light)}},createLoadingManager(){let e=0;return{show(){e++,e===1&&document.body.classList.add("loading")},hide(){e=Math.max(0,e-1),e===0&&document.body.classList.remove("loading")}}},networkStatusDetector(){const e=()=>{const t=navigator.onLine;document.body.classList.toggle("offline",!t),t||console.warn("网络连接已断开")};window.addEventListener("online",e),window.addEventListener("offline",e),e()}},j_=()=>{Mh()&&(Wl.disablePullToRefresh(),Wl.preventFontScale(),Wl.fixIOSInputBug()),Cu.preloadCriticalResources(),Cu.lazyLoadImages(),H_.networkStatusDetector()},W_=()=>En(()=>import("./Home-CN1eZbly.js"),__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),U_=()=>En(()=>import("./MultiServiceForm-DESGZTx4.js"),__vite__mapDeps([7,8,2,1,3,4,5,9]),import.meta.url),Y_=()=>En(()=>import("./MultiServiceSuccess-DobUl1Dw.js"),__vite__mapDeps([10,1,2,3,11]),import.meta.url),K_=()=>En(()=>import("./QRCode-DtF6vB9P.js"),__vite__mapDeps([12,13,8,2,14]),import.meta.url),q_=()=>En(()=>import("./Admin-B3aH8-aY.js"),__vite__mapDeps([15,8,1,2,3,4,5,16,17]),import.meta.url),G_=()=>En(()=>import("./RoomManagement-DlR6LsDb.js"),__vite__mapDeps([18,8,1,2,3,4,5,16,19]),import.meta.url),X_=()=>En(()=>import("./QRManagement-UOe5BI0g.js"),__vite__mapDeps([20,8,1,2,3,4,5,16,21]),import.meta.url),Z_=()=>En(()=>import("./RequestManagement-DUiR35py.js"),__vite__mapDeps([22,8,4,2,5,16,23]),import.meta.url),J_=()=>En(()=>import("./Settings-CCWDUgDf.js"),__vite__mapDeps([24,8,4,2,5,16,25]),import.meta.url),Q_=[{path:"/",name:"Home",component:W_},{path:"/form",redirect:"/multi-form"},{path:"/multi-form",name:"ServiceForm",component:U_},{path:"/multi-success",name:"MultiServiceSuccess",component:Y_},{path:"/qrcode",name:"QRCode",component:K_},{path:"/admin",name:"Admin",component:q_},{path:"/rooms",name:"RoomManagement",component:G_},{path:"/qr-management",name:"QRManagement",component:X_},{path:"/requests",name:"RequestManagement",component:Z_},{path:"/settings",name:"Settings",component:J_}],ek=vb({history:Wg(),routes:Q_}),fs=Nd(yb);fs.use(N_);fs.use(ek);j_();fs.mount("#app");export{nk as A,je as B,We as C,ik as D,ok as E,qe as F,Lt as G,Li as H,Se as I,lk as J,rk as K,Rx as L,En as _,Dd as a,f as b,xv as c,jm as d,he as e,zf as f,Ce as g,GS as h,At as i,Ht as j,Uo as k,Om as l,Hf as m,Je as n,Od as o,Lr as p,B as q,M as r,Pi as s,Qh as t,ak as u,at as v,te as w,Sv as x,Vi as y,tk as z};
