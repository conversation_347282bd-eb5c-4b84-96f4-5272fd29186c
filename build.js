#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建项目...');

try {
  // 检查是否存在 node_modules
  if (!fs.existsSync('node_modules')) {
    console.log('📦 安装依赖...');
    execSync('npm install', { stdio: 'inherit' });
  }

  // 构建项目
  console.log('🔨 构建项目...');
  execSync('npm run build', { stdio: 'inherit' });

  console.log('✅ 构建完成！');
  console.log('📁 构建产物位置: ./dist/');
  
  // 显示构建产物
  const distFiles = fs.readdirSync('./dist');
  console.log('构建文件列表:');
  distFiles.forEach(file => {
    console.log(`  - ${file}`);
  });

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}