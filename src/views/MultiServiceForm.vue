<template>
  <div class="multi-service-form">
    <van-nav-bar
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
    >
      <template #title>
        <AppLogo size="small" />
      </template>
    </van-nav-bar>
    
    <div class="content">
      <van-form @submit="onSubmit" ref="formRef">
        <!-- 会议室信息 -->
        <van-cell-group inset>
          <div class="meeting-room-section">
            <van-field
               v-model="formData.meetingRoom"
               name="meetingRoom"
               label="会议室编号"
               placeholder="请输入会议室编号，如A0101"
               clearable
               @input="onRoomNumberInput"
               :rules="[{ required: true, message: '请输入会议室编号' }]"
             >
               <template #button>
                 <van-button size="small" type="default" @click.stop="showMeetingRoomPicker = true" style="margin-right: 8px;">
                   选择
                 </van-button>
                 <van-button size="small" type="primary" @click.stop="startScan" icon="scan">
                   扫码
                 </van-button>
               </template>
             </van-field>

             <!-- 简化提示 -->
             <div class="room-tip" v-if="!formData.meetingRoom">
               <van-icon name="info-o" size="14" color="#969799" />
               <span>可点击"选择"或"扫码"快速获取会议室编号</span>
             </div>
          </div>
        </van-cell-group>

        <!-- 服务需求清单 -->
        <van-cell-group inset>
          <div class="service-checklist-section">
            <div class="checklist-header">
              <div class="section-title">
                <van-icon name="todo-list-o" size="16" />
                请选择需要的服务
              </div>
              <div class="selected-count">
                已选择 {{ getTotalSelectedCount() }} 项服务
              </div>
            </div>



            <!-- 快速选择区域 -->
            <div class="quick-select-section" v-if="getTotalSelectedCount() === 0">
              <div class="quick-select-header">
                <van-icon name="fire-o" size="16" color="#ff976a" />
                <span>常用服务</span>
              </div>
              <div class="quick-select-items">
                <div
                  v-for="item in getPopularServices()"
                  :key="`quick-${item.categoryValue}-${item.value}`"
                  class="quick-select-item"
                  @click="quickSelectService(item)"
                >
                  <van-icon :name="getCategoryIcon(item.categoryValue)" size="16" />
                  <span>{{ item.text }}</span>
                </div>
              </div>
            </div>

            <!-- 服务类别列表 -->
            <div class="service-categories">
              <div
                v-for="category in serviceCategories"
                :key="category.value"
                class="service-category enterprise-card haptic-feedback"
              >
                <!-- 类别标题 -->
                <div class="category-header" @click="toggleCategory(category, $event)">
                  <div class="category-info">
                    <van-icon :name="category.icon" size="20" />
                    <span class="category-name">{{ category.text }}</span>
                    <span class="category-count" v-if="getSelectedCountForCategory(category.value) > 0">
                      {{ getSelectedCountForCategory(category.value) }}
                    </span>
                    <van-tag v-if="category.popular" type="warning" size="small" class="popular-tag">
                      常用
                    </van-tag>
                  </div>
                  <div class="category-actions">
                    <van-checkbox
                      :model-value="isCategorySelected(category.value)"
                      @update:model-value="toggleAllInCategory(category.value, $event)"
                      @click.stop
                      size="18"
                    />
                    <van-icon
                      :name="category.expanded ? 'arrow-up' : 'arrow-down'"
                      size="16"
                      color="#969799"
                      class="expand-icon"
                    />
                  </div>
                </div>

                <!-- 具体服务项 -->
                <div class="service-items" v-if="category.expanded">
                  <div
                    v-for="item in category.items"
                    :key="item.value"
                    class="service-item-wrapper"
                  >
                    <div
                      class="service-item"
                      :class="{
                        'selected': isItemSelected(category.value, item.value),
                        'popular': item.popular
                      }"
                      @click="toggleServiceItem(category.value, item.value, !isItemSelected(category.value, item.value))"
                    >
                      <div class="item-content">
                        <van-checkbox
                          :model-value="isItemSelected(category.value, item.value)"
                          @update:model-value="toggleServiceItem(category.value, item.value, $event)"
                          @click.stop
                          size="18"
                        />
                        <span class="item-text">{{ getItemDisplayText(category.value, item) }}</span>
                        <van-tag v-if="item.popular" type="primary" size="small" class="item-popular-tag">
                          热门
                        </van-tag>
                      </div>
                      <div class="item-controls">
                        <!-- 茶水服务数量选择 -->
                        <div v-if="category.value === 'tea' && isItemSelected(category.value, item.value)" class="quantity-selector">
                          <van-stepper
                            :model-value="getItemQuantity(category.value, item.value)"
                            @update:model-value="updateItemQuantity(category.value, item.value, $event)"
                            min="1"
                            max="30"
                            integer
                            button-size="22"
                            @click.stop
                          />
                          <span class="quantity-unit">人</span>
                        </div>
                      </div>
                    </div>

                    <!-- 设备状态选择 - 移到wrapper内部，作为独立的块级元素 -->
                    <div
                      v-show="category.value === 'equipment' && item.hasStatus && isItemSelected(category.value, item.value)"
                      class="equipment-status"
                      :key="`status-${category.value}-${item.value}`"
                      @click.stop
                    >
                      <!-- 调试信息 -->
                      <div v-if="false" style="font-size: 10px; color: #999; margin-bottom: 4px;">
                        当前状态: {{ getItemStatus(category.value, item.value) }}
                      </div>
                      <van-radio-group
                        :model-value="getItemStatus(category.value, item.value)"
                        @update:model-value="updateItemStatus(category.value, item.value, $event)"
                        direction="horizontal"
                        @click.stop
                      >
                        <van-radio
                          v-for="statusOption in item.statusOptions"
                          :key="statusOption.value"
                          :name="statusOption.value"
                          @click.stop="handleRadioClick(category.value, item.value, statusOption.value, $event)"
                          @touchstart.stop="handleRadioClick(category.value, item.value, statusOption.value, $event)"
                        >
                          {{ statusOption.text }}
                        </van-radio>
                      </van-radio-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="contact-info-section">
            <div class="contact-category enterprise-card haptic-feedback">
              <!-- 联系信息标题 -->
              <div class="category-header" @click="toggleContactInfo">
                <div class="category-info">
                  <van-icon name="contact" size="20" />
                  <span class="category-name">联系信息</span>
                  <span class="category-count" v-if="hasContactInfo">
                    {{ getContactInfoCount() }}
                  </span>
                </div>
                <div class="category-actions">
                  <van-icon
                    :name="contactInfoExpanded ? 'arrow-up' : 'arrow-down'"
                    size="16"
                    color="#969799"
                    class="expand-icon"
                    :style="{ transform: contactInfoExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }"
                  />
                </div>
              </div>

              <!-- 联系信息表单 -->
              <div class="contact-items" v-show="contactInfoExpanded">
                <van-field
                  v-model="formData.contactName"
                  name="contactName"
                  label="联系人"
                  placeholder="请输入联系人姓名（选填）"
                  clearable
                />
                <van-field
                  v-model="formData.contactPhone"
                  name="contactPhone"
                  label="联系电话"
                  type="tel"
                  placeholder="请输入联系电话（选填）"
                  clearable
                  :rules="[
                    { validator: validators.phone, message: validationMessages.phone }
                  ]"
                />
              </div>
            </div>
          </div>

          <!-- 补充说明 -->
          <van-field
            v-model="formData.description"
            name="description"
            label="补充说明"
            type="textarea"
            placeholder="请详细描述您的需求（选填）"
            rows="3"
            autosize
            maxlength="200"
            show-word-limit
          />

          <!-- 紧急程度 -->
          <van-field name="urgency" label="紧急程度">
            <template #input>
              <van-radio-group v-model="formData.urgency" direction="horizontal">
                <van-radio name="normal">普通</van-radio>
                <van-radio name="urgent">紧急</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 已选服务预览 -->
        <van-cell-group inset v-if="getTotalSelectedCount() > 0">
          <van-cell title="已选服务" :value="`${getTotalSelectedCount()}项`" />
        </van-cell-group>

        <!-- 草稿操作 -->
        <van-cell-group inset v-if="hasDraft">
          <van-cell title="草稿操作">
            <template #right-icon>
              <van-button size="small" type="warning" @click="restoreDraft" style="margin-right: 8px;">
                恢复草稿
              </van-button>
              <van-button size="small" type="default" @click="clearDraft">
                清除草稿
              </van-button>
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            native-type="submit"
            block
            round
            :loading="submitting"
            loading-text="提交中..."
            :disabled="getTotalSelectedCount() === 0"
            class="enterprise-button haptic-feedback"
            size="large"
          >
            提交需求 ({{ getTotalSelectedCount() }}项)
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 会议室选择器 -->
    <RoomSelector
      v-model:show="showMeetingRoomPicker"
      :rooms="allMeetingRooms"
      @select="onRoomSelect"
    />

    <!-- 二维码扫描器 -->
    <QRScanner
      v-model:show="showQRScanner"
      @scan-success="handleScanSuccess"
      @scan-error="handleScanError"
      @switch-manual="handleSwitchManual"
    />

    <!-- 品牌标识 -->
    <BrandFooter variant="minimal" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast, showNotify } from 'vant'
import { submitServiceRequest, submitBatchServiceRequests, callCloudFunction } from '../utils/api'
import QRScanner from '../components/QRScanner.vue'
import RoomSelector from '../components/RoomSelector.vue'
import AppLogo from '../components/AppLogo.vue'
import BrandFooter from '../components/BrandFooter.vue'

// 防抖工具函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

const router = useRouter()
const route = useRoute()
const formRef = ref()
const submitting = ref(false)

// 统一验证函数
const validators = {
  // 手机号验证（选填字段）
  phone: (value) => {
    if (!value || !value.trim()) return true // 选填字段
    const phonePattern = /^1[3-9]\d{9}$/
    return phonePattern.test(value.trim())
  },

  // 会议室编号验证 - 支持多种格式（包括连字符）
  meetingRoom: (value) => {
    if (!value || !value.trim()) return false
    // 支持格式：A0101, B1234, A11-27, A110101, A1101等
    return /^[A-Z]+(\d{2,4}|-\d{1,4})$/.test(value.trim())
  },

  // 服务选择验证
  serviceSelection: (selectedServices) => {
    let total = 0
    selectedServices.forEach(categoryServices => {
      total += categoryServices.size
    })
    return total > 0
  }
}

// 验证错误信息
const validationMessages = {
  phone: '请输入正确的手机号码格式（如：13800138000）',
  meetingRoom: '请输入正确的会议室编号格式（如：A0815、A1101、A11-27、B12-01）',
  serviceSelection: '请至少选择一项服务'
}

// 错误处理工具函数
const errorHandler = {
  // 获取详细的错误信息
  getDetailedErrorMessage: (error) => {
    const errorMessages = {
      'NETWORK_ERROR': '网络连接失败，请检查网络设置后重试',
      'VALIDATION_ERROR': '表单数据验证失败，请检查输入内容',
      'SERVER_ERROR': '服务器暂时无法处理请求，请稍后重试',
      'AUTH_ERROR': '身份验证失败，请刷新页面后重试',
      'TIMEOUT_ERROR': '请求超时，请检查网络连接后重试',
      'DUPLICATE_ERROR': '检测到重复提交，请勿重复操作',
      'ROOM_NOT_FOUND': '会议室不存在，请检查会议室编号',
      'SERVICE_UNAVAILABLE': '服务暂时不可用，请稍后重试'
    }

    // 根据错误类型返回对应信息
    if (error.code && errorMessages[error.code]) {
      return errorMessages[error.code]
    }

    // 根据错误信息关键词匹配
    const message = error.message || error.toString()
    if (message.includes('网络')) return errorMessages.NETWORK_ERROR
    if (message.includes('超时')) return errorMessages.TIMEOUT_ERROR
    if (message.includes('重复')) return errorMessages.DUPLICATE_ERROR
    if (message.includes('会议室')) return errorMessages.ROOM_NOT_FOUND
    if (message.includes('服务')) return errorMessages.SERVICE_UNAVAILABLE

    return message || '操作失败，请重试'
  },

  // 显示错误信息
  showError: (error, options = {}) => {
    const message = errorHandler.getDetailedErrorMessage(error)
    const defaultOptions = {
      type: 'fail',
      message: message,
      duration: 4000,
      ...options
    }

    showToast(defaultOptions)

    // 记录错误日志
    console.error('操作失败:', {
      error,
      message,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })
  },

  // 显示警告信息
  showWarning: (message, options = {}) => {
    const defaultOptions = {
      type: 'warning',
      message: message,
      duration: 3000,
      ...options
    }

    showToast(defaultOptions)
  },

  // 显示成功信息
  showSuccess: (message, options = {}) => {
    const defaultOptions = {
      type: 'success',
      message: message,
      duration: 2000,
      ...options
    }

    showToast(defaultOptions)
  }
}

// 统一的服务状态管理器
const createServiceStateManager = () => {
  const state = reactive({
    selectedServices: new Map(),
    serviceQuantities: new Map(),
    serviceStatuses: new Map()
  })

  const generateKey = (categoryValue, itemValue) => `${categoryValue}_${itemValue}`

  const addService = (categoryValue, itemValue) => {
    if (!state.selectedServices.has(categoryValue)) {
      state.selectedServices.set(categoryValue, new Set())
    }

    const categoryServices = state.selectedServices.get(categoryValue)
    categoryServices.add(itemValue)

    // 为茶水服务设置默认数量
    if (categoryValue === 'tea') {
      const key = generateKey(categoryValue, itemValue)
      if (!state.serviceQuantities.has(key)) {
        state.serviceQuantities.set(key, 1)
      }
    }

    // 为设备服务设置默认状态
    if (categoryValue === 'equipment') {
      const key = generateKey(categoryValue, itemValue)
      if (!state.serviceStatuses.has(key)) {
        // 设置为null，让getItemStatus函数处理默认值
        state.serviceStatuses.set(key, null)
      }
    }
  }

  const removeService = (categoryValue, itemValue) => {
    const categoryServices = state.selectedServices.get(categoryValue)
    if (categoryServices) {
      categoryServices.delete(itemValue)
      if (categoryServices.size === 0) {
        state.selectedServices.delete(categoryValue)
      }
    }

    // 清除相关数据
    const key = generateKey(categoryValue, itemValue)
    state.serviceQuantities.delete(key)
    state.serviceStatuses.delete(key)
  }

  const isServiceSelected = (categoryValue, itemValue) => {
    const categoryServices = state.selectedServices.get(categoryValue)
    return categoryServices ? categoryServices.has(itemValue) : false
  }

  const getServiceQuantity = (categoryValue, itemValue) => {
    const key = generateKey(categoryValue, itemValue)
    return state.serviceQuantities.get(key) || 1
  }

  const setServiceQuantity = (categoryValue, itemValue, quantity) => {
    const key = generateKey(categoryValue, itemValue)
    state.serviceQuantities.set(key, quantity)
  }

  const getServiceStatus = (categoryValue, itemValue) => {
    const key = generateKey(categoryValue, itemValue)
    const currentStatus = state.serviceStatuses.get(key)

    if (currentStatus) {
      return currentStatus
    }

    // 返回一个通用的默认值，具体的默认值将在组件中处理
    return null
  }

  const setServiceStatus = (categoryValue, itemValue, status) => {
    const key = generateKey(categoryValue, itemValue)
    state.serviceStatuses.set(key, status)
  }

  const getTotalCount = () => {
    let total = 0
    state.selectedServices.forEach(categoryServices => {
      total += categoryServices.size
    })
    return total
  }

  const clearAll = () => {
    state.selectedServices.clear()
    state.serviceQuantities.clear()
    state.serviceStatuses.clear()
  }

  return {
    state,
    addService,
    removeService,
    isServiceSelected,
    getServiceQuantity,
    setServiceQuantity,
    getServiceStatus,
    setServiceStatus,
    getTotalCount,
    clearAll
  }
}

// 创建服务状态管理器实例
const serviceManager = createServiceStateManager()

// 统一的触觉反馈管理器
const tactileFeedback = {
  // 触觉反馈类型
  types: {
    light: 30,    // 轻微反馈
    medium: 50,   // 中等反馈
    heavy: 100,   // 强烈反馈
    success: [30, 50], // 成功反馈（两次震动）
    error: [100, 50, 100] // 错误反馈（三次震动）
  },

  // 提供触觉反馈
  provide: (type = 'light') => {
    if (!navigator.vibrate) return

    const pattern = tactileFeedback.types[type]
    if (Array.isArray(pattern)) {
      navigator.vibrate(pattern)
    } else {
      navigator.vibrate(pattern || tactileFeedback.types.light)
    }
  },

  // 检查是否支持触觉反馈
  isSupported: () => {
    return 'vibrate' in navigator
  }
}

// 草稿保存功能
const draftManager = {
  DRAFT_KEY: 'serviceFormDraft',

  // 保存草稿
  saveDraft: () => {
    try {
      const draft = {
        meetingRoom: formData.meetingRoom,
        selectedServices: Array.from(serviceManager.state.selectedServices.entries()).map(([key, value]) => [key, Array.from(value)]),
        serviceQuantities: Array.from(serviceManager.state.serviceQuantities.entries()),
        serviceStatuses: Array.from(serviceManager.state.serviceStatuses.entries()),
        contactName: formData.contactName,
        contactPhone: formData.contactPhone,
        description: formData.description,
        urgency: formData.urgency,
        timestamp: Date.now()
      }

      localStorage.setItem(draftManager.DRAFT_KEY, JSON.stringify(draft))
      console.log('草稿已保存')
    } catch (error) {
      console.error('保存草稿失败:', error)
    }
  },

  // 加载草稿
  loadDraft: () => {
    try {
      const draftStr = localStorage.getItem(draftManager.DRAFT_KEY)
      if (!draftStr) return null

      const draft = JSON.parse(draftStr)

      // 检查草稿是否过期（24小时）
      const isExpired = Date.now() - draft.timestamp > 24 * 60 * 60 * 1000
      if (isExpired) {
        draftManager.clearDraft()
        return null
      }

      return draft
    } catch (error) {
      console.error('加载草稿失败:', error)
      draftManager.clearDraft()
      return null
    }
  },

  // 恢复草稿
  restoreDraft: (draft) => {
    try {
      // 恢复基本表单数据
      formData.meetingRoom = draft.meetingRoom || ''
      formData.contactName = draft.contactName || ''
      formData.contactPhone = draft.contactPhone || ''
      formData.description = draft.description || ''
      formData.urgency = draft.urgency || 'normal'

      // 恢复服务选择状态
      if (draft.selectedServices) {
        serviceManager.state.selectedServices.clear()
        draft.selectedServices.forEach(([categoryValue, items]) => {
          serviceManager.state.selectedServices.set(categoryValue, new Set(items))
        })
      }

      // 恢复服务数量
      if (draft.serviceQuantities) {
        serviceManager.state.serviceQuantities.clear()
        draft.serviceQuantities.forEach(([key, value]) => {
          serviceManager.state.serviceQuantities.set(key, value)
        })
      }

      // 恢复设备状态
      if (draft.serviceStatuses) {
        serviceManager.state.serviceStatuses.clear()
        draft.serviceStatuses.forEach(([key, value]) => {
          serviceManager.state.serviceStatuses.set(key, value)
        })
      }

      console.log('草稿已恢复')
      return true
    } catch (error) {
      console.error('恢复草稿失败:', error)
      return false
    }
  },

  // 清除草稿
  clearDraft: () => {
    try {
      localStorage.removeItem(draftManager.DRAFT_KEY)
      console.log('草稿已清除')
    } catch (error) {
      console.error('清除草稿失败:', error)
    }
  },

  // 检查是否有草稿
  hasDraft: () => {
    const draft = draftManager.loadDraft()
    return draft !== null
  }
}

// 表单数据
const formData = reactive({
  meetingRoom: '',
  contactName: '', // 联系人姓名
  contactPhone: '', // 联系电话
  description: '',
  urgency: 'normal'
})

// 选择器状态
const showMeetingRoomPicker = ref(false)
const showQRScanner = ref(false)

// 联系信息展开状态
const contactInfoExpanded = ref(false)

// 动态会议室列表
const allMeetingRooms = ref([])
const quickRooms = ref([])

// 服务类别配置（支持展开/收起，默认折叠以减少视觉复杂度）
const serviceCategories = ref([
  {
    text: '茶水服务',
    value: 'tea',
    icon: 'hot-o',
    expanded: false,
    popular: true, // 标记为常用服务
    items: [
      { text: '更换茶水', value: 'replace_tea', popular: true },
      { text: '增加茶水', value: 'add_tea', popular: true },
      { text: '清洁茶具', value: 'clean_tea_set' }
    ]
  },
  {
    text: '空调服务',
    value: 'air_conditioning',
    icon: 'fire-o',
    expanded: false,
    popular: true, // 标记为常用服务
    items: [
      { text: '降温', value: 'cool_down', popular: true },
      { text: '关闭', value: 'turn_off' },
      { text: '调节风速', value: 'adjust_fan' }
    ]
  },
  {
    text: '设备服务',
    value: 'equipment',
    icon: 'desktop-o',
    expanded: false,
    items: [
      {
        text: '投影设备问题',
        value: 'projector_issue',
        hasStatus: true,
        popular: true,
        statusOptions: [
          { text: '投影设备无法启动', value: 'no_start' },
          { text: '投影画面异常', value: 'display_abnormal' },
          { text: '投影设备故障', value: 'malfunction' }
        ]
      },
      {
        text: '音响设备问题',
        value: 'audio_issue',
        hasStatus: true,
        popular: true,
        statusOptions: [
          { text: '音响无声音', value: 'no_sound' },
          { text: '音响杂音/噪音', value: 'noise' },
          { text: '音响设备故障', value: 'malfunction' }
        ]
      },
      {
        text: '麦克风故障',
        value: 'microphone_issue',
        hasStatus: true,
        statusOptions: [
          { text: '麦克风无声音', value: 'no_sound' },
          { text: '麦克风杂音', value: 'noise' },
          { text: '麦克风损坏', value: 'damaged' }
        ]
      },
      {
        text: '网络连接问题',
        value: 'network_issue',
        hasStatus: true,
        statusOptions: [
          { text: '网络无法连接', value: 'no_connection' },
          { text: '网络连接不稳定', value: 'unstable' },
          { text: '网络速度慢', value: 'slow_speed' }
        ]
      },
      {
        text: '电脑设备问题',
        value: 'computer_issue',
        hasStatus: true,
        statusOptions: [
          { text: '电脑无法启动', value: 'no_start' },
          { text: '电脑运行缓慢', value: 'slow_running' },
          { text: '电脑系统故障', value: 'system_error' }
        ]
      },
      {
        text: '显示屏问题',
        value: 'display_issue',
        hasStatus: true,
        statusOptions: [
          { text: '显示屏无显示', value: 'no_display' },
          { text: '显示屏花屏/闪烁', value: 'screen_flicker' },
          { text: '显示屏故障', value: 'malfunction' }
        ]
      }
    ]
  }
])

// 加载会议室列表
const loadMeetingRooms = async () => {
  try {
    const result = await callCloudFunction('roomManagement', {
      action: 'getRooms'
    })

    if (result.code === 0 && result.data && result.data.length > 0) {
      allMeetingRooms.value = result.data.map(room => ({
        number: room.roomNumber || room.number,
        name: room.name || room.roomNumber || room.number,
        building: room.building || (room.roomNumber || room.number)?.match(/^([A-Z]+)/)?.[1] || '',
        floor: room.floor || ''
      }))
      quickRooms.value = allMeetingRooms.value.slice(0, 8).map(room => room.number)
    } else {
      showToast('请先在管理后台添加会议室数据')
      allMeetingRooms.value = []
      quickRooms.value = []
    }
  } catch (error) {
    console.error('加载会议室列表失败:', error)
    errorHandler.showError(error, {
      message: '加载会议室列表失败，请检查网络连接'
    })
    allMeetingRooms.value = []
    quickRooms.value = []
  }
}

// 服务选择相关方法
const toggleCategory = (category, event) => {
  // 阻止默认行为，避免页面跳转
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  category.expanded = !category.expanded
}

const isItemSelected = (categoryValue, itemValue) => {
  return serviceManager.isServiceSelected(categoryValue, itemValue)
}

const toggleServiceItem = (categoryValue, itemValue, selected) => {
  if (selected) {
    serviceManager.addService(categoryValue, itemValue)
    // 提供触觉反馈
    tactileFeedback.provide('light')
  } else {
    serviceManager.removeService(categoryValue, itemValue)
    // 提供轻微反馈表示取消选择
    tactileFeedback.provide('light')
  }
}

const isCategorySelected = (categoryValue) => {
  const categoryServices = serviceManager.state.selectedServices.get(categoryValue)
  return categoryServices && categoryServices.size > 0
}

const getSelectedCountForCategory = (categoryValue) => {
  const categoryServices = serviceManager.state.selectedServices.get(categoryValue)
  return categoryServices ? categoryServices.size : 0
}

// 获取常用服务列表（使用computed缓存）
const getPopularServices = () => {
  return popularServices.value
}

// 获取类别图标
const getCategoryIcon = (categoryValue) => {
  const category = serviceCategories.value.find(cat => cat.value === categoryValue)
  return category ? category.icon : 'service-o'
}

// 快速选择服务
const quickSelectService = (item) => {
  // 自动展开对应的类别
  const category = serviceCategories.value.find(cat => cat.value === item.categoryValue)
  if (category) {
    category.expanded = true
  }

  // 选择该服务
  toggleServiceItem(item.categoryValue, item.value, true)

  // 提供中等强度触觉反馈
  tactileFeedback.provide('medium')

  // 显示选择反馈
  errorHandler.showSuccess(`已选择：${item.text}`, { duration: 1000 })
}

const toggleAllInCategory = (categoryValue, selected) => {
  const category = serviceCategories.value.find(cat => cat.value === categoryValue)
  if (!category) return

  if (selected) {
    // 选中该类别下的所有服务
    category.items.forEach(item => {
      serviceManager.addService(categoryValue, item.value)
    })
  } else {
    // 取消选中该类别下的所有服务
    category.items.forEach(item => {
      serviceManager.removeService(categoryValue, item.value)
    })
  }
}

// 使用computed缓存计算结果
const totalSelectedCount = computed(() => {
  return serviceManager.getTotalCount()
})

const getTotalSelectedCount = () => {
  return totalSelectedCount.value
}

// 缓存常用服务列表
const popularServices = computed(() => {
  const services = []
  serviceCategories.value.forEach(category => {
    category.items.forEach(item => {
      if (item.popular) {
        services.push({
          ...item,
          categoryValue: category.value,
          categoryText: category.text
        })
      }
    })
  })
  return services.slice(0, 6) // 最多显示6个常用服务
})

// 缓存选中的服务列表
const selectedServicesList = computed(() => {
  const result = []
  serviceCategories.value.forEach(category => {
    const categoryServices = serviceManager.state.selectedServices.get(category.value)
    if (categoryServices && categoryServices.size > 0) {
      const selectedItems = category.items.filter(item =>
        categoryServices.has(item.value)
      )
      result.push({
        ...category,
        selectedItems
      })
    }
  })
  return result
})

// 检查是否有草稿
const hasDraft = computed(() => {
  return draftManager.hasDraft()
})

// 创建设备状态的响应式计算属性
const equipmentStatuses = computed(() => {
  const statuses = new Map()

  serviceCategories.value.forEach(category => {
    if (category.value === 'equipment') {
      category.items.forEach(item => {
        if (item.hasStatus && isItemSelected(category.value, item.value)) {
          const key = `${category.value}_${item.value}`
          const currentStatus = serviceManager.getServiceStatus(category.value, item.value)

          if (currentStatus) {
            statuses.set(key, currentStatus)
          } else if (item.statusOptions && item.statusOptions.length > 0) {
            // 设置默认状态
            const defaultStatus = item.statusOptions[0].value
            serviceManager.setServiceStatus(category.value, item.value, defaultStatus)
            statuses.set(key, defaultStatus)
          }
        }
      })
    }
  })

  return statuses
})

// 获取服务项显示文本（使用重构后的接口）
const getItemDisplayText = (categoryValue, item) => {
  return serviceItemOperations.getDisplayText(categoryValue, item)
}

// 服务项操作的统一接口
const serviceItemOperations = {
  // 获取服务项数量
  getQuantity: (categoryValue, itemValue) => {
    return serviceManager.getServiceQuantity(categoryValue, itemValue)
  },

  // 更新服务项数量
  setQuantity: (categoryValue, itemValue, quantity) => {
    serviceManager.setServiceQuantity(categoryValue, itemValue, quantity)
    tactileFeedback.provide('light')
  },

  // 获取设备状态
  getStatus: (categoryValue, itemValue) => {
    return serviceManager.getServiceStatus(categoryValue, itemValue)
  },

  // 更新设备状态
  setStatus: (categoryValue, itemValue, status) => {
    serviceManager.setServiceStatus(categoryValue, itemValue, status)
    tactileFeedback.provide('light')
  },

  // 获取显示文本
  getDisplayText: (categoryValue, item) => {
    if (categoryValue === 'tea' && isItemSelected(categoryValue, item.value)) {
      const quantity = serviceItemOperations.getQuantity(categoryValue, item.value)
      return `${item.text} x ${quantity}人`
    }
    return item.text
  }
}

// 获取设备状态（包含默认值处理）
const getItemStatus = (categoryValue, itemValue) => {
  // 首先尝试从状态管理器获取
  const currentStatus = serviceManager.getServiceStatus(categoryValue, itemValue)

  if (currentStatus) {
    return currentStatus
  }

  // 如果是设备服务，使用computed属性确保响应式
  if (categoryValue === 'equipment') {
    const key = `${categoryValue}_${itemValue}`
    const computedStatus = equipmentStatuses.value.get(key)
    if (computedStatus) {
      return computedStatus
    }

    // 最后的兜底逻辑
    const category = serviceCategories.value.find(cat => cat.value === categoryValue)
    if (category) {
      const item = category.items.find(item => item.value === itemValue)
      if (item && item.statusOptions && item.statusOptions.length > 0) {
        const defaultStatus = item.statusOptions[0].value
        serviceManager.setServiceStatus(categoryValue, itemValue, defaultStatus)
        return defaultStatus
      }
    }
  }

  return 'startup' // 兜底默认值
}

// 向后兼容的函数（保持原有API）
const getItemQuantity = serviceItemOperations.getQuantity
const updateItemQuantity = serviceItemOperations.setQuantity

// 优化的设备状态更新函数，增加调试信息
const updateItemStatus = (categoryValue, itemValue, status) => {
  console.log('updateItemStatus called:', { categoryValue, itemValue, status })

  try {
    serviceManager.setServiceStatus(categoryValue, itemValue, status)
    tactileFeedback.provide('light')

    // 验证状态是否正确设置
    const verifyStatus = serviceManager.getServiceStatus(categoryValue, itemValue)
    console.log('Status verification:', { expected: status, actual: verifyStatus })

    if (verifyStatus !== status) {
      console.warn('Status update failed - mismatch detected')
    }
  } catch (error) {
    console.error('Error updating item status:', error)
  }
}

// 原生点击事件处理（备用方案）
const handleRadioClick = (categoryValue, itemValue, status, event) => {
  console.log('handleRadioClick called:', { categoryValue, itemValue, status })
  event.stopPropagation()
  event.preventDefault()

  // 直接调用状态更新
  updateItemStatus(categoryValue, itemValue, status)

  // 强制触发响应式更新
  nextTick(() => {
    console.log('Force update completed')
  })
}

const getSelectedServices = () => {
  return selectedServicesList.value
}

// 扫码功能
const startScan = () => {
  showQRScanner.value = true
}

const handleScanSuccess = (scanResult) => {
  const { roomNumber, building, floor } = scanResult
  if (roomNumber) {
    formData.meetingRoom = roomNumber
    if (building && floor) {
      showNotify({
        type: 'success',
        message: `扫码成功！获取到会议室：${roomNumber}（${building}栋${floor}楼）`
      })
    } else {
      showNotify({
        type: 'success',
        message: `扫码成功！获取到会议室：${roomNumber}`
      })
    }
  }
}

const handleScanError = (error) => {
  showToast({
    type: 'fail',
    message: error
  })
}

const handleSwitchManual = () => {
  showMeetingRoomPicker.value = true
}

// 事件处理
const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}

const onRoomSelect = (room) => {
  formData.meetingRoom = room.number
  showMeetingRoomPicker.value = false
}

const selectQuickRoom = (room) => {
  formData.meetingRoom = room
  tactileFeedback.provide('light')
  errorHandler.showSuccess('已选择会议室：' + room)
}

// 防抖处理会议室编号输入
const debouncedRoomInput = debounce((value) => {
  formData.meetingRoom = value.toUpperCase().replace(/[^A-Z0-9]/g, '')
}, 300)

const onRoomNumberInput = (value) => {
  debouncedRoomInput(value)
}

// 提交表单
const onSubmit = async () => {
  // 统一验证逻辑
  if (!validators.serviceSelection(serviceManager.state.selectedServices)) {
    showToast(validationMessages.serviceSelection)
    return
  }

  // 验证手机号格式（仅在用户输入时验证）
  if (!validators.phone(formData.contactPhone)) {
    showToast(validationMessages.phone)
    return
  }

  try {
    submitting.value = true
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
      duration: 0
    })

    // 构建多服务请求数据
    const selectedServices = getSelectedServices()
    const serviceRequests = []
    const batchId = Date.now().toString() // 统一的批次ID

    selectedServices.forEach(category => {
      category.selectedItems.forEach(item => {
        let serviceDetail = item.text
        let additionalInfo = {}

        // 处理茶水服务数量
        if (category.value === 'tea') {
          const quantity = getItemQuantity(category.value, item.value)
          serviceDetail = `${item.text} x ${quantity}人`
          additionalInfo.quantity = quantity
        }

        // 处理设备状态
        if (category.value === 'equipment' && item.hasStatus) {
          const status = getItemStatus(category.value, item.value)

          // 根据设备类型和状态值获取对应的状态文本
          let statusText = status
          if (item.statusOptions) {
            const statusOption = item.statusOptions.find(option => option.value === status)
            if (statusOption) {
              statusText = statusOption.text
            }
          }

          serviceDetail = `${item.text} - ${statusText}`
          additionalInfo.equipmentStatus = status
        }

        serviceRequests.push({
          meetingName: formData.meetingRoom,
          meetingRoom: formData.meetingRoom,
          serviceType: category.text,
          serviceDetail: serviceDetail,
          contactName: formData.contactName,
          contactPhone: formData.contactPhone,
          description: formData.description,
          urgency: formData.urgency,
          location: formData.meetingRoom,
          createTime: new Date().toISOString(),
          batchId: batchId, // 使用统一的批次ID
          ...additionalInfo // 包含额外信息
        })
      })
    })

    // 使用优化的批量提交接口（只发送一条企业微信消息）
    const result = await submitBatchServiceRequests(serviceRequests, batchId)

    closeToast()

    // 提供成功反馈
    tactileFeedback.provide('success')
    errorHandler.showSuccess(`成功提交${serviceRequests.length}项服务需求！`)

    // 清除草稿
    draftManager.clearDraft()

    // 跳转到成功页面
    router.push({
      name: 'MultiServiceSuccess',
      query: {
        meetingRoom: formData.meetingRoom,
        serviceCount: serviceRequests.length,
        batchId: batchId,
        services: JSON.stringify(selectedServices.map(cat => ({
          category: cat.text,
          items: cat.selectedItems.map(item => item.text)
        })))
      }
    })
  } catch (error) {
    closeToast()
    errorHandler.showError(error, {
      message: error.message || '提交失败，请重试'
    })
  } finally {
    submitting.value = false
  }
}

// 路由参数处理函数
const parseRouteParams = () => {
  // 支持多种路由参数格式
  const roomParam = route.query.room ||
                   route.query.roomNumber ||
                   route.query.meetingRoom ||
                   route.query.roomId ||
                   route.params.room ||
                   route.params.roomNumber

  if (roomParam) {
    // 标准化会议室编号格式
    const normalizedRoom = roomParam.toString().toUpperCase().trim()

    // 验证格式并设置 - 支持多种格式（包括连字符）
    if (/^[A-Z]+(\d{2,4}|-\d{1,4})$/.test(normalizedRoom)) {
      formData.meetingRoom = normalizedRoom
      showToast({
        type: 'success',
        message: `已自动选择会议室：${normalizedRoom}`,
        duration: 2000
      })
    } else {
      // 如果格式不标准，仍然设置但给出提示
      formData.meetingRoom = normalizedRoom
      showToast({
        type: 'warning',
        message: '会议室编号格式可能不正确，请检查',
        duration: 3000
      })
    }
  }

  // 处理其他可能的参数
  if (route.query.service || route.query.serviceType) {
    const serviceParam = route.query.service || route.query.serviceType
    // 可以根据服务类型自动展开对应类别
    autoExpandServiceCategory(serviceParam)
  }
}

// 自动展开服务类别
const autoExpandServiceCategory = (serviceType) => {
  const serviceTypeMap = {
    'tea': 'tea',
    'water': 'tea',
    'air': 'air_conditioning',
    'ac': 'air_conditioning',
    'equipment': 'equipment',
    'device': 'equipment'
  }

  const categoryValue = serviceTypeMap[serviceType.toLowerCase()]
  if (categoryValue) {
    const category = serviceCategories.value.find(cat => cat.value === categoryValue)
    if (category) {
      category.expanded = true
      showToast({
        type: 'success',
        message: `已为您展开${category.text}类别`,
        duration: 2000
      })
    }
  }
}

// 页面初始化
onMounted(async () => {
  try {
    console.log('MultiServiceForm mounted')
    console.log('serviceCategories:', serviceCategories.value)

    // 加载会议室列表
    await loadMeetingRooms()

    // 处理路由参数
    parseRouteParams()

    // 检查并提示恢复草稿
    if (draftManager.hasDraft()) {
      showToast({
        type: 'warning',
        message: '检测到未完成的表单，点击恢复按钮可恢复之前的填写内容',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
    errorHandler.showError(error, {
      message: '页面初始化失败，请刷新页面重试'
    })
  }
})

// 监听表单变化，自动保存草稿
watch([
  () => formData.meetingRoom,
  () => formData.contactName,
  () => formData.contactPhone,
  () => formData.description,
  () => formData.urgency,
  () => serviceManager.state.selectedServices,
  () => serviceManager.state.serviceQuantities,
  () => serviceManager.state.serviceStatuses
], () => {
  // 防抖保存草稿
  clearTimeout(draftSaveTimer.value)
  draftSaveTimer.value = setTimeout(() => {
    if (getTotalSelectedCount() > 0 || formData.meetingRoom) {
      draftManager.saveDraft()
    }
  }, 2000) // 2秒后保存
}, { deep: true })

const draftSaveTimer = ref(null)

// 恢复草稿
const restoreDraft = () => {
  try {
    const draft = draftManager.loadDraft()
    if (draft) {
      const success = draftManager.restoreDraft(draft)
      if (success) {
        errorHandler.showSuccess('草稿已恢复，请检查填写内容')
        // 提供触觉反馈
        tactileFeedback.provide('success')
      } else {
        errorHandler.showError(new Error('恢复草稿失败'))
      }
    } else {
      errorHandler.showWarning('没有找到可恢复的草稿')
    }
  } catch (error) {
    console.error('恢复草稿失败:', error)
    errorHandler.showError(error, {
      message: '恢复草稿失败，请重试'
    })
  }
}

// 联系信息相关函数
const toggleContactInfo = () => {
  contactInfoExpanded.value = !contactInfoExpanded.value
  tactileFeedback.provide('light')
}

// 检查是否有联系信息
const hasContactInfo = computed(() => {
  return !!(formData.contactName?.trim() || formData.contactPhone?.trim())
})

// 获取联系信息数量
const getContactInfoCount = () => {
  let count = 0
  if (formData.contactName?.trim()) count++
  if (formData.contactPhone?.trim()) count++
  return count
}

// 清除草稿
const clearDraft = () => {
  try {
    draftManager.clearDraft()
    errorHandler.showSuccess('草稿已清除')
    tactileFeedback.provide('light')
  } catch (error) {
    console.error('清除草稿失败:', error)
    errorHandler.showError(error, {
      message: '清除草稿失败，请重试'
    })
  }
}
</script>

<style scoped>
.multi-service-form {
  min-height: 100vh;
  background: var(--gradient-primary);
  padding-top: calc(46px + env(safe-area-inset-top));
  padding-bottom: env(safe-area-inset-bottom);
  /* 强制浅色模式以确保生产环境稳定性 */
  color-scheme: light;
}

.content {
  padding: var(--spacing-lg) var(--spacing-lg) calc(100px + env(safe-area-inset-bottom));
}

/* 会议室选择区域 */
.meeting-room-section {
  padding: 0;
}

.room-tip {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  background: var(--color-background-light);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* 服务清单区域 */
.service-checklist-section {
  padding: 0;
}

/* 联系信息区域 */
.contact-info-section {
  margin-top: var(--spacing-base);
}

.contact-category {
  background: #ffffff;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid #ebedf0;
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-base) var(--ease-out);
}

.contact-category:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-1px);
}

.contact-items {
  padding: var(--spacing-sm) 0;
  transition: all var(--duration-base) var(--ease-out);
}

.contact-items .van-field {
  background: #ffffff;
  border-bottom: 1px solid #f5f5f5;
}

.contact-items .van-field:last-child {
  border-bottom: none;
}

.checklist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  background: var(--color-background-light);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--shadow-sm);
}

/* 快速选择区域 */
.quick-select-section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-base);
  background: linear-gradient(135deg, #fff7ed 0%, #fef3e2 100%);
  border-radius: var(--radius-lg);
  border: 1px solid #fed7aa;
}

.quick-select-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: #ea580c;
}

.quick-select-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.quick-select-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-base);
  background: #ffffff;
  border: 1px solid #fed7aa;
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all var(--duration-base) var(--ease-out);
  font-size: var(--font-size-sm);
  color: #9a3412;
  min-height: 40px;
}

.quick-select-item:hover {
  background: #fef3e2;
  border-color: #fb923c;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(251, 146, 60, 0.2);
}

.quick-select-item:active {
  transform: translateY(0);
  background: #fed7aa;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.selected-count {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-medium);
  min-width: 24px;
  text-align: center;
}

.service-categories {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.service-category {
  background: #ffffff; /* 强制白色背景 */
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid #ebedf0; /* 强制浅色边框 */
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-base) var(--ease-out);
}

.service-category:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-1px);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  background: #fafbfc; /* 强制浅色背景 */
  border-bottom: 1px solid #ebedf0; /* 强制浅色边框 */
  cursor: pointer;
  transition: all var(--duration-base) var(--ease-out);
  min-height: 56px; /* 确保触摸目标足够大 */
}

.category-header:hover {
  background: var(--color-primary-light);
}

.category-header:active {
  background: var(--color-primary-light);
  transform: scale(0.99);
}

.category-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.expand-icon {
  transition: transform var(--duration-base) var(--ease-out);
}

.popular-tag {
  margin-left: var(--spacing-xs);
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.category-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.category-count {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 2px var(--spacing-sm);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  min-width: 20px;
  text-align: center;
}

.service-items {
  padding: var(--spacing-sm) 0;
}

.service-item-wrapper {
  /* 包装器确保每个服务项和其状态选择器作为一个整体 */
  margin-bottom: 0;
}

.service-item {
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1px solid #f5f5f5; /* 强制浅色边框 */
  background: #ffffff; /* 强制白色背景 */
  transition: all var(--duration-base) var(--ease-out);
  min-height: 48px; /* 确保触摸目标足够大 */
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-item.selected {
  background: #f0f9ff;
  border-left: 3px solid var(--color-primary);
}

.service-item.popular {
  position: relative;
}

.service-item .item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.service-item .item-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.item-popular-tag {
  margin-left: var(--spacing-xs);
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.quantity-unit {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.equipment-status {
  margin: var(--spacing-sm) var(--spacing-lg) var(--spacing-base) var(--spacing-lg); /* 上右下左 */
  padding: var(--spacing-sm) var(--spacing-base);
  background: var(--color-background-light);
  border-radius: var(--radius-base);
  border-left: 3px solid var(--color-primary);
  transition: all 0.3s ease-in-out; /* 平滑的展开动画 */
  min-height: 60px; /* 确保稳定的高度 */
  display: flex;
  align-items: center;
  /* 正常的块级元素，占用文档流空间 */
  pointer-events: auto; /* 确保可以接收点击事件 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 轻微阴影增强层次感 */
}

.equipment-status .van-radio-group {
  width: 100%;
  pointer-events: auto; /* 确保可以接收点击事件 */
}

.equipment-status .van-radio {
  margin-right: var(--spacing-sm);
  flex: 1;
  min-width: 0; /* 允许文字换行 */
  pointer-events: auto; /* 确保可以接收点击事件 */
  cursor: pointer; /* 显示手型光标 */
}

.equipment-status .van-radio__label {
  font-size: var(--font-size-sm);
  line-height: 1.4;
  word-break: break-word;
  pointer-events: auto; /* 确保可以接收点击事件 */
  cursor: pointer; /* 显示手型光标 */
}

.equipment-status .van-radio__icon {
  pointer-events: auto; /* 确保可以接收点击事件 */
  cursor: pointer; /* 显示手型光标 */
}

.service-item:last-child {
  border-bottom: none;
}

.service-item:hover {
  background: var(--color-background-light);
}

.service-item.selected:hover {
  background: #e0f2fe;
}

.service-item:active {
  background: var(--color-primary-light);
  transform: translateX(2px);
}

.item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.item-text {
  font-size: var(--font-size-base);
  color: #323233; /* 强制深色文字 */
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}



/* 提交按钮区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-base) var(--spacing-base);
  padding-bottom: calc(var(--spacing-base) + env(safe-area-inset-bottom));
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  z-index: var(--z-fixed);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-section .van-button {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .content {
    padding: var(--spacing-base) var(--spacing-base) calc(80px + env(safe-area-inset-bottom));
  }

  .quick-select-items {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-select-item {
    font-size: 12px;
    padding: var(--spacing-xs) var(--spacing-sm);
    min-height: 36px;
  }

  .submit-section {
    padding: var(--spacing-sm) var(--spacing-sm);
    padding-bottom: calc(var(--spacing-sm) + env(safe-area-inset-bottom));
  }

  .submit-section .van-button {
    max-width: 100%;
    font-size: var(--font-size-base);
  }
}

@media (min-width: 768px) {
  .content {
    max-width: 768px;
    margin: 0 auto;
  }

  .quick-select-items {
    grid-template-columns: repeat(3, 1fr);
  }

  .submit-section {
    padding: var(--spacing-lg) var(--spacing-xl);
  }

  .submit-section .van-button {
    max-width: 360px;
  }
}

/* 中等屏幕优化 */
@media (min-width: 376px) and (max-width: 767px) {
  .submit-section .van-button {
    max-width: 320px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .service-category {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .service-category,
  .service-item,
  .category-header {
    transition: none;
  }
}
</style>
