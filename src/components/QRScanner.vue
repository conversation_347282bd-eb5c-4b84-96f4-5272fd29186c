<template>
  <div class="qr-scanner">
    <!-- 扫码界面 -->
    <van-popup 
      v-model:show="visible" 
      position="center" 
      :style="{ width: '90%', height: '70%' }"
      round
      closeable
      @close="handleClose"
    >
      <div class="scanner-container">
        <div class="scanner-header">
          <h3>扫描二维码</h3>
          <p>请将二维码放入扫描框内</p>
        </div>
        
        <!-- 摄像头预览区域 -->
        <div class="camera-container">
          <video 
            ref="videoRef" 
            class="camera-video"
            autoplay 
            muted 
            playsinline
            v-show="cameraActive"
          ></video>
          
          <!-- 扫描框 -->
          <div class="scan-frame" v-show="cameraActive">
            <div class="scan-line"></div>
          </div>
          
          <!-- 加载状态 -->
          <div class="loading-state" v-show="!cameraActive && !error">
            <van-loading size="24px" />
            <p>正在启动摄像头...</p>
          </div>
          
          <!-- 错误状态 -->
          <div class="error-state" v-show="error">
            <van-icon name="warning-o" size="48" color="#ee0a24" />
            <p>{{ error }}</p>
            <van-button type="primary" size="small" @click="retryCamera">重试</van-button>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="scanner-actions">
          <van-button 
            type="default" 
            size="large" 
            @click="handleClose"
            block
          >
            取消扫码
          </van-button>
          <van-button 
            type="primary" 
            size="large" 
            @click="switchToManual"
            block
            style="margin-top: 12px;"
          >
            手动选择会议室
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { showToast } from 'vant'
import { BrowserQRCodeReader } from '@zxing/browser'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'scan-success', 'scan-error', 'switch-manual'])

const visible = ref(false)
const videoRef = ref(null)
const cameraActive = ref(false)
const error = ref('')
const codeReader = ref(null)
const stream = ref(null)

// 监听显示状态
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal) {
    startScanning()
  } else {
    stopScanning()
  }
})

// 启动扫码
const startScanning = async () => {
  try {
    error.value = ''
    cameraActive.value = false
    
    // 检查浏览器支持
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('当前浏览器不支持摄像头功能')
    }
    
    // 初始化二维码读取器
    codeReader.value = new BrowserQRCodeReader()
    
    // 获取摄像头权限，优化配置
    stream.value = await navigator.mediaDevices.getUserMedia({
      video: {
        facingMode: 'environment', // 优先使用后置摄像头
        width: { ideal: 1920, min: 640 },
        height: { ideal: 1080, min: 480 },
        frameRate: { ideal: 30, min: 15 },
        focusMode: 'continuous',
        exposureMode: 'continuous',
        whiteBalanceMode: 'continuous'
      }
    })
    
    // 设置视频流
    if (videoRef.value) {
      videoRef.value.srcObject = stream.value
      await videoRef.value.play()
      cameraActive.value = true
      
      // 开始扫码
      startDecoding()
    }
    
  } catch (err) {
    console.error('启动摄像头失败:', err)
    handleCameraError(err)
  }
}

// 开始解码
const startDecoding = async () => {
  try {
    if (!codeReader.value || !videoRef.value) return

    let scanAttempts = 0
    const maxAttempts = 3

    const result = await codeReader.value.decodeFromVideoDevice(
      undefined, // 使用默认摄像头
      videoRef.value,
      (result, error) => {
        if (result) {
          console.log('扫码成功，尝试次数:', scanAttempts + 1)
          handleScanSuccess(result.getText())
        } else if (error) {
          scanAttempts++
          // 每隔几次尝试输出调试信息
          if (scanAttempts % 30 === 0) {
            console.log(`扫码尝试中... (${scanAttempts}次)`)
          }

          // 如果连续失败太多次，提示用户调整角度
          if (scanAttempts > 150 && scanAttempts % 60 === 0) {
            showToast({
              type: 'warning',
              message: '请调整二维码角度和距离',
              duration: 1000
            })
          }
        }
      }
    )
  } catch (err) {
    console.error('扫码解码失败:', err)
    showToast({
      type: 'fail',
      message: '扫码功能启动失败，请重试'
    })
  }
}

// 扫码成功处理
const handleScanSuccess = (text) => {
  try {
    console.log('扫码原始内容:', text)

    let roomNumber = ''
    let building = ''
    let floor = ''

    // 预处理文本：去除空白字符和特殊字符
    const cleanText = text.trim().replace(/[\s\n\r\t]/g, '')
    console.log('清理后的内容:', cleanText)

    // 尝试多种解析方式
    if (cleanText.includes('roomNumber=') || cleanText.includes('meetingRoom=')) {
      // URL参数格式
      try {
        const url = new URL(cleanText)
        const params = new URLSearchParams(url.hash.split('?')[1] || url.search)
        roomNumber = params.get('roomNumber') || params.get('meetingRoom')
        building = params.get('building')
        floor = params.get('floor')
      } catch {
        // 如果URL解析失败，尝试正则提取参数
        const roomMatch = cleanText.match(/(?:roomNumber|meetingRoom)=([A-Z]+\d+)/i)
        const buildingMatch = cleanText.match(/building=([A-Z]+)/i)
        const floorMatch = cleanText.match(/floor=(\d+)/i)

        if (roomMatch) roomNumber = roomMatch[1]
        if (buildingMatch) building = buildingMatch[1]
        if (floorMatch) floor = floorMatch[1]
      }
    } else if (cleanText.match(/^[A-Z]+(\d+|-\d+)$/)) {
      // 直接是会议室编号格式，如 A0101, A11-27
      roomNumber = cleanText
      building = roomNumber.match(/^([A-Z]+)/)?.[1] || ''
    } else {
      // 尝试从文本中提取会议室编号（更宽松的匹配）
      const patterns = [
        /([A-Z]+\d{3,4})/g,  // A0101, B1234 等
        /([A-Z]+-\d{1,4})/g, // A11-27, B12-01 等（连字符格式）
        /([A-Z]+\d{2,3})/g,  // A01, B12 等
        /会议室[：:]?([A-Z]+(\d+|-\d+))/g,  // 会议室：A0101, A11-27
        /房间[：:]?([A-Z]+(\d+|-\d+))/g,    // 房间：A0101, A11-27
        /([A-Z]\d+)/g        // 简单格式 A1, B2
      ]

      for (const pattern of patterns) {
        const matches = [...cleanText.matchAll(pattern)]
        if (matches.length > 0) {
          roomNumber = matches[0][1]
          building = roomNumber.match(/^([A-Z]+)/)?.[1] || ''
          break
        }
      }
    }

    // 清理重复字符问题和格式化
    if (roomNumber) {
      // 检查是否有重复的楼栋字母，如 AA0101 -> A0101
      const cleanMatch = roomNumber.match(/^([A-Z])\1+(.*)$/)
      if (cleanMatch) {
        roomNumber = cleanMatch[1] + cleanMatch[2]
        building = cleanMatch[1]
      }

      // 确保格式正确（大写字母+数字）
      roomNumber = roomNumber.toUpperCase()

      // 从会议室编号中提取楼层信息（如果没有的话）
      if (!floor && roomNumber.length >= 4) {
        // 尝试多种楼层提取模式
        let floorMatch = null

        // 模式1: 连字符格式 A11-27 (A11楼栋 + 27房间)
        floorMatch = roomNumber.match(/^([A-Z]+\d+)-(\d+)$/)
        if (floorMatch) {
          // 对于连字符格式，楼栋部分可能包含楼层信息
          const buildingPart = floorMatch[1]
          const roomPart = floorMatch[2]

          // 尝试从楼栋部分提取楼层（如A11中的11）
          const buildingFloorMatch = buildingPart.match(/^[A-Z]+(\d+)$/)
          if (buildingFloorMatch) {
            floor = buildingFloorMatch[1]
          }
        } else {
          // 模式2: 标准格式 A0101 (A + 01楼 + 01房间)
          floorMatch = roomNumber.match(/^[A-Z](\d{2})\d{2}$/)
          if (floorMatch) {
            floor = parseInt(floorMatch[1]).toString()
          } else {
            // 模式3: 扩展格式 A1101 (A + 11楼 + 01房间) 或 A11开头的楼栋
            floorMatch = roomNumber.match(/^[A-Z]+(\d{1,2})\d{2}$/)
            if (floorMatch) {
              floor = parseInt(floorMatch[1]).toString()
            }
          }
        }
      }

      console.log('解析后的会议室编号:', roomNumber)
      console.log('楼栋:', building, '楼层:', floor)

      showToast({
        type: 'success',
        message: `扫码成功！获取到会议室：${roomNumber}`
      })

      emit('scan-success', {
        roomNumber,
        building,
        floor,
        url: text
      })

      handleClose()
    } else {
      throw new Error('无法识别会议室编号')
    }
  } catch (err) {
    console.error('解析二维码失败:', err)
    console.error('原始内容:', text)

    // 提供更友好的错误提示
    let errorMessage = '二维码格式不正确'
    if (text.length > 200) {
      errorMessage = '二维码内容过长，请确认是会议室二维码'
    } else if (text.length < 3) {
      errorMessage = '二维码内容过短，请重新扫描'
    } else if (!text.match(/[A-Z]/)) {
      errorMessage = '未找到会议室编号，请扫描正确的会议室二维码'
    }

    showToast({
      type: 'fail',
      message: errorMessage,
      duration: 2000
    })

    // 不立即关闭，让用户可以重试
    setTimeout(() => {
      if (cameraActive.value) {
        showToast({
          type: 'info',
          message: '请重新对准二维码扫描',
          duration: 1500
        })
      }
    }, 2500)
  }
}

// 处理摄像头错误
const handleCameraError = (err) => {
  let errorMessage = '启动摄像头失败'
  
  if (err.name === 'NotAllowedError') {
    errorMessage = '请允许访问摄像头权限'
  } else if (err.name === 'NotFoundError') {
    errorMessage = '未找到摄像头设备'
  } else if (err.name === 'NotSupportedError') {
    errorMessage = '当前浏览器不支持摄像头功能'
  } else if (err.message) {
    errorMessage = err.message
  }
  
  error.value = errorMessage
  emit('scan-error', errorMessage)
}

// 重试摄像头
const retryCamera = () => {
  startScanning()
}

// 切换到手动选择
const switchToManual = () => {
  emit('switch-manual')
  handleClose()
}

// 停止扫码
const stopScanning = () => {
  try {
    // 停止二维码读取器
    if (codeReader.value) {
      codeReader.value.reset()
      codeReader.value = null
    }
    
    // 停止视频流
    if (stream.value) {
      stream.value.getTracks().forEach(track => track.stop())
      stream.value = null
    }
    
    // 清空视频元素
    if (videoRef.value) {
      videoRef.value.srcObject = null
    }
    
    cameraActive.value = false
    error.value = ''
  } catch (err) {
    console.error('停止扫码失败:', err)
  }
}

// 关闭扫码器
const handleClose = () => {
  stopScanning()
  emit('update:show', false)
}

// 组件卸载时清理
onUnmounted(() => {
  stopScanning()
})
</script>

<style scoped>
.scanner-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.scanner-header {
  text-align: center;
  margin-bottom: 20px;
}

.scanner-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.scanner-header p {
  font-size: 14px;
  color: #969799;
}

.camera-container {
  flex: 1;
  position: relative;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid #1989fa;
  border-radius: 12px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1989fa, transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% { transform: translateY(0); }
  100% { transform: translateY(196px); }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.loading-state p,
.error-state p {
  margin: 12px 0;
  font-size: 14px;
}

.scanner-actions {
  margin-top: 20px;
}
</style>
